spring:
    application:
        name: Mind-Body-Warrior
    mvc:
        pathmatch:
            matching-strategy: PATH_PATTERN_PARSER
    servlet:
        multipart:
            enabled: true
            max-file-size: 30MB
            max-request-size: 30MB
    mail:
        host: smtp.gmail.com
        port: 587
        username: <EMAIL>
        password: lhspbuabaqvnmhbi

        properties:
            mail:
                smtp:
                    ssl:
                        trust: "*"
                    auth: true
                    starttls:
                        enable: true
                    connectiontimeout: 5000
                    timeout: 3000
                    writetimeout: 5000
    quartz:
        job-store-type: jdbc
        jdbc:
            initialize-schema: always
        properties:
            org:
                quartz:
                    jobStore:
                        isClustered: false  # Optional: For clustered environments
                        useProperties: false
                        driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
        scheduler:
            instance-id: AUTO
            auto-startup: true
            startupDelay: 10
app:
    jwt:
        secret: JeghtBvyUodklSapmAcvsjkfbpS111jfri$544jng%50fdnkn69040034nsdf#$FMFFMk4004
        expiration: 604800000


reset:
    passwordUrl: https://mind-body-warrior-cms.openxcell.dev/reset-password

logging:
    level:
        reactor:
            netty:
                http:
                    client: DEBUG
        org:
            springframework:
                web:
                    reactive:
                        function:
                            client: DEBUG

