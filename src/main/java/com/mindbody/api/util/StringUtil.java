package com.mindbody.api.util;


import com.amazonaws.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Predicate;


public class StringUtil {


    public static Function<String, String> funTrim = String::trim;
    public static Predicate<String> predicateIsEmpty = s -> s.isEmpty();
    public static Predicate<String> predicateNull = s -> s == null;

    public static boolean nullOrEmpty(String strObj) {

        return (Objects.isNull(strObj) || strObj.trim().isEmpty());
    }


    public static boolean nonNullNonEmpty(String strObj) {

        return (Objects.nonNull(strObj) && !strObj.trim().isEmpty());
    }

    public static String valueOf(Object var0) {
        return var0 == null ? null : var0.toString();
    }

    public static String decodeBase64(String message) throws IllegalArgumentException {
        try {

            return new String(
                    Base64.getDecoder().decode(message.getBytes(StandardCharsets.UTF_8)),
                    StandardCharsets.UTF_8);
        } catch (Exception ignored) {
            return null;
        }

    }

    public static String encodeBase64(String message) throws IllegalArgumentException {
        try {
            return new String(
                    Base64.getEncoder().encode(message.getBytes(StandardCharsets.UTF_8)),
                    StandardCharsets.UTF_8);
        } catch (Exception ignored) {
            return null;
        }

    }

    public static String urldecodeBase64(String message) throws IllegalArgumentException {
        try {

            return new String(
                    Base64.getUrlDecoder().decode(message.getBytes(StandardCharsets.UTF_8)),
                    StandardCharsets.UTF_8);
        } catch (Exception ignored) {
            return null;
        }

    }

    public static String decodeURL(String message) {
        try {
            if (message.endsWith("=")) return message;
            return URLDecoder.decode(message, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }

    public static String escapeDataFromES(String str) {
        final String[] metaCharacters = {"\\", "/", "^", "$", "{", "}", "[", "]", "(", ")", ".", "*", "+", "?", "!", "|", "<", ">", "-", "&", "%", " ", "@", "#", "'", " "};

        str = checkStringContain(str, metaCharacters);
        return str;
    }

    private static String checkStringContain(String str, String[] metaCharacters) {
        for (String metaCharacter : metaCharacters) {
            if (str.contains(metaCharacter)) {
                str = str.replace(metaCharacter, "\\" + metaCharacter);
            }
        }
        return str;
    }

    public static String getRandomString(int n) {

        // choose a Character random from this String
        String AlphaNumericString = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
                + "0123456789"
                + "abcdefghijklmnopqrstuvxyz";

        // create StringBuffer size of AlphaNumericString
        StringBuilder sb = new StringBuilder(n);

        for (int i = 0; i < n; i++) {

            // generate a random number between
            // 0 to AlphaNumericString variable length
            int index
                    = (int) (AlphaNumericString.length()
                    * Math.random());

            // add Character one by one in end of sb
            sb.append(AlphaNumericString
                    .charAt(index));
        }

        return sb.toString();
    }

    public static String escapeData(String str) {
        String regex = "[\\[+\\]+:{}^~?\\\\/()><=\"!9-&%@#'$|.,]";
        str = StringUtils.replace(str, regex, "\\\\$0");
        return str;
    }

}
