package com.mindbody.api.util;


public class Urls {

    public static final String BASE_PATH = "/mindbody/api";

    public static final String BASE_PATH_CMS = "/mindbody/api/cms";
    public static final String BASE_PATH_AUTH = "/mindbody/api/auth";
    public static final String LOGIN = "/login";
    public static final String LOG_OUT = "/logout";
    public static final String CHANGE_PASSWORD = "/change-password";

    public static final String SEND_FORGOT_PASSWORD_LINK = "/send-forgot-password-link";

    public static final String RESET_PASSWORD = "/reset-password";

    public static final String RESET_PASSWORD_USER = "/reset-password-user";


    public static final String VIEW_ADMIN_PROFILE = "/view-admin-profile";
    public static final String DASHBOARD = "/dashboard";
    public static final String USER = "/user";
    public static final String CMS = "/cms";
    public static final String ADMIN = "/admin";
    public static final String EDIT_PROFILE = "/edit-profile";
    public static final String SIGN_UP = "/signup";
    public static final String FEEDBACK = "/feedback";
    public static final String SUBMIT = "/submit";

    public static final String QUESTION= "/question";

    public static final String USER_MANAGEMENT = "/user-management";

    public static final String ADD = "/add";
    public static final String EDIT = "/edit";
    public static final String LIST= "/list";

    public static final String VIEW = "/view";

    public static final String ACTIVE_INACTIVE = "/active-inactive";
    public static final String DEACTIVATE = "/deactivate";
    public static final String DELETE = "/delete";
    public static final String ANSWER = "/answer";
    public static final String SEND_OTP = "/send-otp";
    public static final String VERIFY_OTP = "/verify-otp";
    public static final String SOCIAL_MEDIA = "/social-media";
    public static final String STREAK = "/streak";

    public static final String MAGAZINE = "/magazine";
    public static final String SOUND = "/sound";
    public static final String NOTIFICATION = "/notification";
    public static final String SEND_NOTIFICATION = "/send-notification";

    public static final String DAILY_TIPS = "/daily-tips";
    public static final String HOME_SCREEN = "/home-screen";
    public static final String CHANGE_ORDER = "/change-order";

    public static final String PRODUCT_CATEGORY = "/product-category";

    public static final String AUDIO_PLAYLIST = "/audio-playlist";

    public static final String WORKOUT_PLAYLIST = "/workout-playlist";

    public static final String WORKOUT_PLAN = "/workout-plan";
    public static final String AUDIO = "/audio";
    public static final String REWARD = "/reward";
    public static final String RANDOM = "/random";
    public static final String FAVORITE = "/favorite";
    public static final String EXCLUSIVE = "/exclusive";
    public static final String EXERCISE = "/exercise";
    public static final String HEALTH = "/health";
    public static final String CALCULATE = "/calculate";
    public static final String FILE_UPLOAD = "/file-upload";
    public static final String PRE_SIGNED_URLS     = "/pre-signed-urls";
    public static final String COMPLETE = "/complete";
    public static final String ABORT = "/abort";

    public static final String STATIC_PAGE = "/static-page";

    public static final String PLANETS = "/planets";
    public static final String TROPICAL = "/tropical";
    public static final String TIMEZONE_WITH_DST = "/timezone_with_dst";
    public static final String NATAL_WHEEL_CHART = "/natal_wheel_chart";
    public static final String GENERAL_SIGN_REPORT = "/general_sign_report";
    public static final String ASTROLOGY = "/astrology";
    public static final String GENERATE = "/generate";
    public static final String SUN_SIGN_PREDICTION = "/sun_sign_prediction";
    public static final String HOROSCOPE_PREDICTION = "/horoscope_prediction";
    public static final String DAILY = "/daily";
    public static final String MONTHLY = "/monthly";
    public static final String GENERATE_DAILY_HOROSCOPE = "/generate-daily-horoscope";
    public static final String GENERATE_MONTHLY_HOROSCOPE = "/generate-monthly-horoscope";
    public static final String SHOPIFY = "/shopify";
    public static final String PRODUCT = "/product";



    public static final String CUSTOM_COLLECTION_JSON ="/custom_collections.json";
    public static final String CATEGORY = "/category";
    public static final String PRODUCTS_JSON = "/products.json";
    public static final String CART = "/cart";
    public static final String ORDERS = "/orders";
    public static final String SHOPIFY_ADMIN_ORDERS_LIST = "/shopify-admin-orders-list";
    public static final String API = "/api";
    public static final String GRAPHQL_JSON = "/graphql.json";
    public static final String ORDERS_JSON = "/orders.json";
    public static final String CREATE = "/create";
    public static final String CHECKOUT = "/checkout";
    public static final String WEBHOOK = "/webhook";
    public static final String CUSTOMERS_CREATE = "/customers/create";
    public static final String ORDERS_CREATE = "/orders/create";
    public static final String ORDERS_PAID = "/orders/paid";
    public static final String ORDERS_PAYMENT = "/orders/payment";
    public static final String ORDERS_FULFILLMENT = "/orders/fulfillment";
    public static final String TRACK_ORDER = "/track-order";
    public static final String ADD_SHIPPING_ADDRESS ="/add-shipping-address";

    public static final String ACHIEVEMENT_REWARDS = "/achievement-rewards";

    public static final String ACHIEVEMENTS = "/achievements";

    public static final String ACHIEVEMENT_TITLE = "/title";

    public static final String ACHIEVEMENT_BORDER = "/border";

    public static final String ACHIEVEMENT_MEDAL = "/medal";

    public static final String GET_ACHIEVEMENT_SUB_CATEGORY_LIST_FOR_CATEGORY_SELECTED_BY_USER = "/get-achievement-sub-category-list-for-category-selected-by-user";

    public static final String GET_ACHIEVEMENTS_COUNT_FOR_ALL_CATEGORIES = "/get-achievements-count-for-all-categories";

    public static final String ID = "/id";

    public static final String VARIANT = "/variant";

    public static final String GET_ACHIEVEMENTS_BY_SET_NAME = "/get-achievements-by-set-name";

    public static final String USER_ACHIEVEMENTS = "/user-achievements";

    public static final String SUBMIT_USER_ACHIEVEMENT = "/submit-user-achievement";

    public static final String CLAIM_USER_ACHIEVEMENT = "/claim-user-achievement";

    public static final String GET_USER_TITLES = "/get-user-titles";

    public static final String GET_USER_ACHIEVEMENT = "/get-user-achievement";

    public static final String GET_USER_BADGES = "/get-user-badges";

    public static final String GET_USER_BORDERS = "/get-user-borders";

    public static final String VIEW_USER_ACHIEVEMENTS = "/view-user-achievements";

//    public static final String UPDATE_REACTIVATION_REQUEST_PENDING_STATUS = "/update-reactivation-request-pending-status";

    public static final String USER_REQUEST_MANAGEMENT = "/user-request-management";

    public static final String USER_IDS = "/user-ids";

    public static final String SUBSCRIBE_USER_FOR_TOPIC = "/subscribe-user-for-topic";


    public static final String REACTIVATE = "/re-activate";

    public static final String USER_CURRENT_CONNECTION = "/user-current-connection";

    public static final String USER_REFERRAL = "/user-referral";

    public static final String SUBMIT_USER_REFERRAL = "/submit-user-referral";

    public static final String LIST_USER_REFERRAL = "/list-user-referral";

    public static final String SUBMIT_CATEGORY_TIME_SPENT = "/submit-category-time-spent";

    public static final String LEADERBOARD = "/leaderboard";

    public static final String USER_STATS = "/user-stats";

    public static final String LEADERBOARD_PIN_USER = "/leaderboard-pin-user";

    public static final String LEADERBOARD_UNPIN_USER = "/leaderboard-unpin-user";

    public static final String LEADERBOARD_VIEW_PROFILE = "/leaderboard-view-profile";

    public static final String AI_CHAT = "/ai-chat";

    public static final String VIEW_CATEGORY_DETAILS = "/view-category-details";

    public static final String MARK_AI_CHAT_MESSAGES_READ = "/mark-ai-chat-messages-read";

    public static final String REFERRAL = "/referral";

    public static final String THEME_MODE = "/theme-mode";

    public static final String MEAL_PLAN = "/meal-plan";

    public static final String CALCULATE_METRICS = "/calculate-metrics";


}

