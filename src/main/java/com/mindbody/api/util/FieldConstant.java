package com.mindbody.api.util;

public class FieldConstant {

    public static final String DATE_24_HR_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_PATTERN = "MM-dd-yyyy";
    public static final String COMMON_DATE_24_HR_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    public static final String ROLE = "role";
    public static final String UTC_TIMEZONE = "UTC";
    public static final long TWENTY_FOUR_HOUR_MILLISECOND = 86400000;
    public static final String EMAIL = "email";
    public static final String COUNTRY_CODE = "countryCode";
    public static final String PHONE_NUMBER = "phoneNumber";
    public static final String LOGIN_TYPE = "loginType";

    public static final class CollectionName {

        public static final String COLLECTION_USER = "user";
        public static final String COLLECTION_ADMIN = "admin";
        public static final String FEEDBACK = "feedback";

        public static final String COLLECTION_EMAIL_VERIFICATION_TOKEN = "email_verification_token";
        public static final String COLLECTION_ACCESS_TOKEN = "access_token";
        public static final String COLLECTION_USER_INFO = "user_info";
        public static final String COLLECTION_QUESTION = "question";


        public static final String COLLECTION_QUESTION_OPTION = "question_option";
        public static final String COLLECTION_USER_ANSWER = "user_answer";
        public static final String COLLECTION_USER_ASTROLOGY_INFO = "user_astrology_info";
        public static final String OTP = "otp";
        public static final String FCM_TOKEN = "fcm_token";
        public static final String COLLECTION_USER_STREAK = "user_streak";

        public static final String COLLECTION_MAGAZINE = "magazine";
        public static final String DAILY_TIPS = "daily_tips";
        public static final String AUDIO_PLAYLIST = "audio_playlist";
        public static final String WORKOUT_PLAYLIST = "workout_playlist";
        public static final String WORKOUT_PLAN = "workout_plan";
        public static final String WORKOUT_PLAN_EXERCISE = "workout_plan_exercise";

        public static final String WORKOUT_PLAN_ZODIAC_SIGN = "workout_plan_zodiac_sign";
        public static final String AUDIO = "audio";
        public static final String EXCLUSIVE_AUDIO = "exclusive_audio";
        public static final String AUDIO_ZODIAC_SIGN = "audio_zodiac_sign";
        public static final String COLLECTION_FAVORITE_AUDIO = "favoriteAudio";
        public static final String EXERCISE = "exercise";
        public static final String RANDOM_MEDIA_TRACKER = "random_media_tracker";
        public static final String COLLECTION_USER_HEALTH_INFO = "user_health_info";
        public static final String COLLECTION_FAVORITE_WORKOUT_PLAN = "favorite_workout_plan";
        public static final String PRODUCT_CATEGORY = "product_category";
        public static final String STATIC_PAGE = "static_page";
        public static final String SOUND = "sound";
        public static final String NOTIFICATION = "notification";
        public static final String NOTIFICATION_IMAGE = "notification_image";
        public static final String NOTIFICATION_FILTERS = "notification_filters";

        public static final String ACHIEVEMENT_MEDALS = "achievement_medals";

        public static final String ACHIEVEMENT_REWARDS = "achievement_rewards";

        public static final String ACHIEVEMENT_LEVEL_UP = "achievement_level_up";

        public static final String ACHIEVEMENT_BORDER = "achievement_border";

        public static final String ACHIEVEMENT_TITLE = "achievement_title";

        public static final String WARRIOR_SET = "warrior_set";

        public static final String ACHIEVEMENTS = "achievements";

        public static final String USER_ACHIEVEMENTS = "user_achievements";


        public static final String COLLECTION_USER_CART = "user_cart";

        public static final String SHOPIFY_CUSTOMERS = "shopify_customers";

        public static final String USER_POINTS_TRACKER = "user_points_tracker";

        public static final String USER_TOPIC_SUBSCRIPTIONS = "user_topic_subscriptions";

        public static final String NOTIFICATION_SCHEDULE = "notificationSchedule";

        public static final String CHAT_MESSAGES = "chatMessages";
        public static final String COLLECTION_USER_REFERRAL = "user_referral";

        public static final String CATEGORY_USER_STATS = "user_stats";

        public static final String PINNED_USER_MAPPING = "pinned_user_mapping";

        public static final String USER_UNLOCKED_AUDIO = "user_unlocked_audio";

        public static final String USER_DEVICE = "user_device";

        public static final String USER_REDEEMED_AUDIO = "user_redeemed_audio";
    }

    public static final class EmailTemplateName {
        public static final String TEMPLATE_FORGOT_PASSWORD_LINK = "forgotPasswordLink.ftl";
        public static final String TEMPLATE_ACCOUNT_VERIFICATION_OTP = "accountVerificationOtp.ftl";
        public static final String TEMPLATE_RESET_PASSWORD_OTP = "resetPasswordOTP.ftl";
        public static final String APPROVE_USER_ACCOUNT_ACTIVATION_REQUEST = "approveUserAccountActivationRequest.ftl";
        public static final String REJECT_USER_ACCOUNT_ACTIVATION_REQUEST = "rejectUserAccountActivationRequest.ftl";
        public static final String TEMPLATE_USER_FEEDBACK = "userFeedback.ftl";
    }


    public static final class Flags {
        public static final String IS_DELETED = "isDeleted";
        public static final String IS_ACTIVE = "isActive";
    }

    public static final class Astrology {

        public static final String PLACIDUS = "placidus";

        public static final String INNER_CIRCLE_BACKGROUND = "#ffffff";

        public static final String PLANET_ICON_COLOR = "#D9AC6D";

        public static final String SIGN_ICON_COLOR = "#ffffff";

        public static final String SIGN_BACKGROUND = "#D9AC6D";

        public static final String CHART_SIZE = "350";

        public static final String IMAGE_TYPE = "png";

    }

    public static final class Shopify {

        public static final String LIMIT = "limit";

        public static final String PAGE_INFO = "page_info";

        public static final String LINK = "Link";

        public static final String COLLECTION_ID = "collection_id";

        public static final String TITLE = "title";


    }


}
