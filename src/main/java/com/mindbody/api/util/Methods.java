package com.mindbody.api.util;

import com.google.common.base.CaseFormat;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.exception.BusinessValidationException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.awt.*;
import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.ZoneId;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Methods {
    private static final String HTML_PATTERN = "<(\"[^\"]*\"|'[^']*'|[^'\">])*>";
    private static final Pattern pattern = Pattern.compile(HTML_PATTERN);
    static Random random = new Random();
    private static final String CHARACTERS = "ABCDEFGHJKLMNOPQRSTUVWXYZ1234567890";

    public static boolean hasHTMLTags(String text) {
        Matcher matcher = pattern.matcher(text);
        return matcher.find();
    }

    public static long generateCustomId() {
        random.setSeed(System.currentTimeMillis());
        return 100000 + random.nextInt(900000);
    }

    public static String generateCustomColor() {
        Color your_color = generateRandomColour().darker();
        String hex = "#" + Integer.toHexString(your_color.getRGB()).substring(2);
        if (hex.equals("#9aa1a9") || hex.equals("#e91e63")) {
            generateRandomColour().darker();

        }
        return hex;
    }

    public static final Color generateRandomColour() {

        return Color.getHSBColor(new Random().nextFloat(),
                new Random().nextFloat(), new Random().nextFloat());
    }

    public static double ParseDouble(String strNumber) {
        if (strNumber != null && strNumber.length() > 0) {
            try {
                return Double.parseDouble(strNumber);
            } catch (Exception e) {
                return -1;   // or some value to mark this field is wrong. or make a function validates field first ...
            }
        } else return 0;
    }

    public static double roundOff(double invoiceAmount) {
        return Math.round(invoiceAmount * 100.0) / 100.0;
    }

    public static String getFileExtension(String fileName) {
        if (fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0)
            return fileName.substring(fileName.lastIndexOf(".") + 1);
        else return "";
    }

    public static String getFileName(String fileName) {
        if (fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0)
            return fileName.substring(0, fileName.lastIndexOf("."));
        else return "";
    }

    public static void copyNonNullProperties(Object src, Object target) {
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }

    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    public static String getInternalValue(String fieldName) {
        String replaceFieldName = fieldName.replaceAll("[^a-zA-Z0-9]", "_");
        return CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, replaceFieldName);
    }

    public static String getEmailDomain(String email) {
        if (!StringUtil.nullOrEmpty(email)) {
            String pattern = "(?<=@)[^.]+(?=\\.)";
            Pattern r = Pattern.compile(pattern);
            Matcher regexMatcher = r.matcher(email);
            String accountName = null;
            if (regexMatcher.find()) {
                if ((email.indexOf("@gmail.com", email.length() - "@gmail.com".length()) != -1) || (email.indexOf("@hotmail.com", email.length() - "@hotmail.com".length()) != -1) || (email.indexOf("@yahoo.com", email.length() - "@yahoo.com".length()) != -1)) {
                    accountName = "";
                } else {
                    accountName = regexMatcher.group();
                }
            }
            return accountName;
        }
        return "";
    }

    public static <E extends Enum<E>> boolean isInEnum(String value, Class<E> enumClass) {
        for (E e : enumClass.getEnumConstants()) {
            if (e.name().equals(value)) {
                return true;
            }
        }
        return false;
    }


    public static String generateRandomPassword() {
        final char[] lowercase = "abcdefghijklmnopqrstuvwxyz".toCharArray();
        final char[] uppercase = "ABCDEFGHIJKLMNPOPQRSTUVWXYZ".toCharArray();
        final char[] numbers = "**********".toCharArray();
        final char[] symbols = "^$?!@#%&".toCharArray();
        final char[] allAllowed = "abcdefghijklmnopqrstuvwxyzABCDEFGJKLMNPRSTUVWXYZ**********^$?!@#%&".toCharArray();

        //Use cryptographically secure random number generator
        Random random = new SecureRandom();

        StringBuilder password = new StringBuilder();
        int length = 8;
        for (int i = 0; i < length - 4; i++) {
            password.append(allAllowed[random.nextInt(allAllowed.length)]);
        }

        //Ensure password policy is met by inserting required random chars in random positions
        password.insert(random.nextInt(password.length()), lowercase[random.nextInt(lowercase.length)]);
        password.insert(random.nextInt(password.length()), uppercase[random.nextInt(uppercase.length)]);
        password.insert(random.nextInt(password.length()), numbers[random.nextInt(numbers.length)]);
        password.insert(random.nextInt(password.length()), symbols[random.nextInt(symbols.length)]);

        return password.toString();
    }


    public static boolean isValidEmail(String email) {
        String emailRegex = "^[a-zA-Z0-9_+&*-]+(?:\\." +
                "[a-zA-Z0-9_+&*-]+)*@" +
                "(?:[a-zA-Z0-9-]+\\.)+[a-z" +
                "A-Z]{2,7}$";

        Pattern pat = Pattern.compile(emailRegex);
        if (email == null)
            return false;
        return pat.matcher(email).matches();
    }

    public static boolean isValidPhoneNumber(String phoneNumber) {
        String emailRegex = "^\\(?([0-9]{3})\\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$";

        Pattern pat = Pattern.compile(emailRegex);
        if (phoneNumber == null)
            return false;
        return pat.matcher(phoneNumber).matches();
    }

    public static String generateOTP() {
        random.setSeed(System.currentTimeMillis());
        return String.valueOf((long) (1000 + random.nextInt(9000)));
    }


    public static String setStringLength(String str) {
        if (StringUtil.nonNullNonEmpty(str)) {
            if (str.length() > 100)
                return str.substring(0, 100);
            else
                return str;
        }
        return "";
    }

    public static String digitFormating(long number, int requiredLength) {
        int length = (int) (Math.log10(number) + 1);
        if (length == requiredLength) {
            return String.valueOf(number);
        } else {
            return StringUtils.leftPad(String.valueOf(number), requiredLength, "0");
        }
    }

    public static String getRandomUUID() {
        return UUID.randomUUID().toString();
    }


    public static ZodiacSignType getZodiacSign(LocalDateTime dateOfBirth, String timezone) {
        try {
            if (dateOfBirth == null) {
                throw new BusinessValidationException("Date of birth cannot be null");
            }
            if (timezone == null || timezone.isEmpty()) {
                throw new BusinessValidationException("Timezone cannot be null or empty");
            }
            ZoneId userTimeZone = ZoneId.of(timezone);
            /** Convert UTC to the user's local time zone*/
            LocalDateTime userTimezoneDate = dateOfBirth.atZone(ZoneId.of(FieldConstant.UTC_TIMEZONE)).withZoneSameInstant(userTimeZone).toLocalDateTime();
            int day = userTimezoneDate.getDayOfMonth();
            Month month = userTimezoneDate.getMonth();
            return switch (month) {
                case JANUARY -> (day < 20) ? ZodiacSignType.Capricorn : ZodiacSignType.Aquarius;
                case FEBRUARY -> (day < 19) ? ZodiacSignType.Aquarius : ZodiacSignType.Pisces;
                case MARCH -> (day < 21) ? ZodiacSignType.Pisces : ZodiacSignType.Aries;
                case APRIL -> (day < 20) ? ZodiacSignType.Aries : ZodiacSignType.Taurus;
                case MAY -> (day < 21) ? ZodiacSignType.Taurus : ZodiacSignType.Gemini;
                case JUNE -> (day < 21) ? ZodiacSignType.Gemini : ZodiacSignType.Cancer;
                case JULY -> (day < 23) ? ZodiacSignType.Cancer : ZodiacSignType.Leo;
                case AUGUST -> (day < 23) ? ZodiacSignType.Leo : ZodiacSignType.Virgo;
                case SEPTEMBER -> (day < 23) ? ZodiacSignType.Virgo : ZodiacSignType.Libra;
                case OCTOBER -> (day < 23) ? ZodiacSignType.Libra : ZodiacSignType.Scorpio;
                case NOVEMBER -> (day < 22) ? ZodiacSignType.Scorpio : ZodiacSignType.Sagittarius;
                case DECEMBER -> (day < 22) ? ZodiacSignType.Sagittarius : ZodiacSignType.Capricorn;
                default -> throw new IllegalArgumentException("Invalid month: " + month);
            };
        } catch (Exception e) {
            throw new BusinessValidationException("Error while calculating zodiac sign");
        }
    }

    public static LocalDateTime convertUtcToUserTimeZone(LocalDateTime dateOfBirth, String timezone) {
        ZoneId userTimeZone = ZoneId.of(timezone);
        return dateOfBirth.atZone(ZoneId.of(FieldConstant.UTC_TIMEZONE))
                .withZoneSameInstant(userTimeZone)
                .toLocalDateTime();
    }

    public static String generateReferralCode() {
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder(9);
        for (int i = 0; i < 9; i++) {
            sb.append(random.nextInt(10)); // Generates digits 0-9
        }
        return sb.toString();
    }

    public static String generateUniqueReferralCode(List<String> existingCodes) {
        String code;
        do {
            code = generateReferralCode();
        } while (existingCodes.contains(code));
        return code;
    }

    private static String getRandomCharacters(int length) {
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }
        return sb.toString();
    }


}
