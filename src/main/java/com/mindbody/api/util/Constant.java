package com.mindbody.api.util;


public class Constant {

    public static final int SUCCESS = 1;
    public static final int FAIL = 0;
    public static final int WARN = 2;
    public static final String API_KEY = "X-API-KEY";
    public static final String ACCESS_TOKEN = "accessToken";
    public static final String SECRET_TOKEN = "secretToken";
    public static final String TOKEN = "token";
    public static final int MIN_FILE_SIZE = 10000;//file size in byte
    public static final String TIMEZONE = "timezone";
    public static final String DATA = "data";
    public static final String X_SHOPIFY_ACCESS_TOKEN ="X-Shopify-Access-Token";
    public static final String X_SHOPIFY_STOREFRONT_ACCESS_TOKEN ="X-Shopify-Storefront-Access-Token";
    public static final String CURRENT_LEVEL = "currentLevel";
    public static final String CURRENT_LEVEL_WXP_POINT = "currentLevelPoint";
    public static final String NEXT_LEVEL = "nextLevel";
    public static final String NEXT_LEVEL_WXP_POINT = "nextLevelPoint";
    public static final String ACHIEVEMENT_UNLOCKED = "Achievement Unlocked.!!";
    public static final String SYSTEM = "system";

}