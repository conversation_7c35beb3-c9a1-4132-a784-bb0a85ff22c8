package com.mindbody.api.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

@Component
@Slf4j
public class TransactionUtil {

    /**
     * Executes a supplier in a read-only transaction
     * @param supplier The operation to execute
     * @param <T> The return type
     * @return The result of the operation
     */
    @Transactional(readOnly = true)
    public <T> T executeReadOnly(Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            log.error("Error in read-only transaction", e);
            throw e;
        }
    }

    /**
     * Executes a runnable in a read-only transaction
     * @param runnable The operation to execute
     */
    @Transactional(readOnly = true)
    public void executeReadOnly(Runnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            log.error("Error in read-only transaction", e);
            throw e;
        }
    }

    /**
     * Executes a supplier in a write transaction with timeout
     * @param supplier The operation to execute
     * @param timeoutSeconds Timeout in seconds
     * @param <T> The return type
     * @return The result of the operation
     */
    @Transactional(timeout = 30)
    public <T> T executeWithTimeout(Supplier<T> supplier, int timeoutSeconds) {
        try {
            return supplier.get();
        } catch (Exception e) {
            log.error("Error in transaction with timeout {} seconds", timeoutSeconds, e);
            throw e;
        }
    }

    /**
     * Executes a runnable in a write transaction with timeout
     * @param runnable The operation to execute
     * @param timeoutSeconds Timeout in seconds
     */
    @Transactional(timeout = 30)
    public void executeWithTimeout(Runnable runnable, int timeoutSeconds) {
        try {
            runnable.run();
        } catch (Exception e) {
            log.error("Error in transaction with timeout {} seconds", timeoutSeconds, e);
            throw e;
        }
    }
} 