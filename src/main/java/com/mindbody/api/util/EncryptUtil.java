package com.mindbody.api.util;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.Base64;
import java.util.Objects;

public class EncryptUtil {

    private static SecretKeySpec secretKey;
    private static byte[] key;
    private static final String ALGORITHM = "AES";
    private static final String secret = "Mindfj344?#$%6458_)$^3dfn@#$9Gdskfb4$@49#$56c";

    public static String encryptKey(String strToEncrypt) {
        try {
            if (strToEncrypt != null) {
                key = secret.getBytes(StandardCharsets.UTF_8);
                MessageDigest sha = MessageDigest.getInstance("SHA-1");
                key = sha.digest(key);
                key = Arrays.copyOf(key, 16);
                secretKey = new SecretKeySpec(key, ALGORITHM);
                Cipher cipher = Cipher.getInstance(ALGORITHM);
                cipher.init(Cipher.ENCRYPT_MODE, secretKey);
                return Base64.getEncoder().encodeToString(cipher.doFinal(strToEncrypt.getBytes("UTF-8")));
            } else {
                throw new RuntimeException("key not found for encryption");
            }
        } catch (Exception e) {
            System.out.println("Error while encrypting: " + e.toString());
        }
        return null;
    }


    public static String decryptKey(String strToDecrypt) {
        try {
            if (strToDecrypt != null) {
                key = secret.getBytes(StandardCharsets.UTF_8);
                MessageDigest sha = MessageDigest.getInstance("SHA-1");
                key = sha.digest(key);
                key = Arrays.copyOf(key, 16);
                secretKey = new SecretKeySpec(key, ALGORITHM);
                Cipher cipher = Cipher.getInstance(ALGORITHM);
                cipher.init(Cipher.DECRYPT_MODE, secretKey);
                return new String(cipher.doFinal(Base64.getDecoder().decode(strToDecrypt)));
            } else {
                throw new RuntimeException("key not found for decryption");
            }
        } catch (Exception e) {
            System.out.println("Error while decrypting: " + e.toString());
        }
        return null;
    }

    public static String decodeUrl(String token) {
        String decodedToken = URLDecoder.decode(token);
        String tokenId = decryptKey(decodedToken);
        if (Objects.isNull(tokenId)) {
            tokenId = decryptKey(token);
        }
        return tokenId;
    }

    public static String encodeUrl(String token) {
        String encryptedToken = null;
        if (StringUtil.nonNullNonEmpty(token)) {
            try {
                encryptedToken = URLEncoder.encode(encryptKey(token), "UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        }
        return encryptedToken;
    }
}
