package com.mindbody.api.logging;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_EMPTY)
public class LogHolder {

    private final Map<String, Object> attributes;
    @JsonIgnore
    private final StringJoiner buildMessage;
    private Long startTime;
    private Long endTime;
    @JsonIgnore
    private String action;
    @JsonIgnore
    private String message;

    public LogHolder() {
        attributes = new HashMap<>();
        buildMessage = new StringJoiner(",\n");
    }

    public Object get(String key) {
        return attributes.get(key);
    }

    public Long executionStartTime() {
        if (Objects.isNull(startTime)) {
            startTime = currentTimeMillis();
        }
        return startTime;
    }

    private Long currentTimeMillis() {
        return System.currentTimeMillis();
    }

    public Long executionEndTime() {
        if (Objects.isNull(endTime)) {
            endTime = currentTimeMillis();
            attributes.put(LogKeys.ELAPSE_TIME, endTime - startTime);
        }
        return endTime;
    }

    public Map<String, Object> getAttributes() {
        return attributes;
    }

    public void setAction(String action) {
        put(LogKeys.LogEvent.ACTION, action);
    }

    public LogHolder put(String key, Object value) {
        attributes.put(key, value);
        return this;
    }

    public void buildMessage(Object key, Object value) {
        if (Objects.nonNull(key) && Objects.nonNull(value)) {
            buildMessage(String.format("%s : %s", key, value));
        }
    }

    public void buildMessage(Object message) {
        if (Objects.nonNull(message)) {
            buildMessage.add(message.toString());
            setMessage(buildMessage.toString());
        }
    }

    public void setMessage(String message) {
        put(LogKeys.LogEvent.MESSAGE, message);
    }
}
