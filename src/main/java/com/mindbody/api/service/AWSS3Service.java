package com.mindbody.api.service;


import com.amazonaws.services.s3.model.PartETag;
import com.mindbody.api.dto.UploadedFileNameResponseDTO;
import com.mindbody.api.enums.ImageType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

public interface AWSS3Service {

//    String generateFileUrl(String fileName, ImageType imageType);
//
//    String generateFileUrlExportFile(String fileName);
//
//    void uploadDocument(MultipartFile multipartFile, String fileName, String projectName);
//
//    void uploadAttachments(MultipartFile multipartFile, String fileName, String projectName);
//
//    void uploadBulkAttachments(List<File> fileList, String projectName);
//
//    URL downloadFile(String fileName, String generateFileName, ImageType type);

    UploadedFileNameResponseDTO uploadFile(MultipartFile multipartFile, String filePath, ImageType imageType);

    @Async
    void writeByte(byte[] bytes, String fileName, String s3FileName, String projectName);

    UploadedFileNameResponseDTO uploadFileType(File file, String filePath);

    String generateFileName(String fileName);

    File convertMultiPartToFile(MultipartFile file) /*throws IOException*/;

    File convertMultiPartToFileWithRename(MultipartFile file) /*throws IOException*/;

    void deleteFile(String fileName, ImageType imageType);

    void deleteFiles(List<String> fileName);

    String getFileExtension(String fileName);

    String generateUploadId(String filePath, String contentType);

    String generatePreSignedUrl(String filePath, String uploadId, int partNumber) ;

     void abortMultipartUpload(String filePath, String uploadId) ;

     void completeMultipartUpload(String filePath, String uploadId, List<PartETag> partETags) ;

}
