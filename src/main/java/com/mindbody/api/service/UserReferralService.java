package com.mindbody.api.service;

import com.mindbody.api.dto.ReferralCodeDTOList;
import com.mindbody.api.dto.ReferralListReqDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.SubmitReferralCodeDTO;
import com.mindbody.api.dto.cms.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface UserReferralService {

    void submitReferralCode(SubmitReferralCodeDTO submitReferralCodeDTO);
    SearchResultDTO<ReferralCodeDTOList> listReferrals(ReferralListReqDTO referralListReqDTO);
}
