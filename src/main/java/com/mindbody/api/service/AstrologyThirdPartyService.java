package com.mindbody.api.service;

import com.mindbody.api.dto.*;
import jakarta.validation.Valid;

import java.util.List;

public interface AstrologyThirdPartyService {

    TimezoneWithDstResDTO getTimeZoneWithDst(@Valid TimeZoneWithDstReqDTO timeZoneWithDstReqDTO);

    List<UserAstrologyDetailResDTO> getUserPlanetDetails(@Valid UserAstrologyReqDTO userAstrologyReqDTO);

    UserWheelChartResDTO getUserWheelChartDetails(@Valid UserWheelChartReqDTO userWheelChartReqDTO);

    UserPlanetSignReportResDTO getUserPlanetSignReport(@Valid UserAstrologyReqDTO userAstrologyReqDTO, String planetName);

    UserDailyHoroscopeResDTO getUserDailyHoroscope(@Valid UserHoroscopeReqDTO userHoroscopeReqDTO, String zodiacName);

    UserMonthlyHoroscopeResDTO getUserMonthlyHoroscope(@Valid UserHoroscopeReqDTO userHoroscopeReqDTO, String zodiacName);

}
