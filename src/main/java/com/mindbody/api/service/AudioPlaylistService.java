package com.mindbody.api.service;


import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.UserAudioPlaylistResDTO;
import com.mindbody.api.dto.cms.AddAudioPlaylistReqDTO;
import com.mindbody.api.dto.cms.AudioPlaylistDetailResDTO;
import com.mindbody.api.dto.cms.CMSAudioPlaylistResDTO;
import com.mindbody.api.dto.cms.EditAudioPlaylistReqDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface AudioPlaylistService {

    void addAudioPlaylist(MultipartFile audioPlaylistImage, AddAudioPlaylistReqDTO addAudioPlaylistReqDTO);

    void editAudioPlaylist(MultipartFile audioPlaylistImage, EditAudioPlaylistReqDTO editAudioPlaylistReqDTO);

    SearchResultDTO<AudioPlaylistDetailResDTO> listAudioPlaylist(CommonListDTO commonListDTO, boolean checkIsActiveRecord);

    void deleteAudioPlaylist(Long audioPlaylistId);

    String activeInactiveAudioPlaylist(Long audioPlaylistId);

    List<CMSAudioPlaylistResDTO> listAudioPlaylistForSelectedAudioPlaylistTypeForCMS(String playlistModuleType);

    List<UserAudioPlaylistResDTO> listAudioPlaylistForSelectedAudioPlaylistTypeForUser(String playlistModuleType);
}
