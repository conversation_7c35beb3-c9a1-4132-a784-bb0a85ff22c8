package com.mindbody.api.service;

import com.mindbody.api.dto.SearchResultDTO;
import org.springframework.data.domain.Sort;

import java.lang.reflect.Field;
import java.util.Comparator;
import java.util.List;

public abstract class BaseService {

    protected final MessageService messageService;

    public BaseService(MessageService messageService) {
        this.messageService = messageService;
    }


    public static <T> Comparator<T> applySorting(Sort sort, Class<T> type) {
        if (sort == null || !sort.isSorted()) {
            return null;
        }

        return sort.get().map(order -> {
            Comparator<T> comparator = Comparator.comparing(item -> {
                try {
                    Field field = type.getDeclaredField(order.getProperty());
                    field.setAccessible(true);
                    return (Comparable<Object>) field.get(item);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    throw new RuntimeException("Failed to access field: " + order.getProperty(), e);
                }
            });
            return order.isAscending() ? comparator : comparator.reversed();
        }).reduce(Comparator::thenComparing).orElseThrow();
    }

    public static <T> SearchResultDTO<T> applyPagination(List<T> items, int pageNumber, int pageSize) {
        if (pageNumber < 0 || pageSize <= 0) {
            throw new IllegalArgumentException("Page number cannot be negative and page size must be positive.");
        }
        int totalRecords = items.size();
        int startIndex = pageNumber * pageSize;
        if (startIndex >= totalRecords) {
            return new SearchResultDTO<>(List.of(), totalRecords, pageSize);
        }
        int endIndex = Math.min(startIndex + pageSize, totalRecords);
        List<T> paginatedItems = items.subList(startIndex, endIndex);
        return new SearchResultDTO<>(paginatedItems, totalRecords, pageSize);
    }

}
