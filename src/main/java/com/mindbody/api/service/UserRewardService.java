package com.mindbody.api.service;

import com.mindbody.api.dto.RewardRedemptionResDTO;
import com.mindbody.api.dto.RewardStatusDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.enums.RewardType;
import org.springframework.data.domain.Pageable;

/**
 * Service interface for managing user rewards functionality
 */
public interface UserRewardService {

    /**
     * Get all rewards with their redeem status and points for a user
     *
     * @param userId User ID
     * @param queryToSearch Search query
     * @param rewardType Type of reward to filter by (optional)
     * @param pageable Pagination information
     * @return List of rewards with their status
     */
    SearchResultDTO<RewardStatusDTO> getRewardsWithStatus(Long userId, String queryToSearch, RewardType rewardType, Pageable pageable);

    /**
     * Redeem a reward for a user using points
     *
     * @param userId User ID
     * @param rewardId Reward ID
     * @return Redemption response containing reward title and remaining points
     */
    RewardRedemptionResDTO redeemReward(Long userId, Long rewardId);
} 