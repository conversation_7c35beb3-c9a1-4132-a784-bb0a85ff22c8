package com.mindbody.api.service.scheduler;

import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class DeleteSentNotificationsJob implements Job {

    private final ScheduledNotificationService scheduledNotificationService;

    public DeleteSentNotificationsJob(ScheduledNotificationService scheduledNotificationService) {
        this.scheduledNotificationService = scheduledNotificationService;
    }

    private static final Logger logger = LoggerFactory.getLogger(DeleteSentNotificationsJob.class);

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        logger.info("Scheduling Delete Sent Notifications for today...");
        scheduledNotificationService.scheduleDeleteSentNotification();  // Everyday delete notifications from table which are sent
    }
}