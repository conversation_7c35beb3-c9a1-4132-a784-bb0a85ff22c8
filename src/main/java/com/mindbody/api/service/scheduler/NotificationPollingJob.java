package com.mindbody.api.service.scheduler;

import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class NotificationPollingJob implements Job {

    @Autowired
    private ScheduledNotificationService scheduledNotificationService;

    Logger logger = LoggerFactory.getLogger(NotificationPollingJob.class);

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        logger.info("Checking for scheduled notifications...");
        scheduledNotificationService.sendScheduledNotifications();  // Send notifications that are due
    }
}
