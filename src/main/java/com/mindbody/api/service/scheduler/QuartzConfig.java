package com.mindbody.api.service.scheduler;

import org.quartz.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.TimeZone;

@Configuration
public class QuartzConfig {


    // Birthday Notifications
    @Bean
    public JobDetail birthdayNotificationJobDetail() {
        return JobBuilder.newJob(BirthdayNotificationJob.class)
                .withIdentity("birthdayNotificationJob")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger birthdayNotificationJobTrigger() {
        return TriggerBuilder.newTrigger()
                .forJob(birthdayNotificationJobDetail())
                .withIdentity("birthdayNotificationTrigger")
                .withSchedule(CronScheduleBuilder.dailyAtHourAndMinute(0, 0)
                .inTimeZone(TimeZone.getTimeZone("UTC")))
                .build();
    }

    // Daily Reading Notification
    @Bean
    public JobDetail dailyReadingNotificationJobDetail() {
        return JobBuilder.newJob(DailyReadingJob.class)
                .withIdentity("dailyReadingNotificationJob")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger dailyReadingNotificationJobTrigger() {
        return TriggerBuilder.newTrigger()
                .forJob(dailyReadingNotificationJobDetail())
                .withIdentity("dailyReadingNotificationJob")
                .withSchedule(CronScheduleBuilder.dailyAtHourAndMinute(0, 0)
                .inTimeZone(TimeZone.getTimeZone("UTC")))
                .build();
    }

//    // Weekly Reading Notification
//    @Bean
//    public JobDetail monthlyReadingNotificationJobDetail() {
//        return JobBuilder.newJob(MonthlyReadingJob.class)
//                .withIdentity("monthlyReadingNotificationJob")
//                .storeDurably()
//                .build();
//    }
//
//    @Bean
//    public Trigger monthlyReadingNotificationJobTrigger() {
//        return TriggerBuilder.newTrigger()
//                .forJob(monthlyReadingNotificationJobDetail())
//                .withIdentity("monthlyReadingNotificationJob")
//                .withSchedule(CronScheduleBuilder.monthlyOnDayAndHourAndMinute(1, 9, 0)
//                .inTimeZone(TimeZone.getTimeZone("UTC")))
//                .build();
//    }

    // Notification Polling

    @Bean
    public JobDetail notificationPollingJobDetail() {
        return JobBuilder.newJob(NotificationPollingJob.class)
                .withIdentity("notificationPollingJob")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger notificationPollingJobTrigger() {
        return TriggerBuilder.newTrigger()
                .forJob(notificationPollingJobDetail())
                .withIdentity("notificationPollingTrigger")
                .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                        .withIntervalInMinutes(1)
                        .repeatForever())
                .build();
    }

    // Daily Delete Sent Notification
    @Bean
    public JobDetail deleteSentNotificationsJobDetail() {
        return JobBuilder.newJob(DeleteSentNotificationsJob.class)
                .withIdentity("deleteSentNotificationsJob")
                .storeDurably()
                .build();
    }

    @Bean
    public Trigger deleteSentNotificationsJobTrigger() {
        return TriggerBuilder.newTrigger()
                .forJob(deleteSentNotificationsJobDetail())
                .withIdentity("deleteSentNotificationsJob")
                .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                        .withIntervalInHours(1)
                        .repeatForever())
                .build();
    }

}
