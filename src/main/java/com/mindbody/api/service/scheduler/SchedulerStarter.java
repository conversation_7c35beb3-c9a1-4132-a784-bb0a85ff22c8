package com.mindbody.api.service.scheduler;

import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class SchedulerStarter {

    Logger logger = LoggerFactory.getLogger(SchedulerStarter.class);

    @Autowired
    private Scheduler scheduler;

    @PostConstruct
    public void startScheduler() throws SchedulerException {
        try {
        if (!scheduler.isStarted()) {
            logger.info("Starting Quartz Scheduler...");
            scheduler.start();
        } else {
            logger.info("Quartz Scheduler already started.");
        }

    } catch (SchedulerException e) {
        e.printStackTrace();
    }

    }
}
