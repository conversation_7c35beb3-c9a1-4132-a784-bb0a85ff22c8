package com.mindbody.api.service.scheduler;

import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class DailyReadingJob implements Job {

    private final ScheduledNotificationService scheduledNotificationService;

    public DailyReadingJob(ScheduledNotificationService scheduledNotificationService) {
        this.scheduledNotificationService = scheduledNotificationService;
    }

    private static final Logger logger = LoggerFactory.getLogger(DailyReadingJob.class);

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        logger.info("Scheduling Daily Reading notifications for today...");
        scheduledNotificationService.scheduleDailyReadingNotification();  // Schedule birthday notifications only once per day
    }
}