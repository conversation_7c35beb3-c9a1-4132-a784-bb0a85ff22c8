package com.mindbody.api.service.scheduler;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.notification.NotificationDTO;
import com.mindbody.api.dto.notification.SendNotificationToUsersReqDTO;
import com.mindbody.api.enums.NotificationType;
import com.mindbody.api.model.EntityNotificationImage;
import com.mindbody.api.model.EntityNotificationSchedule;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.model.EntityUserInfo;
import com.mindbody.api.repository.NotificationImageRepository;
import com.mindbody.api.repository.NotificationScheduleRepository;
import com.mindbody.api.repository.UserInfoRepository;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.service.NotificationService;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.Year;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

@Service
@Transactional
public class ScheduledNotificationService {

    private final NotificationScheduleRepository scheduleRepository;

    private final UserInfoRepository userInfoRepository;

    private final NotificationService notificationService;

    private final UserRepository userRepository;

    private final NotificationImageRepository notificationImageRepository;

    private static final Logger LOGGER = LoggerFactory.getLogger(ScheduledNotificationService.class);

    private final GlobalConfiguration globalConfiguration;

    private final String baseS3Url;
    protected static final Logger logger = LoggerFactory.getLogger(ScheduledNotificationService.class);

    @Autowired
    ScheduledNotificationService(NotificationScheduleRepository scheduleRepository, UserInfoRepository userInfoRepository, NotificationService notificationService, UserRepository userRepository, NotificationImageRepository notificationImageRepository, GlobalConfiguration globalConfiguration) {
        this.scheduleRepository = scheduleRepository;
        this.userInfoRepository = userInfoRepository;
        this.notificationService = notificationService;
        this.userRepository = userRepository;
        this.notificationImageRepository = notificationImageRepository;
        this.globalConfiguration = globalConfiguration;
        this.baseS3Url = globalConfiguration.getAmazonS3().getUrlUpToBucket()
                + globalConfiguration.getAmazonS3().getUploadFolderName();
    }

    // Extracted method for birthday notifications
    public void scheduleBirthdayNotifications() {
        List<EntityUserInfo> birthdayUsers = userInfoRepository.findUsersWithBirthdayToday();
        logger.info("Current Timezone is " + TimeZone.getDefault().getID());

        for (EntityUserInfo userInfo : birthdayUsers) {
            LocalDateTime userBirthdayInUserTimeZone=null;
                LocalDateTime userBirthdayUtc = userInfo.getDateOfBirth().withHour(9).withMinute(0).withYear(Year.now().getValue());  // This represents the birthday in UTC (system's time)

            ZonedDateTime userBirthdayUtcZoned = userBirthdayUtc.atZone(ZoneId.of(userInfo.getTimezone()));

            userBirthdayInUserTimeZone = userBirthdayUtcZoned.withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();


            scheduleNotification(
                    "Happy Birthday, " + userInfo.getName() + "!",
                    "Wishing you a fantastic day filled with joy!",
                    userInfo.getUserId(),
                    userBirthdayInUserTimeZone,
                    NotificationType.BIRTHDAY,
                    null
            );
        }
    }

    // Generic method to schedule notifications
    public void scheduleNotification(String title, String content, Long userId, LocalDateTime scheduledTime, NotificationType notificationType, String topicName) {
        EntityNotificationSchedule notification = EntityNotificationSchedule.builder()
                .title(title)
                .content(content)
                .userId(userId)
                .scheduledTime(scheduledTime)
                .sent(false)
                .notificationType(notificationType)
                .topicName(topicName)
                .build();
        scheduleRepository.save(notification);
    }

    @Async
    // Method to send all pending notifications
    public void sendScheduledNotifications() {
        Pageable pageable = PageRequest.of(0, 5000);
        List<EntityNotificationSchedule> pendingNotifications = scheduleRepository.findTop5000PendingNotifications(pageable, LocalDateTime.now());
        for (EntityNotificationSchedule notification : pendingNotifications) {
            EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(notification.getUserId(), true, false);

            Map<String, String> notificationInfo = new HashMap<>();
            NotificationDTO notificationDTO = new NotificationDTO();
            notificationDTO.setTitle(notification.getTitle());
            notificationDTO.setMessage(notification.getContent());
            EntityNotificationImage entityNotificationImage = notificationImageRepository.findByNotificationType(notification.getNotificationType());
            if (entityNotificationImage != null) {
                notificationDTO.setImageKey(entityNotificationImage.getImage());
                String folderName = globalConfiguration.getAmazonS3().getNotificationImageFolderName();
                notificationDTO.setImageUrl(baseS3Url + folderName + entityNotificationImage.getImage());
            }
            notificationDTO.setNotificationType(notification.getNotificationType().name());
            notificationInfo.put("title", notificationDTO.getTitle());
            notificationInfo.put("message", notificationDTO.getMessage());
            notificationInfo.put("notificationType", notificationDTO.getNotificationType());
            notificationDTO.setData(notificationInfo);

            if (Objects.nonNull(notification.getUserId())) {
                notificationService.sendNotificationToAppUser(entityUser, notificationDTO);
            } else if (Objects.nonNull(notification.getTopicName())) {
                notificationService.sendNotificationToUsersFromCMS(new SendNotificationToUsersReqDTO(
                        null,
                        new String[]{notification.getTopicName()},
                        notification.getTitle(),
                        notification.getContent(),
                        notification.getNotificationType().name(),
                        false,
                        null,
                        null
                ));
            }
            notification.setSent(true);
            scheduleRepository.save(notification);
        }
    }

    // Send Daily Reading Notification to all users.
    public void scheduleDailyReadingNotification() {
        LocalDateTime date = LocalDateTime.now().withHour(9).withMinute(0).withSecond(0);
        List<EntityNotificationSchedule> listNotificationSchedule = new ArrayList<>();
        Optional<List<EntityUserInfo>> optionalUserInfoList = userInfoRepository.findByIsDeletedAndIsActive(false, true);
        NotificationDTO notificationDTO = new NotificationDTO();
        notificationDTO.setTitle("Daily Reading");
        notificationDTO.setMessage("Click here for your Daily Reading");
        notificationDTO.setNotificationType(NotificationType.DAILY_READING.name());
        Map<String, String> notificationInfo = new HashMap<>();
        notificationInfo.put("title", notificationDTO.getTitle());
        notificationInfo.put("message", notificationDTO.getMessage());
        notificationInfo.put("notificationType", NotificationType.DAILY_READING.name());
        notificationDTO.setData(notificationInfo);

        if (optionalUserInfoList.isPresent()) {
            List<EntityUserInfo> userInfoList = optionalUserInfoList.get();
            for (EntityUserInfo userInfo : userInfoList) {
                ZonedDateTime systemZonedDateTime = date.atZone(ZoneId.of(userInfo.getTimezone()));
                LocalDateTime userLocalDateTime = systemZonedDateTime.withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();

                listNotificationSchedule.add(new EntityNotificationSchedule(
                        notificationDTO.getTitle(),
                        notificationDTO.getMessage(),
                        userInfo.getUserId(),
                        userLocalDateTime,
                        false,
                        NotificationType.valueOf(NotificationType.DAILY_READING.name()),
                        null
                ));
            }
            scheduleRepository.saveAll(listNotificationSchedule);
        }
    }

    // Send Weekly Reading Notification to all users.
    public void scheduleMonthlyReadingNotification() {

        LocalDateTime date = LocalDateTime.now().withHour(9).withMinute(0).withSecond(0);
        List<EntityNotificationSchedule> listNotificationSchedule = new ArrayList<>();
        Optional<List<EntityUserInfo>> optionalUserInfoList = userInfoRepository.findByIsDeletedAndIsActive(false, true);
        NotificationDTO notificationDTO = new NotificationDTO();
        notificationDTO.setTitle("Monthly Reading");
        notificationDTO.setMessage("Click here for your Monthly Reading");
        notificationDTO.setNotificationType(NotificationType.MONTHLY_READING.name());
        Map<String, String> notificationInfo = new HashMap<>();
        notificationInfo.put("title", notificationDTO.getTitle());
        notificationInfo.put("message", notificationDTO.getMessage());
        notificationInfo.put("notificationType", NotificationType.MONTHLY_READING.name());
        notificationDTO.setData(notificationInfo);

        if (optionalUserInfoList.isPresent()) {
            List<EntityUserInfo> userInfoList = optionalUserInfoList.get();
            for (EntityUserInfo userInfo : userInfoList) {
                ZonedDateTime systemZonedDateTime = date.atZone(ZoneId.of(userInfo.getTimezone()));
                LocalDateTime userLocalDateTime = systemZonedDateTime.withZoneSameInstant(ZoneId.of(TimeZone.getDefault().getID())).toLocalDateTime();

                listNotificationSchedule.add(new EntityNotificationSchedule(
                        notificationDTO.getTitle(),
                        notificationDTO.getMessage(),
                        userInfo.getUserId(),
                        userLocalDateTime,
                        false,
                        NotificationType.valueOf(NotificationType.MONTHLY_READING.name()),
                        null
                ));
            }
            scheduleRepository.saveAll(listNotificationSchedule);
        }

    }

    // Delete Sent Notifications to all users.
    public void scheduleDeleteSentNotification() {
        scheduleRepository.deleteBySent(true);
    }

    public void saveAll(List<EntityNotificationSchedule> list) {
        scheduleRepository.saveAll(list);
    }

}
