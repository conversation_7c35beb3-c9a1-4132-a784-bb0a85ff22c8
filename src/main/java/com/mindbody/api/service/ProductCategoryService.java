package com.mindbody.api.service;

import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.cms.AddProductCategoryReqDTO;
import com.mindbody.api.dto.cms.EditProductCategoryReqDTO;
import com.mindbody.api.dto.cms.ProductCategoryResDTO;

import java.util.List;

public interface ProductCategoryService {

    void addProductCategory(AddProductCategoryReqDTO addProductCategoryReqDTO);

    void editProductCategory(EditProductCategoryReqDTO editProductCategoryReqDTO);

    SearchResultDTO<ProductCategoryResDTO> listProductCategoryCms(CommonListDTO commonListDTO);

    void deleteProductCategory(Long productCategoryId);

    boolean activeInactiveProductCategory(Long productCategoryId);

    List<ProductCategoryResDTO> listProductCategoryForUserAndCms();
}
