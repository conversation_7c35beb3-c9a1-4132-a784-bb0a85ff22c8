package com.mindbody.api.service;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;

@Service
@Slf4j
public class ConnectionMonitoringService {

    @Autowired
    private DataSource dataSource;

    @Scheduled(fixedRate = 30000) // Log every 30 seconds
    public void monitorConnectionPool() {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
            
            int activeConnections = hikariDataSource.getHikariPoolMXBean().getActiveConnections();
            int idleConnections = hikariDataSource.getHikariPoolMXBean().getIdleConnections();
            int totalConnections = hikariDataSource.getHikariPoolMXBean().getTotalConnections();
            int maxPoolSize = hikariDataSource.getMaximumPoolSize();
            
            log.info("Connection Pool Status - Active: {}, Idle: {}, Total: {}, Max: {}", 
                    activeConnections, idleConnections, totalConnections, maxPoolSize);
            
            // Log warning if pool is getting full
            if (activeConnections > maxPoolSize * 0.8) {
                log.warn("Connection pool is getting full! Active connections: {} out of {}", 
                        activeConnections, maxPoolSize);
            }
        }
    }
} 