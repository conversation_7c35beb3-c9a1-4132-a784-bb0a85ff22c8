package com.mindbody.api.service;

import com.mindbody.api.dto.notification.*;
import com.mindbody.api.model.EntityAudio;
import com.mindbody.api.model.EntityUser;

import java.util.List;

public interface NotificationService {


    void sendNotificationToUsersFromCMS(SendNotificationToUsersReqDTO sendNotificationToUsersReqDTO);

    void sendNotificationToUsersByAdmin(NotificationDTO notificationDTO, List<String> tokens, List<Long> userIds);

    void sendNotificationToAppUser(EntityUser entityUser, NotificationDTO notificationDTO);

    NotificationDTO setWelcomeNotificationData();

    List<NotificationDetailResDTO> listNotificationsForUser();

    void subscribeUserForTopic(SubscribeTopicReqDTO subscribeTopicReqDTO);

    NotificationDTO setNewWorkoutPlanNotificationData(boolean isAddWorkoutPlan,Long workoutPlanId);

    NotificationDTO setExclusiveContentUnlockedNotificationData(boolean isReferrer, EntityAudio unlockedAudio);
}
