package com.mindbody.api.service;

import com.mindbody.api.enums.DeviceType;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.model.EntityUserDevice;

import java.util.List;

public interface UserDeviceService {

    /**
     * Track a user's device and IP address
     *
     * @param user The user entity
     * @param deviceUniqueId The unique device ID
     * @param ipAddress The IP address
     * @param deviceType The device type
     * @return The created or updated user device entity
     */
    EntityUserDevice trackUserDevice(EntityUser user, String deviceUniqueId, String ipAddress, DeviceType deviceType);

    /**
     * Check if a device or IP is associated with another user
     *
     * @param userId The current user ID
     * @param deviceUniqueId The device unique ID
     * @param ipAddress The IP address
     * @return true if the device or IP is associated with another user, false otherwise
     */
    boolean isDeviceOrIpAssociatedWithOtherUser(Long userId, String deviceUniqueId, String ipAddress);

    /**
     * Mark devices as belonging to a deleted account
     *
     * @param userId The user ID whose account is being deleted
     */
    void markUserDevicesAsDeletedAccount(Long userId);

    /**
     * Check if a device or IP was previously associated with a deleted account
     *
     * @param deviceUniqueId The device unique ID
     * @param ipAddress The IP address
     * @return true if the device or IP was previously associated with a deleted account, false otherwise
     */
    boolean wasDeviceOrIpAssociatedWithDeletedAccount(String deviceUniqueId, String ipAddress);

    /**
     * Get the IP address from the request
     *
     * @return The IP address
     */
    String getCurrentIpAddress();

    /**
     * Check if a device has been used to enter any referral code in the past
     *
     * @param deviceUniqueId The device unique ID
     * @return true if the device has been used to enter a referral code in the past, false otherwise
     */
    boolean hasDeviceEnteredAnyReferralCodeBefore(String deviceUniqueId);
}
