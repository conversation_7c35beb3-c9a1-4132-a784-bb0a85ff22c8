package com.mindbody.api.service;

import com.mindbody.api.dto.cms.EmailDetailDTO;
import com.mindbody.api.util.Mail;
import freemarker.template.TemplateException;
import jakarta.mail.MessagingException;
import java.io.IOException;
import java.util.Map;

public interface EmailService {

    void sendMimeMail(EmailDetailDTO details) throws MessagingException;

    /**
     * This method will send email with attachments.
     *
     * @param details - reference of class having metadata of email
     * @return null
     */
    String sendMailWithAttachment(EmailDetailDTO details);

    /**
     * This method is used to convert HTML code into String by using .ftl file
     *
     * @param mail having value of template and mail.
     * @return returns html code converted into string.
     * @throws IOException       need to throw.
     * @throws TemplateException need to throw.
     */
    String parseHtml(Mail mail) throws IOException, TemplateException;

    void sendEmail(String templateName, String subject, String toEmail, Map<String, Object> map);

}
