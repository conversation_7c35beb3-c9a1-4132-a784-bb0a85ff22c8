package com.mindbody.api.service;

import com.mindbody.api.enums.AchievementActivityType;

/**
 * Service interface for handling achievement events.
 * This service acts as a mediator between UserStatsService and UserAchievementService
 * to break the circular dependency between them.
 */
public interface AchievementEventService {

    /**
     * Processes a leaderboard achievement event.
     * 
     * @param userId The ID of the user.
     * @param rank The user's rank on the leaderboard.
     */
    void processLeaderboardAchievement(Long userId, long rank);
    
    /**
     * Checks if a user already has a specific achievement.
     * 
     * @param achievementActivityType The type of achievement activity.
     * @param userId The ID of the user.
     * @return true if the user has the achievement, false otherwise.
     */
    boolean hasAchievement(AchievementActivityType achievementActivityType, Long userId);
}
