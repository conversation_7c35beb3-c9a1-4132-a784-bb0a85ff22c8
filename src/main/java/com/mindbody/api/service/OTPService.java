package com.mindbody.api.service;

import com.mindbody.api.dto.SendOTPReqDTO;
import com.mindbody.api.dto.UserResDTO;
import com.mindbody.api.dto.VerifyOTPReqDTO;
import com.mindbody.api.dto.VerifyOTPResDTO;
import com.mindbody.api.enums.OtpModuleName;
import com.mindbody.api.model.EntityUser;
import jakarta.validation.Valid;

public interface OTPService {

    UserResDTO sendVerificationOTP(@Valid SendOTPReqDTO sendOTPReqDTO);

    String generateAndSaveOTP(EntityUser entityUser, OtpModuleName moduleName);

    VerifyOTPResDTO verifyOTP(VerifyOTPReqDTO verifyOTPReqDTO);
}
