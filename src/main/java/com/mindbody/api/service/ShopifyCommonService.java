package com.mindbody.api.service;

import org.springframework.web.reactive.function.client.ClientResponse;
import reactor.core.publisher.Mono;

public interface ShopifyCommonService {

    <T> T sendGraphQlRequestToShopify(String requestBody, Class<T> responseType);
    <T> T sendGraphQlRequestToShopifyAdmin(String requestBody, Class<T> responseType);
    <T> Mono<T> handleErrors(ClientResponse response);

}
