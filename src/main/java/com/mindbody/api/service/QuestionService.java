package com.mindbody.api.service;

import com.mindbody.api.dto.*;

import java.util.List;

public interface QuestionService {

    QuestionOptionResDTO addQuestion(AddQuestionReqDTO addQuestionReqDTO);

    QuestionOptionResDTO editQuestion(EditQuestionReqDTO editQuestionReqDTO);

    SearchResultDTO<QuestionOptionResDTO> listQuestion(CommonListDTO commonListDTO, boolean checkIsActiveRecord);
    void deleteQuestion(Long questionId);

    void changeQuestionOrder(List<ChangeQuestionOrderReqDTO> changeQuestionOrderReqDTOList);

}
