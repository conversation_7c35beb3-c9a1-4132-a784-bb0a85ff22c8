package com.mindbody.api.service;

import com.mindbody.api.dto.GenerateLeaderboardReqDTO;
import com.mindbody.api.dto.MeSectionResponseDTO;
import com.mindbody.api.dto.PinUserRequestDTO;

import java.util.Map;

public interface LeaderboardService {

    Map<String, Object> generateLeaderboard(GenerateLeaderboardReqDTO generateLeaderboardReqDTO);
    void pinLeaderboardUser(PinUserRequestDTO request);
    MeSectionResponseDTO getLeaderboardProfile(Long currentUserId, String userType, Long userId);
    void unpinUser(PinUserRequestDTO request);

}
