package com.mindbody.api.service;

import com.mindbody.api.dto.cms.*;
import org.springframework.web.multipart.MultipartFile;

public interface AdminService {
    AdminAccessTokenDTO adminLogin(AdminLoginReqDTO adminLoginReqDTO);

    void editAdminProfile(MultipartFile profileImage,EditAdminProfileReqDTO editAdminProfileReqDTO);

    void adminLogout(String accessToken);

    void changePassword(ChangePasswordReqDTO changePasswordReqDTO);

    void sendForgotPasswordLink(ForgotPasswordReqDTO forgotPasswordReqDTO);

    void resetPassword(ResetPasswordReqDTO resetPasswordReqDTO);

    ViewAdminProfileResDTO viewAdminProfile();
}
