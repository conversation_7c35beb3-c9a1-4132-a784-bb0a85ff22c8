package com.mindbody.api.service;

import com.mindbody.api.dto.AbortFileUploadReqDTO;
import com.mindbody.api.dto.CompleteFileUploadReqDTO;
import com.mindbody.api.dto.GeneratePreSignedUrlReqDTO;
import com.mindbody.api.dto.GeneratePreSignedUrlResDTO;

public interface FileUploadService {

    GeneratePreSignedUrlResDTO generatePreSignedUrls(GeneratePreSignedUrlReqDTO generatePreSignedUrlReqDTO);

    void completeFileUpload(CompleteFileUploadReqDTO completeFileUploadReqDTO);

    void abortFileUpload(AbortFileUploadReqDTO abortFileUploadReqDTO);
}
