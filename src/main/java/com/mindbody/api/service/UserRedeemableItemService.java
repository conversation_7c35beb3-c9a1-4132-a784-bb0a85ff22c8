package com.mindbody.api.service;

import com.mindbody.api.dto.RedeemItemReqDTO;
import com.mindbody.api.dto.RedeemItemResDTO;
import com.mindbody.api.dto.RedeemableItemDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.RedeemableItemsWithPointsDTO;
import org.springframework.data.domain.Pageable;

/**
 * Service interface for managing user redeemable items functionality
 */
public interface UserRedeemableItemService {

    /**
     * Get all redeemable items (audios and rewards) with their redeem status for a user
     *
     * @param userId User ID
     * @param queryToSearch Search query
     * @param itemType Type of item to filter by (optional, "AUDIO" or "REWARD")
     * @param pageable Pagination information
     * @return List of redeemable items with their status and available points
     */
    RedeemableItemsWithPointsDTO getRedeemableItemsWithStatus(
            Long userId,
            String queryToSearch,
            String itemType,
            Pageable pageable);

    /**
     * Redeem an item (audio or reward) for a user using points
     *
     * @param redeemItemReqDTO Request containing user ID, item ID, and item type
     * @return Redemption response containing item title and remaining points
     */
    RedeemItemResDTO redeemItem(RedeemItemReqDTO redeemItemReqDTO);
} 