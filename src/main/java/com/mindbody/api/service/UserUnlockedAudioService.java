package com.mindbody.api.service;

import com.mindbody.api.dto.ExclusiveAudioStatusDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.model.EntityUserUnlockedAudio;

import java.util.List;
import org.springframework.data.domain.Pageable;

/**
 * Service interface for managing user unlocked audios
 */
public interface UserUnlockedAudioService {

    /**
     * Unlock an audio for a user
     *
     * @param userId  User ID
     * @param audioId Audio ID
     * @return EntityUserUnlockedAudio
     */
    EntityUserUnlockedAudio unlockAudio(Long userId, Long audioId);

    /**
     * Lock an audio for a user
     *
     * @param userId  User ID
     * @param audioId Audio ID
     */
    void lockAudio(Long userId, Long audioId);

    /**
     * Check if an audio is unlocked by a user
     *
     * @param userId  User ID
     * @param audioId Audio ID
     * @return true if audio is unlocked, false otherwise
     */
    boolean isAudioUnlocked(Long userId, Long audioId);

    /**
     * Get all unlocked audios for a user
     *
     * @param userId User ID
     * @return List of EntityUserUnlockedAudio
     */
    List<EntityUserUnlockedAudio> getAllUnlockedAudios(Long userId);

    /**
     * Count unlocked audios for a user
     *
     * @param userId User ID
     * @return Count of unlocked audios
     */
    long countUnlockedAudios(Long userId);

    /**
     * Get all exclusive audios with their unlock status for a user
     *
     * @param userId User ID
     * @param queryToSearch Search query
     * @param pageable Pageable
     * @return SearchResultDTO containing ExclusiveAudioStatusDTO objects
     */
    SearchResultDTO<ExclusiveAudioStatusDTO> getExclusiveAudiosWithStatus(Long userId, String queryToSearch, Pageable pageable);

    /**
     * Get all locked exclusive audios for a user
     *
     * @param userId User ID
     * @param queryToSearch Search query
     * @param pageable Pageable
     * @return SearchResultDTO containing ExclusiveAudioStatusDTO objects
     */
    SearchResultDTO<ExclusiveAudioStatusDTO> getLockedExclusiveAudios(Long userId, String queryToSearch, Pageable pageable);

    /**
     * Get all unlocked exclusive audios for a user
     *
     * @param userId User ID
     * @param queryToSearch Search query
     * @param pageable Pageable
     * @return SearchResultDTO containing ExclusiveAudioStatusDTO objects
     */
    SearchResultDTO<ExclusiveAudioStatusDTO> getUnlockedExclusiveAudios(Long userId, String queryToSearch, Pageable pageable);
}
