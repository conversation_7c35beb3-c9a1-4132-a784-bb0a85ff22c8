package com.mindbody.api.service;

import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.cms.AddExerciseDTO;
import com.mindbody.api.dto.cms.CMSListExerciseResDTO;
import com.mindbody.api.dto.cms.EditExerciseDTO;
import com.mindbody.api.dto.cms.ExerciseResDTO;
import jakarta.validation.Valid;

import java.util.List;

public interface ExerciseService {

    void addExercise(AddExerciseDTO addExerciseDTO);

    void deleteExercise(Long exerciseId);

    SearchResultDTO<ExerciseResDTO> listExerciseCms(@Valid CommonListDTO commonListDTO);

    List<CMSListExerciseResDTO> listExerciseToAddWorkoutPlanCMS();

    boolean activeInactiveExercise(Long exerciseId);

    void editExercise(EditExerciseDTO editExerciseDTO);
}
