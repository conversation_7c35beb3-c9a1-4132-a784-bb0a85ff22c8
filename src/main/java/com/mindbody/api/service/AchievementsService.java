package com.mindbody.api.service;

import com.mindbody.api.dto.AchievementDTO;
import com.mindbody.api.dto.AchievementsBySetNameReq;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.achievement.AchievementCategoryListReqDTO;
import com.mindbody.api.dto.achievement.AchievementCategoryListResDTO;
import com.mindbody.api.dto.achievement.AchievementCountForCategoryResDTO;
import com.mindbody.api.dto.cms.AchievementListReqDTO;
import com.mindbody.api.dto.cms.AchievementListResDTO;
import com.mindbody.api.dto.cms.EditAchievementReqDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface AchievementsService {

    SearchResultDTO<AchievementListResDTO> listAchievements(AchievementListReqDTO achievementListReqDTO);

    void editAchievement(EditAchievementReqDTO editAchievementReqDTO, MultipartFile borderImage, MultipartFile badgeImage);

    AchievementCategoryListResDTO getAchievementSubCategoryListForCategorySelectedByUser(AchievementCategoryListReqDTO achievementCategoryListReqDTO);

    List<AchievementCountForCategoryResDTO> getAchievementsCountForAllCategories(Long userId);

    List<AchievementDTO> getAllAchievementsBySetName(AchievementsBySetNameReq achievementsBySetNameReq);

    Map<String, Long> getCurrentAndNextLevelMap(Long wxpEarned);


}
