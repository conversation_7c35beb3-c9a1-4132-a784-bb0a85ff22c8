package com.mindbody.api.service;

import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.cms.AddWorkoutPlanReqDTO;
import com.mindbody.api.dto.cms.EditWorkoutPlanReqDTO;
import com.mindbody.api.dto.cms.WorkoutPlanListReqDTO;
import com.mindbody.api.dto.cms.WorkoutPlanlistDetailResDTO;
import jakarta.validation.Valid;

public interface WorkoutPlanService {
    void addWorkoutPlan(AddWorkoutPlanReqDTO addWorkoutPlanReqDTO);

    void editWorkoutPlan(EditWorkoutPlanReqDTO editWorkoutPlanReqDTO);

    String activeInactiveWorkoutPlan(Long workoutPlanId);

    void deleteWorkoutPlan(Long workoutPlanId);

    SearchResultDTO<WorkoutPlanlistDetailResDTO> listWorkoutPlanList(@Valid WorkoutPlanListReqDTO workoutPlanListReqDTO);

    WorkoutPlanlistDetailResDTO viewWorkoutPlan(Long workoutPlanId);
}
