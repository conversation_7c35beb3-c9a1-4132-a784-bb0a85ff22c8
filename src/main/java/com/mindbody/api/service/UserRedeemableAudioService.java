package com.mindbody.api.service;

import com.mindbody.api.dto.RedeemableAudioStatusDTO;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Service interface for managing user redeemable audio functionality
 */
public interface UserRedeemableAudioService {

    /**
     * Get all redeemable audios with their redeem status and points for a user
     *
     * @param userId User ID
     * @param queryToSearch Search query
     * @param pageable Pagination information
     * @return List of redeemable audios with their status and points
     */
    List<RedeemableAudioStatusDTO> getRedeemableAudiosWithStatus(Long userId, String queryToSearch, Pageable pageable);

    /**
     * Get locked redeemable audios for a user
     *
     * @param userId User ID
     * @param queryToSearch Search query
     * @param pageable Pagination information
     * @return List of locked redeemable audios
     */
    List<RedeemableAudioStatusDTO> getLockedRedeemableAudios(Long userId, String queryToSearch, Pageable pageable);

    /**
     * Get redeemed audios for a user
     *
     * @param userId User ID
     * @param queryToSearch Search query
     * @param pageable Pagination information
     * @return List of redeemed audios
     */
    List<RedeemableAudioStatusDTO> getRedeemedAudios(Long userId, String queryToSearch, Pageable pageable);

    /**
     * Redeem an audio for a user using points
     *
     * @param userId User ID
     * @param audioId Audio ID
     */
    void redeemAudio(Long userId, Long audioId);
} 