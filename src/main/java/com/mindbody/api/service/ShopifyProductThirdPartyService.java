package com.mindbody.api.service;

import com.mindbody.api.dto.shopify.*;
import jakarta.validation.Valid;

public interface ShopifyProductThirdPartyService {

    ProductListResDTO getAllProductList(@Valid ProductListReqDTO productListReqDTO);

    ProductListByCategoryResDTO getProductByCategory(@Valid ProductByCategoryReqDTO productByCategoryReqDTO);

    ProductResDTO getProductById(@Valid ProductByIdReqDTO productByIdReqDTO);


}
