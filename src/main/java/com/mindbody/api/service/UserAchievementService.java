package com.mindbody.api.service;

import com.mindbody.api.dto.*;
import com.mindbody.api.enums.AchievementActivityType;

import java.util.List;
import java.util.Map;

public interface UserAchievementService {

    void submitAchievement(SubmitUserAchievementReqDTO submitUserAchievementReqDTO);

    void claimUserAchievement(ClaimAchievementReqDTO claimAchievementReqDTO);

    UserTitlesResDTO getUserTitlesData(UserIdDTO userIdDTO);

    UserAchievementByIdDTO getUserAchievementByEnumAndUserId(UserAchievementReqDTO userAchievementReqDTO);

    Map<String, Long> getCurrentAndNextLevelMap(Long wxpEarned);

    UserBadgesResDTO getUserBadgesData(UserIdDTO userIdDTO);

    UserBorderResDTO getUserBorderData(UserIdDTO userIdDTO);

    boolean existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType achievementActivityType, Long userId);

    List<AchievementActivityType> findAchievementActivityTypeByUserIdAndIsClaimedAndIsDeleted(Long userId, boolean isClaimed,boolean isDeleted);

    SearchResultDTO<UserAllAchievementDTO> findAllUserAchievementsByUserIdForCMS(UserAchievementListReqDTO userAchievementListReqDTO);


}
