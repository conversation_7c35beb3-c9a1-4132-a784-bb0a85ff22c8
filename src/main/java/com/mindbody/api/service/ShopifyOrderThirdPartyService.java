package com.mindbody.api.service;

import com.mindbody.api.dto.shopify.*;
import jakarta.validation.Valid;

public interface ShopifyOrderThirdPartyService {
    OrderListResDTO getAllOrderList(@Valid OrderListReqDTO orderListReqDTO);

    OrdersListGraphqlResDTO getAllOrdersListShopifyAdminGraphql(@Valid OrdersListGraphqlReqDTO ordersListGraphqlReqDTO);

    TrackOrderResDTO trackOrder(String id);

    int getNumberOfProductsPurchasedByCustomer(TotalNumberOfProductsReqDTO totalNumberOfProductsReqDTO);
}
