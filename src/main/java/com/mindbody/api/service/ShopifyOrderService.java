package com.mindbody.api.service;

import com.mindbody.api.dto.shopify.*;

public interface ShopifyOrderService {

    OrderListResDTO getAllOrdersList(int limit);

    OrdersListGraphqlResDTO getAllOrdersListShopifyAdminGraphql(OrdersListGraphqlReqDTO ordersListGraphqlReqDTO);

    TrackOrderResDTO trackOrder(String id);

    int getTotalProductsPurchasedByCustomer(TotalNumberOfProductsReqDTO totalNumberOfProductsReqDTO);


}
