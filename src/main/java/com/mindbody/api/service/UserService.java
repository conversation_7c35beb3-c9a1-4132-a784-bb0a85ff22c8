package com.mindbody.api.service;

import com.mindbody.api.dto.*;
import com.mindbody.api.dto.cms.*;
import com.mindbody.api.dto.notification.FilterUserIdsReqDTO;
import com.mindbody.api.dto.notification.FilterUserIdsResDTO;
import com.mindbody.api.dto.notification.FilterUsersForNotificationReqDTO;
import com.mindbody.api.dto.notification.FilterUsersForNotificationResDTO;
import com.mindbody.api.enums.DeviceType;
import com.mindbody.api.model.EntityUser;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Optional;

public interface UserService {

    UserSignupResDTO signUpUser(UserSignupReqDTO userSignupReqDTO);

    UserSignupResDTO getUserAllInformation(Long userId);

    ViewUserDetailsResDTO getUserAllInformationForCMS(Long userId);

    void resetPassword(ResetPasswordReqDTO resetPasswordReqDTO);

    UserSignupResDTO userLogin(@Valid UserLoginDTO userLoginDTO);

    Object userSocialMediaLogin(@Valid SocialMediaLoginDTO socialMediaLoginDTO);

    void userLogout(String accessToken, DeviceUniqueIdDTO deviceUniqueIdDto);

    SearchResultDTO<UserManagementResDTO> listUserManagement(CommonListDTO commonListDTO);

    SearchResultDTO<FilterUsersForNotificationResDTO> filterUsersBasedOnNotificationFilters(FilterUsersForNotificationReqDTO filterUsersForNotificationReqDTO);

    FilterUserIdsResDTO filterUserIdsBasedOnNotificationFilters(FilterUserIdsReqDTO filterUserIdsReqDTO);

    String activeInactiveUserByAdmin(Long userId);

    ViewUserDetailsResDTO viewUserDetails(Long userId);

    void editUserDetails(EditUserDetailsReqDTO editUserDetailsReqDTO);

    HomeScreenResDTO homeScreenList(HomeScreenReqDTO homeScreenReqDTO);

    ViewMyProfileResDTO viewMyProfile();

    EditProfileResDTO editProfile(EditProfileReqDTO editProfileReqDTO);

    SearchResultDTO<FavoriteListResDTO> favoriteList(FavoriteListReqDTO favoriteListReqDTO);

    void updateUserTitleByAchievementId(UserIdAchievementIdDTO userIdAchievementIdDTO);

    void updateUserBorderByAchievementId(UserIdAchievementIdDTO userIdAchievementIdDTO);

    void updateUserMedalByAchievementId(UserIdAchievementIdDTO userIdAchievementIdDTO);

    void deleteUserAccountByAdmin(Long userId);

    void deleteUserAccount(DeactivateUserAccountReqDTO deactivateUserAccountReqDTO);

    void deactivateUserAccount(DeactivateUserAccountReqDTO deactivateUserAccountReqDTO);

//    void updateReactivationRequestPending(boolean isPending, Long userId);

//    void updateRequestStatus(RequestActionReqDTO requestActionReqDTO);

//    SearchResultDTO<UserRequestManagementResDTO> listUserRequestManagement(CommonListDTO commonListDTO);

    String reActivateUserByEmailId(ForgotPasswordReqDTO forgotPasswordReqDTO);

    EntityUser findByUserIdAndIsActiveAndIsDeleted(Long userId, boolean isActive, boolean isDeleted);

    void saveFcmTokenDetail(EntityUser entityUser, String deviceUniqueId, String fcmToken, DeviceType deviceType);

    List<UserCurrentConnectionListDTO> listCurrentConnection(Long userId);

    void addCurrentConnection(AddCurrentConnectionReqDTO addCurrentConnectionReqDTO);

    void removeCurrentConnection(RemoveCurrentConnectionReqDTO removeCurrentConnectionReqDTO);

    UpdateThemeModeResDTO updateThemeMode(UpdateThemeModeReqDTO updateThemeModeReqDTO);

}
