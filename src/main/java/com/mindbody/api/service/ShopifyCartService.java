package com.mindbody.api.service;

import com.mindbody.api.dto.shopify.*;
import jakarta.validation.Valid;

public interface ShopifyCartService {

    AddProductToCartResDTO addProductToCart(@Valid AddProductToCartReqDTO addProductToCartReqDTO);

    ViewCartResDTO viewCart(@Valid ViewCartReqDTO viewCartReqDTO);

    CreateCartWithFirstProductResDTO createCartWithFirstProduct(@Valid CreateCartWithFirstProductReqDTO createCartWithFirstProductReqDTO);

    UpdateCartResDTO updateCart(@Valid UpdateCartReqDTO updateCartReqDTO);

    RemoveProductFromCartResDTO removeProductFromCart(@Valid RemoveProductFromCartReqDTO removeProductFromCartReqDTO);
}
