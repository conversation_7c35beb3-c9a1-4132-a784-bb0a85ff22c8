package com.mindbody.api.service;

import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.cms.AddSoundReqDTO;
import com.mindbody.api.dto.cms.EditSoundReqDTO;
import com.mindbody.api.dto.cms.SoundDetailResDTO;

public interface SoundService {
    void addSound(AddSoundReqDTO addSoundReqDTO);

    void editSound(EditSoundReqDTO editSoundReqDTO);

    void deleteSound(Long soundId);

    SearchResultDTO<SoundDetailResDTO> listSoundCms(CommonListDTO commonListDTO, boolean checkIsActiveRecord);
}
