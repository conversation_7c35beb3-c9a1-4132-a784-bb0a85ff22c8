package com.mindbody.api.service.impl;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.UploadedFileNameResponseDTO;
import com.mindbody.api.enums.ImageType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.exception.EntityNotFoundException;
import com.mindbody.api.service.AWSS3Service;
import com.mindbody.api.util.Methods;
import com.mindbody.api.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Nullable;
import java.io.*;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class AWSS3ServiceImpl implements AWSS3Service {

    private static final Logger LOG = LoggerFactory.getLogger(AWSS3ServiceImpl.class);

    @Autowired
    private AmazonS3 s3Client;

    @Autowired
    private TransferManager transferManager;

    @Autowired
    private GlobalConfiguration globalConfiguration;

//    @Override
//    public String generateFileUrl(String fileName, ImageType imageType) {
//        StringBuilder strBuilder = new StringBuilder();
//
//        if (fileName == null) return null;
//        else if (imageType.equals(ImageType.profileImage)) {
//            strBuilder = new StringBuilder(globalConfiguration.getAmazonS3().getUrlUpToBucket()).append(globalConfiguration.getAmazonS3().getSubFolderProfileName());
//        }
//        strBuilder.append(fileName);
//        return strBuilder.toString();
//    }

//    @Override
//    public String generateFileUrlExportFile(String fileName) {
//        StringBuilder strBuilder;
//        strBuilder = new StringBuilder(globalConfiguration.getAmazonS3().getUrlUpToBucket()).append(globalConfiguration.getAmazonS3().getExportFolderName());
//        strBuilder.append(fileName);
//        return strBuilder.toString();
//    }

    @Override
    public UploadedFileNameResponseDTO uploadFile(MultipartFile multipartFile, String filePath, ImageType imageType) {
        UploadedFileNameResponseDTO uploadedFileName = new UploadedFileNameResponseDTO();

        File file = null;
        try {
            file = convertMultiPartToFile(multipartFile);
            String fileName = StringUtil.getRandomString(6) + "_" + new Date().getTime() + "." + Methods.getFileExtension(file.getName());
            String fileUrl = filePath + fileName;
            PutObjectRequest request = new PutObjectRequest(globalConfiguration.getAmazonS3().getBucket(), fileUrl, file);
            LOG.info("Document is uploading!!");
            s3Client.putObject(request);
            s3Client.setObjectAcl(globalConfiguration.getAmazonS3().getBucket(), fileUrl, CannedAccessControlList.Private);
            LOG.info("Document is uploaded!!");
            uploadedFileName.setImageName(fileName);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (file != null) {
                file.delete();
            }
        }
        return uploadedFileName;
    }

    @Override
    public File convertMultiPartToFile(MultipartFile file) /*throws IOException*/ {
        File convFile = new File(file.getOriginalFilename());
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(convFile);
            fos.write(file.getBytes());
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return convFile;
    }

//    private String uploadThumbnailFile(File file, ImageType imageType) {
//        try {
//            File thumbnailFile = null;
//            String fileUrl = "";
//            String thumbnailFileName = new Date().getTime() + "_thumbnail" + ".jpg";
//            Thumbnails.of(file).size(70, 70).outputFormat("jpg").toFiles(Rename.PREFIX_DOT_THUMBNAIL);
//            thumbnailFile = new File("thumbnail." + file.getName());
//
//            if (imageType.equals(ImageType.profileImage)) {
//                fileUrl = globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getSubFolderProfileName() + globalConfiguration.getAmazonS3().getThumbnailFileFolderName() + thumbnailFileName;
//            }
//            PutObjectRequest request = new PutObjectRequest(globalConfiguration.getAmazonS3().getBucket(), fileUrl, thumbnailFile);
//            s3Client.putObject(request);
//            s3Client.setObjectAcl(globalConfiguration.getAmazonS3().getBucket(), fileUrl, CannedAccessControlList.Private);
//            thumbnailFile.delete();
//            return thumbnailFileName;
//        } catch (Exception e) {
//            throw new BusinessValidationException(e);
//        }
//
//    }

    private String uploadOriginalFile(File file, ImageType imageType) {
        try {
            String fileUrl = "";
            String originalFileName = Methods.getFileName(file.getName()) + "_" + new Date().getTime() + ".jpg";

            if (imageType.equals(ImageType.profileImage)) {
                fileUrl = "user/" + originalFileName;
            }
            PutObjectRequest request = new PutObjectRequest(globalConfiguration.getAmazonS3().getBucket(), fileUrl, file);
            s3Client.putObject(request);
            s3Client.setObjectAcl(globalConfiguration.getAmazonS3().getBucket(), fileUrl, CannedAccessControlList.Private);
            return originalFileName;
        } catch (Exception e) {
            throw new BusinessValidationException(e);
        }
    }

//    @Override
//    @Async
//    public void uploadDocument(MultipartFile multipartFile, String fileName, String projectName) {
//        getDocumentName(multipartFile, globalConfiguration.getAmazonS3().getDocumentFolderName(), fileName, projectName);
//
//    }

    @javax.annotation.Nullable
    private String getDocumentName(MultipartFile multipartFile, String importFolderName, String fileName, String projectName) {
        String fileUrl = "";
        File file = null;
        try {
            file = convertMultiPartToFile(multipartFile);
            if (fileName == null) fileName = generateFileName(multipartFile.getOriginalFilename());
            fileUrl = globalConfiguration.getAmazonS3().getUploadFolderName() + importFolderName + fileName;
            PutObjectRequest request = new PutObjectRequest(globalConfiguration.getAmazonS3().getBucket(), fileUrl, file);
            s3Client.putObject(request);
            s3Client.setObjectAcl(globalConfiguration.getAmazonS3().getBucket(), fileUrl, CannedAccessControlList.Private);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (file != null && file.exists()) {
                file.delete();
            }
        }
        return fileName;
    }

    @Override
    public String generateFileName(String fileName) {
        return new Date().getTime() + "." + Methods.getFileExtension(fileName);
    }
//
//    @Override
//    public void uploadAttachments(MultipartFile multipartFile, String fileName, String projectName) {
//        getDocumentName(multipartFile, globalConfiguration.getAmazonS3().getEmailTemplateAttachmentFolderName(), fileName, projectName);
//
//    }

//    @Override
//    public void uploadBulkAttachments(List<File> fileList, String projectName) {
//        MultipleFileUpload upload = transferManager.uploadFileList(globalConfiguration.getAmazonS3().getBucket(), "", new File("."), fileList);
//        try {
//            upload.waitForCompletion();
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
//    }

    @Nullable
    public String fileUpload(File multipartFile, String importFolderName, String fileName, String moduleName, String projectName) {
        String fileUrl;
        try {
            if (multipartFile == null || !multipartFile.exists()) {
                throw new FileNotFoundException("file_parse_error");
            }
            if (StringUtil.nullOrEmpty(fileName)) fileName = generateFileName(multipartFile.getName());

            if (StringUtil.nonNullNonEmpty(moduleName))
                fileName = moduleName + "_" + generateExportFileName(multipartFile.getName());
            fileUrl = globalConfiguration.getAmazonS3().getUploadFolderName() + importFolderName + fileName;
            PutObjectRequest request = new PutObjectRequest(globalConfiguration.getAmazonS3().getBucket(), fileUrl, multipartFile);
            s3Client.putObject(request);
            s3Client.setObjectAcl(globalConfiguration.getAmazonS3().getBucket(), fileUrl, CannedAccessControlList.Private);

        } catch (Exception e) {
            LOG.error(String.format("==================Error in uploading document in S3===================%s", e.getMessage()));
            e.printStackTrace();
        }
        if (fileName == null) {
            LOG.error("=============S3  upload file returned null==========");
            return "";
        }
        return fileName;
    }

    public String generateExportFileName(String fileName) {
        return new Date() + "." + Methods.getFileExtension(fileName);
    }

    @Override
    public File convertMultiPartToFileWithRename(MultipartFile file) {

        try {
            File convFile = new File(file.getOriginalFilename());
            try {
                convFile = renameFile(convFile, generateFileName(file.getOriginalFilename()));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            FileOutputStream fos = null;
            try {
                fos = new FileOutputStream(convFile);
                fos.write(file.getBytes());
                fos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            return convFile;
        } catch (Exception e) {
            throw new EntityNotFoundException("common_fail");
        }
    }

    public static File renameFile(File toBeRenamed, String new_name) throws IOException {
        //need to be in the same path
        File fileWithNewName = new File(toBeRenamed.getParent(), new_name);
        if (fileWithNewName.exists()) {
            throw new IOException("file exists");
        }
        // Rename file (or directory)
        boolean success = toBeRenamed.renameTo(fileWithNewName);
        if (!success) {
            // File was not successfully renamed
        }
        return fileWithNewName;
    }

    @Override
    public void deleteFile(String fileName, ImageType imageType) {
        try {
            if (StringUtil.nullOrEmpty(fileName)) return;
            LOG.info("File is getting deleted!!");
            s3Client.deleteObject(new DeleteObjectRequest(globalConfiguration.getAmazonS3().getBucket(), fileName));
            LOG.info("File is deleted!!");
        } catch (AmazonServiceException ex) {
            LOG.error("error [" + ex.getMessage() + "] occurred while removing [" + fileName + "] ");
        }
    }

    @Override
    public void deleteFiles(List<String> fileList) {
        LOG.info("Documents are getting deleted!!");
        try {
            List<DeleteObjectsRequest.KeyVersion> keys = new ArrayList<DeleteObjectsRequest.KeyVersion>();
            for (String fileName : fileList) {
                keys.add(new DeleteObjectsRequest.KeyVersion(fileName));
            }
            s3Client.deleteObjects(new DeleteObjectsRequest(globalConfiguration.getAmazonS3().getBucket()).withKeys(keys));
            LOG.info("Documents are deleted successfully!!");
        } catch (AmazonServiceException ex) {
            LOG.error("error [" + ex.getMessage() + "] occurred while removing [" + fileList + "] ");
        }
    }

    @Override
    public String getFileExtension(String fileName) {
        if (fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0)
            return fileName.substring(fileName.lastIndexOf(".") + 1);
        else return "";
    }

    @Override
    public String generateUploadId(String filePath, String contentType) {
        try {
            InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(globalConfiguration.getAmazonS3().getBucket(), filePath)
                    .withObjectMetadata(new ObjectMetadata() {{
                        setContentType(contentType);
                    }});
            InitiateMultipartUploadResult initResponse = s3Client.initiateMultipartUpload(initRequest);
            return initResponse.getUploadId();
        } catch (AmazonS3Exception e) {
            throw new RuntimeException("Failed to generate the uploadId: " + e.getMessage(), e);
        }
    }

    @Override
    public void completeMultipartUpload(String filePath, String uploadId, List<PartETag> partETags) {
        try {
            CompleteMultipartUploadRequest completeRequest = new CompleteMultipartUploadRequest(
                    globalConfiguration.getAmazonS3().getBucket(),
                    filePath,
                    uploadId,
                    partETags
            );

            s3Client.completeMultipartUpload(completeRequest);
            System.out.println("Multipart upload completed successfully for file: " + filePath);
        } catch (AmazonS3Exception e) {
            throw new RuntimeException("Failed to complete multipart upload for file " + filePath, e);
        }
    }

    @Override
    public void abortMultipartUpload(String filePath, String uploadId) {
        try {
            AbortMultipartUploadRequest abortRequest = new AbortMultipartUploadRequest(
                    globalConfiguration.getAmazonS3().getBucket(),
                    filePath,
                    uploadId
            );
            s3Client.abortMultipartUpload(abortRequest);
            System.out.println("Multipart upload aborted successfully for file: " + filePath);
        } catch (AmazonS3Exception e) {
            throw new RuntimeException("Failed to abort multipart upload for file " + filePath, e);
        }
    }


//    @Override
//    public URL downloadFile(String fileName, String generateFileName, ImageType type) {
//        String fileUrl = "";
//        GeneratePresignedUrlRequest req = null;
//        if (type.equals(ImageType.document)) {
//            fileUrl = globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getDocumentFolderName() + fileName;
//            Calendar calendar = Calendar.getInstance();
//            calendar.add(Calendar.MINUTE, 15);
//            req = new GeneratePresignedUrlRequest(globalConfiguration.getAmazonS3().getBucket(), fileUrl);
//            ResponseHeaderOverrides overrides = new ResponseHeaderOverrides();
//            overrides.setContentDisposition("attachment; filename=" + generateFileName);
//            req.setResponseHeaders(overrides);
//            req.setExpiration(calendar.getTime());
//        }
//        return s3Client.generatePresignedUrl(req);
//    }


    @Override
    public String generatePreSignedUrl(String filePath, String uploadId, int partNumber) {
        try {
            GeneratePresignedUrlRequest preSignedRequest = new GeneratePresignedUrlRequest(globalConfiguration.getAmazonS3().getBucket(), filePath)
                    .withMethod(HttpMethod.PUT)
                    .withExpiration(getExpiration());
            preSignedRequest.addRequestParameter("partNumber", String.valueOf(partNumber));
            preSignedRequest.addRequestParameter("uploadId", uploadId);
            return s3Client.generatePresignedUrl(preSignedRequest).toString();
        } catch (AmazonS3Exception e) {
            throw new RuntimeException("Failed to generate preSigned URL for part " + partNumber, e);
        }
    }

    private Date getExpiration() {
        Date expiration = new Date();
        long expTimeMillis = expiration.getTime();
        // 15 minutes for each URL
        expTimeMillis += 1000 * 60 * 15;
        expiration.setTime(expTimeMillis);
        return expiration;
    }

    @Async
    @Override
    public void writeByte(byte[] bytes, String fileName, String s3FileName, String projectName) {
//        String FILEPATH = "/home/<USER>" + "/" + fileName;
//         File file = new File(FILEPATH);
        OutputStream os = null;
        File file = null;
        String s = null;
        try {
            // Initialize a pointer
            // in file using OutputStream
            file = new File(fileName);
            os = new FileOutputStream(file);

            // Starts writing the bytes in it
            os.write(bytes);
            LOG.info("Successfully byte inserted");

            // Close the file
            os.close();

            // TODO - upload newly created file to s3 if needed
//            uploadConversationAttachmentDocument(file, s3FileName, projectName);
        } catch (Exception e) {
            LOG.info("Exception: " + e);

        } finally {
            try {
                if (os != null) os.close();
            } catch (Exception ignored) {

            }

        }
    }

    private boolean isFileSizeValid(File file) {
        long fileSize = file.length();
        //here check the validation is file size must be grater than 0.1 byte and less than or equal to 7 mb
        double maximumSize = fileSize / 1024.0 * 1024.0;
        return (file.length() > 100 && maximumSize >= 0.0001 && maximumSize <= 7340032.0);
    }

    private boolean isImageExtensionValid(File file) throws IOException {
        String fileType = Files.probeContentType(file.toPath());
        if (fileType != null && fileType.split("/")[0].equals("image")) {
            String formatName = fileType.split("/")[1].toLowerCase();
            return formatName.equals("png") || formatName.equals("jpg") || formatName.equals("jpeg");
        } else {
            throw new BusinessValidationException("invalid_file");
        }
    }

    @Override
    public UploadedFileNameResponseDTO uploadFileType(File file, String filePath) {
        UploadedFileNameResponseDTO uploadedFileName = new UploadedFileNameResponseDTO();

        try {
            String fileName = StringUtil.getRandomString(6) + "_" + new Date().getTime() + "." + Methods.getFileExtension(file.getName());
            String fileUrl = filePath + fileName;
            PutObjectRequest request = new PutObjectRequest(globalConfiguration.getAmazonS3().getBucket(), fileUrl, file);
            LOG.info("Document is uploading!!");
            s3Client.putObject(request);
            s3Client.setObjectAcl(globalConfiguration.getAmazonS3().getBucket(), fileUrl, CannedAccessControlList.Private);
            LOG.info("Document is uploaded!!");
            uploadedFileName.setImageName(fileName);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (file != null) {
                file.delete();
            }
        }
        return uploadedFileName;
    }

}
