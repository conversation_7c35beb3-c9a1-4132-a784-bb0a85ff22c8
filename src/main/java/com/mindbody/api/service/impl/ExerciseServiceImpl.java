package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.UploadedFileNameResponseDTO;
import com.mindbody.api.dto.cms.AddExerciseDTO;
import com.mindbody.api.dto.cms.CMSListExerciseResDTO;
import com.mindbody.api.dto.cms.EditExerciseDTO;
import com.mindbody.api.dto.cms.ExerciseResDTO;
import com.mindbody.api.enums.ImageType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.ExerciseMapper;
import com.mindbody.api.model.EntityExercise;
import com.mindbody.api.model.EntityWorkoutPlanExercise;
import com.mindbody.api.repository.ExerciseRepository;
import com.mindbody.api.repository.WorkoutPlanExerciseRepository;
import com.mindbody.api.service.AWSS3Service;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.ExerciseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.util.StringUtil;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class ExerciseServiceImpl extends BaseService implements ExerciseService {


    private final ExerciseRepository exerciseRepository;

    private final WorkoutPlanExerciseRepository workoutPlanExerciseRepository;

    private final GlobalConfiguration globalConfiguration;

    private final AWSS3Service awss3Service;

    private final ExerciseMapper exerciseMapper;


    public ExerciseServiceImpl(MessageService messageService, ExerciseRepository exerciseRepository, WorkoutPlanExerciseRepository workoutPlanExerciseRepository, GlobalConfiguration globalConfiguration, AWSS3Service awss3Service, ExerciseMapper exerciseMapper) {
        super(messageService);
        this.exerciseRepository = exerciseRepository;
        this.workoutPlanExerciseRepository = workoutPlanExerciseRepository;
        this.globalConfiguration = globalConfiguration;
        this.awss3Service = awss3Service;
        this.exerciseMapper = exerciseMapper;
    }


    @Override
    public void addExercise(AddExerciseDTO addExerciseDTO) {
        if (exerciseRepository.existsByTitleAndIsDeleted(addExerciseDTO.getTitle().trim(), false)) {
            throw new BusinessValidationException("exercise_already_exist");
        }
        validateDTO(addExerciseDTO);
        EntityExercise entityExercise = exerciseMapper.toModel(addExerciseDTO);
//        UploadedFileNameResponseDTO uploadedAudioFile = awss3Service.uploadFile(addExerciseDTO.getExerciseVideo(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseVideoFolderName(), ImageType.exerciseVideo);
//        entityExercise.setExerciseVideo(uploadedAudioFile.getImageName());

        UploadedFileNameResponseDTO uploadedAudioThumbnail = awss3Service.uploadFile(addExerciseDTO.getExerciseThumbnailImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseThumbnailFolderName(), ImageType.exerciseThumbnail);
        entityExercise.setExerciseThumbnailImage(uploadedAudioThumbnail.getImageName());

        exerciseRepository.save(entityExercise);
    }

    @Override
    public void editExercise(EditExerciseDTO editExerciseDTO) {
        if (exerciseRepository.existsByExerciseIdNotAndTitleAndIsDeleted(editExerciseDTO.getExerciseId(), editExerciseDTO.getTitle().trim(), false)) {
            throw new BusinessValidationException("exercise_already_exist");
        }
        validateVideoFileConditions(editExerciseDTO);
        EntityExercise entityExercise = exerciseRepository.findByExerciseIdAndIsDeleted(editExerciseDTO.getExerciseId(), false);
        if (Objects.isNull(entityExercise)) {
            throw new BusinessValidationException("exercise_not_found");
        }
        uploadNewFileAndDeleteOld(editExerciseDTO, entityExercise);
        entityExercise.setTitle(editExerciseDTO.getTitle());
        entityExercise.setDescription(editExerciseDTO.getDescription());

        exerciseRepository.save(entityExercise);
    }

    @Override
    public SearchResultDTO<ExerciseResDTO> listExerciseCms(CommonListDTO commonListDTO) {
        Sort sort = null;
        if (commonListDTO.getSortBy() != null && commonListDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(commonListDTO.getSortBy().getDirection(), commonListDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(commonListDTO.getPage().getPageId(), commonListDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());

        List<ExerciseResDTO> exerciseResList = new ArrayList<>();
        long pageCount = 0L;
        pageCount = exerciseRepository.countExercise(commonListDTO.getQueryToSearch(), false);
        if (pageCount > 0) {
            String exerciseVideoPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseVideoFolderName();
            String exerciseThumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseThumbnailFolderName();
            exerciseResList = exerciseRepository.findAllExerciseCms(commonListDTO.getQueryToSearch(), pageable, exerciseVideoPath, exerciseThumbnailImagePath, false);
        }
        return new SearchResultDTO<>(exerciseResList, pageCount, commonListDTO.getPage().getLimit());
    }

    @Override
    public List<CMSListExerciseResDTO> listExerciseToAddWorkoutPlanCMS() {
        String exerciseVideoPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseVideoFolderName();
        String exerciseThumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseThumbnailFolderName();
        return exerciseRepository.findAllExerciseToAddWorkoutPlanCMS(true, false, exerciseVideoPath, exerciseThumbnailImagePath);
    }

    @Override
    public void deleteExercise(Long exerciseId) {
        EntityExercise entityExercise = exerciseRepository.findByExerciseIdAndIsDeleted(exerciseId, false);
        if (Objects.isNull(entityExercise)) {
            throw new BusinessValidationException("exercise_not_found");
        }
        List<EntityWorkoutPlanExercise> workoutPlanExerciseList = workoutPlanExerciseRepository.findAllByExerciseId(exerciseId);
        if (!workoutPlanExerciseList.isEmpty()) {
            throw new BusinessValidationException("exercise_delete_error");
        }
        entityExercise.setDeleted(true);
        entityExercise.setActive(false);
        exerciseRepository.save(entityExercise);
    }


    @Override
    public boolean activeInactiveExercise(Long exerciseId) {
        boolean isActive = false;
        EntityExercise entityExercise = exerciseRepository.findByExerciseIdAndIsDeleted(exerciseId, false);
        if (Objects.isNull(entityExercise)) {
            throw new BusinessValidationException("exercise_not_found");
        }
        if (entityExercise.isActive()) {
            List<EntityWorkoutPlanExercise> workoutPlanExerciseList = workoutPlanExerciseRepository.findAllByExerciseId(exerciseId);
            if (!workoutPlanExerciseList.isEmpty()) {
                throw new BusinessValidationException("exercise_inactive_error");
            }
            isActive = true;
        }
        entityExercise.setActive(!entityExercise.isActive());
        exerciseRepository.save(entityExercise);
        return isActive;
    }


    private void uploadNewFileAndDeleteOld(EditExerciseDTO editExerciseDTO, EntityExercise entityExercise) {
        List<String> deleteImageList = new ArrayList<>();
        if (StringUtil.nullOrEmpty(editExerciseDTO.getExistingExerciseVideo()) && StringUtil.nonNullNonEmpty(entityExercise.getExerciseVideo())) {
            deleteImageList.add(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseVideoFolderName() + entityExercise.getExerciseVideo());
            entityExercise.setExerciseVideo(null);
        }
        if (StringUtil.nullOrEmpty(editExerciseDTO.getExistingExerciseThumbnailImage()) && StringUtil.nonNullNonEmpty(entityExercise.getExerciseThumbnailImage())) {
            deleteImageList.add(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseThumbnailFolderName() + entityExercise.getExerciseThumbnailImage());
            entityExercise.setExerciseThumbnailImage(null);
        }
        if (StringUtil.nonNullNonEmpty(editExerciseDTO.getExerciseVideo())) {
//            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(editExerciseDTO.getExerciseVideo(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseVideoFolderName(), ImageType.exerciseVideo);
            entityExercise.setExerciseVideo(editExerciseDTO.getExerciseVideo());
        }
        if (editExerciseDTO.getExerciseThumbnailImage() != null) {
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(editExerciseDTO.getExerciseThumbnailImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseThumbnailFolderName(), ImageType.exerciseThumbnail);
            entityExercise.setExerciseThumbnailImage(uploadedFileName.getImageName());
        }
        if (deleteImageList != null && !deleteImageList.isEmpty()) {
            awss3Service.deleteFiles(deleteImageList);
        }
    }

    private void validateVideoFileConditions(EditExerciseDTO editExerciseDTO) {
        if (Objects.nonNull(editExerciseDTO.getExerciseVideo()) && StringUtil.nonNullNonEmpty(editExerciseDTO.getExistingExerciseVideo())) {
            throw new BusinessValidationException("exercise_video_file_error");
        }
        if (Objects.nonNull(editExerciseDTO.getExerciseThumbnailImage()) && StringUtil.nonNullNonEmpty(editExerciseDTO.getExistingExerciseThumbnailImage())) {
            throw new BusinessValidationException("exercise_thumbnail_audio_file_error");
        }
        if (Objects.isNull(editExerciseDTO.getExerciseVideo()) && StringUtil.nullOrEmpty(editExerciseDTO.getExistingExerciseVideo())) {
            throw new BusinessValidationException("exercise_video_file_required");
        }
        if (Objects.isNull(editExerciseDTO.getExerciseThumbnailImage()) && StringUtil.nullOrEmpty(editExerciseDTO.getExistingExerciseThumbnailImage())) {
            throw new BusinessValidationException("exercise_thumbnail_image_required");
        }
    }


    public boolean validateDTO(AddExerciseDTO addExerciseDTO) {
        if (Objects.isNull(addExerciseDTO.getExerciseVideo())) {
            throw new BusinessValidationException("exercise_video_file_required");
        }
        if (Objects.isNull(addExerciseDTO.getExerciseThumbnailImage())) {
            throw new BusinessValidationException("exercise_thumbnail_image_required");
        }

        String[] validExtensions = {"mp4", "mov", "avi", "mkv", "wmv", "flv", "webm", "3gp", "mpeg", "mpg"};
        String extension = awss3Service.getFileExtension(addExerciseDTO.getExerciseVideo());

        for (String ext : validExtensions) {
            if (ext.equalsIgnoreCase(extension)) {
                return true;
            }
        }

        throw new BusinessValidationException("video_file_extension_not_supported");
    }


}
