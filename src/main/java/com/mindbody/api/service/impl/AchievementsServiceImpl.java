package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.AchievementDTO;
import com.mindbody.api.dto.AchievementsBySetNameReq;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.UploadedFileNameResponseDTO;
import com.mindbody.api.dto.achievement.*;
import com.mindbody.api.dto.cms.AchievementListReqDTO;
import com.mindbody.api.dto.cms.AchievementListResDTO;
import com.mindbody.api.dto.cms.EditAchievementReqDTO;
import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.enums.ImageType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.*;
import com.mindbody.api.repository.*;
import com.mindbody.api.service.AWSS3Service;
import com.mindbody.api.service.AchievementsService;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.StringUtil;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class AchievementsServiceImpl extends BaseService implements AchievementsService {

    private final AchievementTitleRepository achievementTitleRepository;

    private final AchievementBorderRepository achievementBorderRepository;

    private final AchievementMedalsRepository achievementMedalsRepository;

    private final AchievementsRepository achievementsRepository;

    private final UserAchievementsRepository userAchievementsRepository;

    private final AchievementLevelUpRepository achievementLevelUpRepository;

    private final UserPointsTrackerRepository userPointsTrackerRepository;

    private final AWSS3Service awss3Service;

    private final GlobalConfiguration globalConfiguration;



    public AchievementsServiceImpl(MessageService messageService,
                                   AchievementTitleRepository achievementTitleRepository,
                                   AchievementBorderRepository achievementBorderRepository,
                                   AchievementMedalsRepository achievementMedalsRepository,
                                   AchievementsRepository achievementsRepository,
                                   UserAchievementsRepository userAchievementsRepository,
                                   AchievementLevelUpRepository achievementLevelUpRepository,
                                   UserPointsTrackerRepository userPointsTrackerRepository,
                                   AWSS3Service awss3Service,
                                   GlobalConfiguration globalConfiguration) {
        super(messageService);
        this.achievementTitleRepository = achievementTitleRepository;
        this.achievementBorderRepository = achievementBorderRepository;
        this.achievementMedalsRepository = achievementMedalsRepository;
        this.achievementsRepository = achievementsRepository;
        this.userAchievementsRepository = userAchievementsRepository;
        this.achievementLevelUpRepository = achievementLevelUpRepository;
        this.userPointsTrackerRepository = userPointsTrackerRepository;
        this.awss3Service = awss3Service;
        this.globalConfiguration = globalConfiguration;
    }

    @Override
    public SearchResultDTO<AchievementListResDTO> listAchievements(AchievementListReqDTO achievementListReqDTO) {
        AchievementCategoryType achievementCategoryType = null;
        boolean isAchievementCategoryTypeFilter = false;
        if (achievementListReqDTO.getFilters() != null && StringUtil.nonNullNonEmpty(achievementListReqDTO.getFilters().getAchievementCategoryType())) {
            try {
                achievementCategoryType = AchievementCategoryType.valueOf(achievementListReqDTO.getFilters().getAchievementCategoryType());
            } catch (Exception e) {
                throw new BusinessValidationException("invalid_achievement_category_type");
            }
            isAchievementCategoryTypeFilter = true;
        }
        Sort sort = null;
        if (achievementListReqDTO.getSortBy() != null && achievementListReqDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(achievementListReqDTO.getSortBy().getDirection(), achievementListReqDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(achievementListReqDTO.getPage().getPageId(), achievementListReqDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());

        List<AchievementListResDTO> achievementListResDTOList = new ArrayList<>();
        long pageCount = 0L;
        pageCount = achievementsRepository.countAchievements(achievementListReqDTO.getQueryToSearch().toLowerCase(), achievementCategoryType, isAchievementCategoryTypeFilter, false);
        if (pageCount > 0) {
            String badgeImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBadgeFolderName();
            String borderImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBorderFolderName();
            achievementListResDTOList = achievementsRepository.findAllAchievements(achievementListReqDTO.getQueryToSearch().toLowerCase(), pageable, achievementCategoryType, badgeImagePath, borderImagePath, isAchievementCategoryTypeFilter, false);
        }
        return new SearchResultDTO<>(achievementListResDTOList, pageCount, achievementListReqDTO.getPage().getLimit());
    }

    @Override
    public void editAchievement(EditAchievementReqDTO editAchievementReqDTO, MultipartFile borderImage, MultipartFile badgeImage) {
        EntityAchievements entityAchievements = achievementsRepository.findByAchievementsIdAndIsDeleted(editAchievementReqDTO.getAchievementId(), false);
        if (Objects.isNull(entityAchievements)) {
            throw new BusinessValidationException("achievement_not_found");
        }

        /** Handle Border Updates **/
        handleBorderUpdates(editAchievementReqDTO, borderImage, entityAchievements);

        /** Handle Medal Updates **/
        handleMedalUpdates(editAchievementReqDTO, entityAchievements);

        /** Handle Badge Image Updates **/
        handleBadgeImageUpdates(editAchievementReqDTO, badgeImage, entityAchievements);

        achievementsRepository.save(entityAchievements);
    }

    /** Helper method to handle border updates **/
    private void handleBorderUpdates(EditAchievementReqDTO editAchievementReqDTO, MultipartFile borderImage, EntityAchievements entityAchievements) {
        // Case 1: Border ID is provided
        if (editAchievementReqDTO.getAchievementBorderId() != null) {
            EntityAchievementBorder entityAchievementBorder = achievementBorderRepository
                    .findById(editAchievementReqDTO.getAchievementBorderId())
                    .orElseThrow(() -> new BusinessValidationException("border_not_found"));
            entityAchievements.setEntityAchievementBorder(entityAchievementBorder);
            
            // Case 1.1: Border ID and new border image provided
            if (borderImage != null && !borderImage.isEmpty()) {
                updateBorderImage(entityAchievements, borderImage);
            }
            // Case 1.2: Border ID provided but no new image - keep existing image
        } 
        // Case 2: Border ID is null or explicit removal requested
        else {
            // Case 2.1: No border ID but image provided - throw error
            if (borderImage != null && !borderImage.isEmpty()) {
                throw new BusinessValidationException("achievement_border_id_required");
            }
            // Case 2.2: Both border ID is null or explicit removal requested
            if (editAchievementReqDTO.getAchievementBorderId() == null || 
                Boolean.TRUE.equals(editAchievementReqDTO.getRemoveBorderImage())) {
                entityAchievements.setEntityAchievementBorder(null);
                if (!StringUtil.nullOrEmpty(entityAchievements.getBorderImage())) {
                    deleteBorderImage(entityAchievements);
                    entityAchievements.setBorderImage(null);
                }
            }
        }
    }

    /** Helper method to handle medal updates **/
    private void handleMedalUpdates(EditAchievementReqDTO editAchievementReqDTO, EntityAchievements entityAchievements) {
        if (editAchievementReqDTO.getAchievementMedalId() != null) {
            EntityAchievementMedals entityAchievementMedals = achievementMedalsRepository
                    .findById(editAchievementReqDTO.getAchievementMedalId())
                    .orElseThrow(() -> new BusinessValidationException("medal_not_found"));
            entityAchievements.setEntityAchievementMedals(entityAchievementMedals);
        } else {
            // If medal ID is null, remove the medal association
            entityAchievements.setEntityAchievementMedals(null);
        }
    }

    /** Helper method to handle badge image updates **/
    private void handleBadgeImageUpdates(EditAchievementReqDTO editAchievementReqDTO, MultipartFile badgeImage, EntityAchievements entityAchievements) {
        // Case 1: New badge image provided - update the image
        if (badgeImage != null && !badgeImage.isEmpty()) {
            updateBadgeImage(entityAchievements, badgeImage);
        }
        // Case 2: Explicit removal requested
        else if (Boolean.TRUE.equals(editAchievementReqDTO.getRemoveBadgeImage())) {
            if (!StringUtil.nullOrEmpty(entityAchievements.getBadgeImage())) {
                deleteBadgeImage(entityAchievements);
                entityAchievements.setBadgeImage(null);
            }
        }
        // Case 3: No badge image provided and no removal requested - keep existing image
    }

    /** Helper method to update border image **/
    private void updateBorderImage(EntityAchievements entityAchievements, MultipartFile borderImage) {
        try {
            // Delete existing border image if present
            if (!StringUtil.nullOrEmpty(entityAchievements.getBorderImage())) {
                deleteBorderImage(entityAchievements);
            }
            // Upload new border image
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(
                borderImage, 
                globalConfiguration.getAmazonS3().getUploadFolderName() + 
                globalConfiguration.getAmazonS3().getAchievementBorderFolderName(), 
                ImageType.borderImage
            );
            entityAchievements.setBorderImage(uploadedFileName.getImageName());
        } catch (Exception e) {
            throw new BusinessValidationException("failed_to_update_border_image");
        }
    }

    /** Helper method to update badge image **/
    private void updateBadgeImage(EntityAchievements entityAchievements, MultipartFile badgeImage) {
        try {
            // Delete existing badge image if present
            if (!StringUtil.nullOrEmpty(entityAchievements.getBadgeImage())) {
                deleteBadgeImage(entityAchievements);
            }
            // Upload new badge image
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(
                badgeImage, 
                globalConfiguration.getAmazonS3().getUploadFolderName() + 
                globalConfiguration.getAmazonS3().getAchievementBadgeFolderName(), 
                ImageType.badgeImage
            );
            entityAchievements.setBadgeImage(uploadedFileName.getImageName());
        } catch (Exception e) {
            throw new BusinessValidationException("failed_to_update_badge_image");
        }
    }

    /** Helper method to delete border image **/
    private void deleteBorderImage(EntityAchievements entityAchievements) {
        try {
            awss3Service.deleteFile(
                globalConfiguration.getAmazonS3().getUploadFolderName() + 
                globalConfiguration.getAmazonS3().getAchievementBorderFolderName() + 
                entityAchievements.getBorderImage(), 
                ImageType.borderImage
            );
        } catch (Exception e) {
            throw new BusinessValidationException("failed_to_delete_border_image");
        }
    }

    /** Helper method to delete badge image **/
    private void deleteBadgeImage(EntityAchievements entityAchievements) {
        try {
            awss3Service.deleteFile(
                globalConfiguration.getAmazonS3().getUploadFolderName() + 
                globalConfiguration.getAmazonS3().getAchievementBadgeFolderName() + 
                entityAchievements.getBadgeImage(), 
                ImageType.badgeImage
            );
        } catch (Exception e) {
            throw new BusinessValidationException("failed_to_delete_badge_image");
        }
    }

    @Override
    @Transactional
    public AchievementCategoryListResDTO getAchievementSubCategoryListForCategorySelectedByUser(AchievementCategoryListReqDTO achievementCategoryListReqDTO) {
        AchievementCategoryType selectedCategoryType = AchievementCategoryType.valueOf(achievementCategoryListReqDTO.getAchievementCategoryType());
        Long userId = achievementCategoryListReqDTO.getUserId();

        // Fetch achievements based on category type
        List<EntityAchievements> achievements = achievementsRepository.findByAchievementCategoryTypeAndIsDeleted(selectedCategoryType, false);

        // Force initialization of lazy-loaded related entities
        achievements.forEach(achievement -> {
            // Ensure related entities are initialized
            if (achievement.getEntityAudioPlaylist() != null) {
                achievement.getEntityAudioPlaylist().getAudioPlaylistName();  // Accessing a property forces loading of the entity
            }
            if (achievement.getEntityWorkoutPlaylist() != null) {
                achievement.getEntityWorkoutPlaylist().getWorkoutPlaylistName();
            }
            if (achievement.getEntityWarriorSet() != null) {
                achievement.getEntityWarriorSet().getWarriorSetName();
            }
        });

        // Fetch user achievements
//        List<EntityUserAchievements> userAchievements = userAchievementsRepository.findByUserId(userId);

        // Map user achievements by achievement ID for quick lookup
//        Map<Long, Integer> userCompletedMedalsMap = userAchievements.stream()
//                .collect(Collectors.toMap(
//                        userAchievement -> userAchievement.getEntityAchievements().getAchievementsId(),
//                        EntityUserAchievements::getCompletedMedals
//                ));

        // Map achievements to response DTO
        AchievementCategoryListResDTO response = new AchievementCategoryListResDTO();
        List<AchievementCategoryResponse> categoryResponses = new ArrayList<>();


        // Group achievements by category type and build the response
        achievements.stream()
                .collect(Collectors.groupingBy(EntityAchievements::getAchievementCategoryType))
                .forEach((categoryType, categoryAchievements) -> {
                    AchievementCategoryResponse categoryResponse = new AchievementCategoryResponse();
                    categoryResponse.setAchievementCategoryType(categoryType.name());

                    // Initialize maps for subcategory types
                    Map<String, AchievementSubCategoryResponse> subCategoryMap = new HashMap<>();

                    // Iterate through achievements and map to subcategories
                    categoryAchievements.forEach(achievement -> {
                        AchievementSubCategoryResponse subCategoryResponse = null;

                        if (categoryType == AchievementCategoryType.Mind && achievement.getMindSubCategoryType() != null) {
                            String subCategoryKey = String.valueOf(achievement.getMindSubCategoryType());
                            subCategoryResponse = subCategoryMap.computeIfAbsent(subCategoryKey, k -> {
                                AchievementSubCategoryResponse newSubCategoryResponse = new AchievementSubCategoryResponse();
                                newSubCategoryResponse.setSubCategoryType(subCategoryKey);
                                newSubCategoryResponse.setAchievementSetResponseList(new ArrayList<>());
                                return newSubCategoryResponse;
                            });

                        } else if (categoryType == AchievementCategoryType.Body && achievement.getBodySubCategoryType() != null) {
                            String subCategoryKey = String.valueOf(achievement.getBodySubCategoryType());
                            subCategoryResponse = subCategoryMap.computeIfAbsent(subCategoryKey, k -> {
                                AchievementSubCategoryResponse newSubCategoryResponse = new AchievementSubCategoryResponse();
                                newSubCategoryResponse.setSubCategoryType(subCategoryKey);
                                newSubCategoryResponse.setAchievementSetResponseList(new ArrayList<>());
                                return newSubCategoryResponse;
                            });

                        } else if (categoryType == AchievementCategoryType.Warrior && achievement.getWarriorSubCategoryType() != null) {
                            String subCategoryKey = String.valueOf(achievement.getWarriorSubCategoryType());
                            subCategoryResponse = subCategoryMap.computeIfAbsent(subCategoryKey, k -> {
                                AchievementSubCategoryResponse newSubCategoryResponse = new AchievementSubCategoryResponse();
                                newSubCategoryResponse.setSubCategoryType(subCategoryKey);
                                newSubCategoryResponse.setAchievementSetResponseList(new ArrayList<>());
                                return newSubCategoryResponse;
                            });
                        }

                        // Add achievement details to subcategory's achievementSetResponseList
                        if (subCategoryResponse != null) {
                            // Create a unique identifier for each set based on the playlist and warrior set names
                            String uniqueIdentifier =
                                    (achievement.getEntityAudioPlaylist() != null ? achievement.getEntityAudioPlaylist().getAudioPlaylistName() : "") +
                                            (achievement.getEntityWorkoutPlaylist() != null ? achievement.getEntityWorkoutPlaylist().getWorkoutPlaylistName() : "") +
                                            (achievement.getEntityWarriorSet() != null ? achievement.getEntityWarriorSet().getWarriorSetName() : "");

                            // Check if the unique identifier already exists in the subCategoryResponse's list
                            boolean isDuplicate = subCategoryResponse.getAchievementSetResponseList().stream()
                                    .anyMatch(existingSetResponse -> {
                                        String existingUniqueIdentifier =
                                                (existingSetResponse.getAudioPlaylistName() != null ? existingSetResponse.getAudioPlaylistName() : "") +
                                                        (existingSetResponse.getWorkoutPlaylistName() != null ? existingSetResponse.getWorkoutPlaylistName() : "") +
                                                        (existingSetResponse.getWarriorSetName() != null ? existingSetResponse.getWarriorSetName() : "");
                                        return existingUniqueIdentifier.equals(uniqueIdentifier);
                                    });

                            if (!isDuplicate) {
                                AchievementSetResponse setResponse = new AchievementSetResponse();
                                if (achievement.getEntityAudioPlaylist() != null) {
                                    setResponse.setAudioPlaylistName(achievement.getEntityAudioPlaylist().getAudioPlaylistName());
                                    setResponse.setSetId(achievement.getEntityAudioPlaylist().getAudioPlaylistId());
                                } else if (achievement.getEntityWorkoutPlaylist() != null) {
                                    setResponse.setWorkoutPlaylistName(achievement.getEntityWorkoutPlaylist().getWorkoutPlaylistName());
                                    setResponse.setSetId(achievement.getEntityWorkoutPlaylist().getWorkoutPlaylistId());
                                } else if (achievement.getEntityWarriorSet() != null) {
                                    setResponse.setWarriorSetName(achievement.getEntityWarriorSet().getWarriorSetName());
                                    setResponse.setSetId(achievement.getEntityWarriorSet().getWarriorSetId());
                                }

                                // Calculate totalMedals based on set names
                                int totalMedals = 0;
                                int userCompletedMedals = 0;
                                boolean isNewAvailable = false;
                                if (achievement.getEntityAudioPlaylist() != null) {
                                    totalMedals = achievementsRepository.countByEntityAudioPlaylist_AudioPlaylistId(achievement.getEntityAudioPlaylist().getAudioPlaylistId());
                                    userCompletedMedals = userAchievementsRepository.countByEntityAudioPlaylist_AudioPlaylistIdAndUserId(achievement.getEntityAudioPlaylist().getAudioPlaylistId(), userId);
                                    isNewAvailable = userAchievementsRepository.existsByEntityAudioPlaylist_AudioPlaylistIdAndUserIdAndIsClaimedAndIsDeleted(achievement.getEntityAudioPlaylist().getAudioPlaylistId(), userId, false, false);
                                } else if (achievement.getEntityWorkoutPlaylist() != null) {
                                    totalMedals = achievementsRepository.countByEntityWorkoutPlaylist_WorkoutPlaylistId(achievement.getEntityWorkoutPlaylist().getWorkoutPlaylistId());
                                    userCompletedMedals = userAchievementsRepository.countByEntityWorkoutPlaylist_WorkoutPlaylistIdAndUserId(achievement.getEntityWorkoutPlaylist().getWorkoutPlaylistId(), userId);
                                    isNewAvailable = userAchievementsRepository.existsByEntityWorkoutPlaylist_WorkoutPlaylistIdAndUserIdAndIsClaimedAndIsDeleted(achievement.getEntityWorkoutPlaylist().getWorkoutPlaylistId(), userId, false, false);
                                } else if (achievement.getEntityWarriorSet() != null) {
                                    totalMedals = achievementsRepository.countByEntityWarriorSet_WarriorSetId(achievement.getEntityWarriorSet().getWarriorSetId());
                                    userCompletedMedals = userAchievementsRepository.countByEntityWarriorSet_WarriorSetIdAndUserId(achievement.getEntityWarriorSet().getWarriorSetId(), userId);
                                    isNewAvailable = userAchievementsRepository.existsByEntityWarriorSet_WarriorSetIdAndUserIdAndIsClaimedAndIsDeleted(achievement.getEntityWarriorSet().getWarriorSetId(), userId, false, false);
                                }

                                if (StringUtil.nonNullNonEmpty(setResponse.getAudioPlaylistName())) {
                                    setResponse.setSetName(setResponse.getAudioPlaylistName());
                                } else if (StringUtil.nonNullNonEmpty(setResponse.getWorkoutPlaylistName())) {
                                    setResponse.setSetName(setResponse.getWorkoutPlaylistName());
                                } else if (StringUtil.nonNullNonEmpty(setResponse.getWarriorSetName())) {
                                    setResponse.setSetName(setResponse.getWarriorSetName());
                                }

                                setResponse.setTotalMedals(totalMedals);
                                setResponse.setUserCompletedMedals(userCompletedMedals);
                                setResponse.setIsNewAvailable(isNewAvailable);
                                subCategoryResponse.getAchievementSetResponseList().add(setResponse);
                            }
                        }
                    });

                    // Collect all subcategories and add them to the category response
                    categoryResponse.setAchievementSubCategories(new ArrayList<>(subCategoryMap.values()));
                    categoryResponses.add(categoryResponse);
                });

        response.setCategories(categoryResponses);
        return response;
    }

    @Override
    public List<AchievementCountForCategoryResDTO> getAchievementsCountForAllCategories(Long userId) {
        // Fetch all unique category types
        List<AchievementCategoryType> categories = achievementsRepository.findAllDistinctAchievementCategories();

        // Calculate count for each category
        return categories.stream()
                .map(category -> {
                    int totalAchievementsCount = achievementsRepository.countAchievementsByCategoryType(category);
                    int userCompletedAchievementsCount = userAchievementsRepository.countUserAchievementsByAchievementCategoryTypeAndUserId(category, userId);
                    boolean isNewAvailable = userAchievementsRepository.existsByAchievementCategoryTypeAndUserIdAndIsClaimedAndIsDeleted(category, userId, false, false);
                    return new AchievementCountForCategoryResDTO(category, totalAchievementsCount, userCompletedAchievementsCount, isNewAvailable);
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<AchievementDTO> getAllAchievementsBySetName(AchievementsBySetNameReq achievementsBySetNameReq) {
        if (achievementsBySetNameReq.getAchievementCategoryType().equals(AchievementCategoryType.Mind.name())) {
            return achievementsRepository.findAchievementsByAudioPlaylist(achievementsBySetNameReq.getSetId(), achievementsBySetNameReq.getUserId());
        } else if (achievementsBySetNameReq.getAchievementCategoryType().equals(AchievementCategoryType.Body.name())) {
            return achievementsRepository.findAchievementsByWorkoutPlaylist(achievementsBySetNameReq.getSetId(), achievementsBySetNameReq.getUserId());
        } else if (achievementsBySetNameReq.getAchievementCategoryType().equals(AchievementCategoryType.Warrior.name())) {
            return achievementsRepository.findAchievementsByWarriorPlaylist(achievementsBySetNameReq.getSetId(), achievementsBySetNameReq.getUserId());
        } else {
            return null;
        }
    }

//    @Override
//    public Map<String,EntityAchievementLevelUp> getCurrentAndNextLevel(int wxpEarned) {
//        List<EntityAchievementLevelUp> levels = achievementLevelUpRepository.findAllOrderedByWxpNeeded();
//
//        EntityAchievementLevelUp currentLevel = levels.get(0);
//        for (EntityAchievementLevelUp level : levels) {
//            int requiredWxp = level.getWxpNeeded();
//            if (wxpEarned >= requiredWxp) {
//                currentLevel = level;
//            } else {
//                break; // Stop when we find a level that requires more wxp than the user has
//            }
//        }
//        Map<String,EntityAchievementLevelUp> map = new HashMap<>();
//        map.put(Constant.CURRENT_LEVEL,currentLevel);
//        if (levels.contains(currentLevel) && levels.indexOf(currentLevel) < levels.size() - 1) {
//            EntityAchievementLevelUp nextLevel = levels.get(levels.indexOf(currentLevel) + 1);
//            map.put(Constant.NEXT_LEVEL,nextLevel);
//        }else {
//            map.put(Constant.NEXT_LEVEL,null);
//        }
//
//        return map;
//    }

    @Override
    public Map<String, Long> getCurrentAndNextLevelMap(Long wxpEarned) {
        long newPoint = 300;
        long currentPoint = 0;
        long currentLevel = 1;
        for(long i = 2; i<wxpEarned; i++){
//            System.out.println("Level:" + i + " Points: " + newPoint);
            if(newPoint>=wxpEarned){
                currentLevel = i-1;
                break;
            }
            currentPoint = newPoint;
            newPoint = Math.round(newPoint * 1.2);
        }
        Map<String, Long> map = new HashMap<>();
        map.put(Constant.CURRENT_LEVEL,currentLevel);
        map.put(Constant.CURRENT_LEVEL_WXP_POINT,currentPoint);
        map.put(Constant.NEXT_LEVEL,currentLevel+1);
        map.put(Constant.NEXT_LEVEL_WXP_POINT,newPoint);
        return map;
    }


}
