package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.UploadedFileNameResponseDTO;
import com.mindbody.api.dto.cms.AddSoundReqDTO;
import com.mindbody.api.dto.cms.EditSoundReqDTO;
import com.mindbody.api.dto.cms.SoundDetailResDTO;
import com.mindbody.api.enums.ImageType;
import com.mindbody.api.enums.SoundType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.SoundMapper;
import com.mindbody.api.model.EntitySound;
import com.mindbody.api.repository.SoundRepository;
import com.mindbody.api.service.AWSS3Service;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.SoundService;
import com.mindbody.api.util.StringUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class SoundServiceImpl extends BaseService implements SoundService {

    private final SoundRepository soundRepository;
    private final AWSS3Service awsS3Service;
    private final GlobalConfiguration globalConfiguration;
    private final SoundMapper soundMapper;

    public SoundServiceImpl(MessageService messageService, SoundRepository soundRepository, AWSS3Service awsS3Service, GlobalConfiguration globalConfiguration, SoundMapper soundMapper) {
        super(messageService);
        this.soundRepository = soundRepository;
        this.awsS3Service = awsS3Service;
        this.globalConfiguration = globalConfiguration;
        this.soundMapper = soundMapper;
    }

    @Override
    public void addSound(AddSoundReqDTO addSoundReqDTO) {
        Optional<EntitySound> entitySound = soundRepository.findByTitleAndIsActiveAndIsDeleted(addSoundReqDTO.getTitle(), true, false);
        if (entitySound.isPresent()) {
            throw new BusinessValidationException("title_already_exists");
        }
        EntitySound sound = soundMapper.toModel(addSoundReqDTO);
        if (addSoundReqDTO.getSoundFile() != null && !addSoundReqDTO.getSoundFile().isEmpty()) {
            UploadedFileNameResponseDTO uploadedFileName = awsS3Service.uploadFile(addSoundReqDTO.getSoundFile(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getSoundFileFolderName(), ImageType.soundFile);
            sound.setSoundFile(uploadedFileName.getImageName());
        }
        soundRepository.save(sound);
    }

    @Override
    public void editSound(EditSoundReqDTO editSoundReqDTO) {
        EntitySound entitySound = soundRepository.findBySoundIdAndIsDeleted(editSoundReqDTO.getSoundId(), false);
        if (Objects.isNull(entitySound)) {
            throw new BusinessValidationException("sound_not_found");
        }
        String newSoundImageName = "";
        if (editSoundReqDTO.getSoundFile() != null && !editSoundReqDTO.getSoundFile().isEmpty()) {
            if (!StringUtil.nullOrEmpty(entitySound.getSoundFile())) {
                awsS3Service.deleteFile(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getSoundFileFolderName() + entitySound.getSoundFile(), ImageType.soundFile);
            }
            UploadedFileNameResponseDTO uploadedFileName = awsS3Service.uploadFile(editSoundReqDTO.getSoundFile(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getSoundFileFolderName(), ImageType.soundFile);
            newSoundImageName = uploadedFileName.getImageName();
        }
        if (!entitySound.getTitle().equals(editSoundReqDTO.getTitle())) {
            Optional<EntitySound> soundTitle = soundRepository.findByTitleAndSoundTypeAndIsActiveAndIsDeleted(editSoundReqDTO.getTitle(), SoundType.valueOf(editSoundReqDTO.getSoundType()), true, false);
            if (soundTitle.isPresent()) {
                throw new BusinessValidationException("title_already_exists");
            }
        }
        entitySound.setTitle(editSoundReqDTO.getTitle());
        if (StringUtil.nonNullNonEmpty(newSoundImageName)) {
            entitySound.setSoundFile(newSoundImageName);
        }
        soundRepository.save(entitySound);
    }

    @Override
    public void deleteSound(Long soundId) {
        EntitySound entitySound = soundRepository.findBySoundIdAndIsDeleted(soundId, false);
        if (Objects.isNull(entitySound)) {
            throw new BusinessValidationException("sound_not_found");
        }
        entitySound.setDeleted(true);
        entitySound.setActive(false);
        soundRepository.save(entitySound);
    }

    @Override
    public SearchResultDTO<SoundDetailResDTO> listSoundCms(CommonListDTO commonListDTO, boolean checkIsActiveRecord) {
        Sort sort = null;
        if (commonListDTO.getSortBy() != null && commonListDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(commonListDTO.getSortBy().getDirection(), commonListDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(commonListDTO.getPage().getPageId(),
                commonListDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());
        List<SoundDetailResDTO> soundList = new ArrayList<>();
        long pageCount = 0L;
        pageCount = soundRepository.countSounds(commonListDTO.getQueryToSearch(), checkIsActiveRecord, false);
        if (pageCount > 0) {
            String soundFilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getMagazineFolderName();
            Page<SoundDetailResDTO> soundsPage = soundRepository.findAllSounds(
                    commonListDTO.getQueryToSearch(),
                    soundFilePath,
                    checkIsActiveRecord,
                    false,
                    pageable
            );
            soundList = soundsPage.getContent();
        }
        return new SearchResultDTO<>(soundList, pageCount, commonListDTO.getPage().getLimit());
    }
}
