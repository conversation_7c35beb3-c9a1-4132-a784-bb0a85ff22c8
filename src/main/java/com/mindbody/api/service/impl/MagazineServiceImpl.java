package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.UploadedFileNameResponseDTO;
import com.mindbody.api.dto.cms.AddMagazineReqDTO;
import com.mindbody.api.dto.cms.EditMagazineReqDTO;
import com.mindbody.api.dto.cms.MagazineDetailResDTO;
import com.mindbody.api.enums.ImageType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.MagazineMapper;
import com.mindbody.api.model.EntityMagazine;
import com.mindbody.api.repository.MagazineRepository;
import com.mindbody.api.service.AWSS3Service;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MagazineService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.util.StringUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class MagazineServiceImpl extends BaseService implements MagazineService {

    private final MagazineRepository magazineRepository;

    private final AWSS3Service awss3Service;

    private final GlobalConfiguration globalConfiguration;

    private final MagazineMapper magazineMapper;

    public MagazineServiceImpl(MessageService messageService, MagazineRepository magazineRepository, AWSS3Service awss3Service, GlobalConfiguration globalConfiguration, MagazineMapper magazineMapper) {
        super(messageService);
        this.magazineRepository = magazineRepository;
        this.awss3Service = awss3Service;
        this.globalConfiguration = globalConfiguration;
        this.magazineMapper = magazineMapper;
    }

    @Override
    public void addMagazine(AddMagazineReqDTO addMagazineReqDTO) {
        validateDTO(addMagazineReqDTO);
        Optional<EntityMagazine> entityMagazine = magazineRepository.findByTitleAndIsActiveAndIsDeleted(addMagazineReqDTO.getTitle(), true, false);
        if (entityMagazine.isPresent()) {
            throw new BusinessValidationException("title_already_exists");
        }
        EntityMagazine magazine = magazineMapper.toModel(addMagazineReqDTO);
        if (addMagazineReqDTO.getMagazineImage() != null && !addMagazineReqDTO.getMagazineImage().isEmpty()) {
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(addMagazineReqDTO.getMagazineImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getMagazineFolderName(), ImageType.magazineImage);
            magazine.setMagazineImage(uploadedFileName.getImageName());
        }

        if (addMagazineReqDTO.getMagazineThumbnailImage() != null && !addMagazineReqDTO.getMagazineThumbnailImage().isEmpty()) {
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(addMagazineReqDTO.getMagazineThumbnailImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getMagazineThumbnailFolderName(), ImageType.magazineThumbnailImage);
            magazine.setMagazineThumbnailImage(uploadedFileName.getImageName());
        }
        magazineRepository.save(magazine);
    }

    public boolean validateDTO(AddMagazineReqDTO addMagazineReqDTO) {
        if (Objects.isNull(addMagazineReqDTO.getMagazineImage())) {
            throw new BusinessValidationException("magazine_image_required");
        }
        if (Objects.isNull(addMagazineReqDTO.getMagazineThumbnailImage())) {
            throw new BusinessValidationException("thumbnail_image_required");
        }
        return true;
    }

    @Override
    public void editMagazine(EditMagazineReqDTO editMagazineReqDTO) {
        Optional<EntityMagazine> entityMagazine = magazineRepository.findById(editMagazineReqDTO.getMagazineId());
        if (entityMagazine.isEmpty()) {
            throw new BusinessValidationException("magazine_not_found");
        }
        String newMagazineImageName = "";
        String newMagazineThumbnailImageName = "";
        if (editMagazineReqDTO.getMagazineImage() != null && !editMagazineReqDTO.getMagazineImage().isEmpty()) {
            if (!StringUtil.nullOrEmpty(entityMagazine.get().getMagazineImage())) {
                awss3Service.deleteFile(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getMagazineFolderName() + entityMagazine.get().getMagazineImage(), ImageType.magazineImage);
            }
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(editMagazineReqDTO.getMagazineImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getMagazineFolderName(), ImageType.magazineImage);
            newMagazineImageName = uploadedFileName.getImageName();
        }

        if (editMagazineReqDTO.getMagazineThumbnailImage() != null && !editMagazineReqDTO.getMagazineThumbnailImage().isEmpty()) {
            if (!StringUtil.nullOrEmpty(entityMagazine.get().getMagazineThumbnailImage())) {
                awss3Service.deleteFile(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getMagazineThumbnailFolderName() + entityMagazine.get().getMagazineThumbnailImage(), ImageType.magazineThumbnailImage);
            }
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(editMagazineReqDTO.getMagazineThumbnailImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getMagazineThumbnailFolderName(), ImageType.magazineThumbnailImage);
            newMagazineThumbnailImageName = uploadedFileName.getImageName();
        }

        entityMagazine.get().setTitle(editMagazineReqDTO.getTitle());
        entityMagazine.get().setSubTitle(editMagazineReqDTO.getSubTitle());
        entityMagazine.get().setDescription(editMagazineReqDTO.getDescription());
        entityMagazine.get().setMagazineLink(editMagazineReqDTO.getMagazineLink());
        if (StringUtil.nonNullNonEmpty(newMagazineImageName)) {
            entityMagazine.get().setMagazineImage(newMagazineImageName);
        }
        if (StringUtil.nonNullNonEmpty(newMagazineThumbnailImageName)) {
            entityMagazine.get().setMagazineThumbnailImage(newMagazineThumbnailImageName);
        }
        magazineRepository.save(entityMagazine.get());
    }

    @Override
    public SearchResultDTO<MagazineDetailResDTO> listMagazine(CommonListDTO commonListDTO, boolean checkIsActiveRecord) {
        Sort sort = null;
        if (commonListDTO.getSortBy() != null && commonListDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(commonListDTO.getSortBy().getDirection(), commonListDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(commonListDTO.getPage().getPageId(),
                commonListDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());
        List<MagazineDetailResDTO> magazineList = new ArrayList<>();
        long pageCount = 0L;
        pageCount = magazineRepository.countMagazines(commonListDTO.getQueryToSearch(), checkIsActiveRecord, false);
        if (pageCount > 0) {
            String magazineImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getMagazineFolderName();
            String magazineThumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getMagazineThumbnailFolderName();
            Page<MagazineDetailResDTO> magazinesPage = magazineRepository.findAllMagazines(
                    commonListDTO.getQueryToSearch(),
                    magazineImagePath,
                    magazineThumbnailImagePath,
                    checkIsActiveRecord,
                    false,
                    pageable
            );
            magazineList = magazinesPage.getContent();
        }
        return new SearchResultDTO<>(magazineList, pageCount, commonListDTO.getPage().getLimit());
    }

    @Override
    public String activeInactiveMagazine(Long magazineId) {
        String activeInactiveMsg = "magazine_activate";
        EntityMagazine entityMagazine = magazineRepository.findByMagazineIdAndIsDeleted(magazineId, false);
        if (Objects.isNull(entityMagazine)) {
            throw new BusinessValidationException("magazine_not_found");
        }
        if (entityMagazine.isActive()) {
            activeInactiveMsg = "magazine_inactivate";
        }
        entityMagazine.setActive(!entityMagazine.isActive());
        magazineRepository.save(entityMagazine);
        return activeInactiveMsg;
    }
}
