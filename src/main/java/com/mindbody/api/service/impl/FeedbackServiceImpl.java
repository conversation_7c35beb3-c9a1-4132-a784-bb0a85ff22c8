package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.feedback.FeedbackReqDTO;
import com.mindbody.api.dto.feedback.FeedbackResDTO;
import com.mindbody.api.mapper.FeedbackMapper;
import com.mindbody.api.model.EntityFeedback;
import com.mindbody.api.model.EntityUserInfo;
import com.mindbody.api.repository.FeedbackRepository;
import com.mindbody.api.repository.UserInfoRepository;
import com.mindbody.api.service.FeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class FeedbackServiceImpl implements FeedbackService {

    @Autowired
    private FeedbackRepository feedbackRepository;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private FeedbackMapper feedbackMapper;

    @Autowired
    private GlobalConfiguration globalConfiguration;

    @Override
    public FeedbackResDTO submitFeedback(FeedbackReqDTO feedbackReqDTO) {
        // Convert DTO to Entity
        EntityFeedback entityFeedback = feedbackMapper.toModel(feedbackReqDTO);
        
        // Save feedback
        entityFeedback = feedbackRepository.save(entityFeedback);

        // Get user info for achievements
        EntityUserInfo userInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(entityFeedback.getUserId(), true, false);
        
        // Convert Entity to Response DTO
        FeedbackResDTO feedbackResDTO = feedbackMapper.toDTO(entityFeedback);

        return feedbackResDTO;
    }

    @Override
    public SearchResultDTO<FeedbackResDTO> listFeedback(CommonListDTO commonListDTO) {
        Sort sort = null;
        if (commonListDTO.getSortBy() != null && commonListDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(commonListDTO.getSortBy().getDirection(), commonListDTO.getSortBy().getProperty());
        }
        
        Pageable pageable = PageRequest.of(
            commonListDTO.getPage().getPageId(),
            commonListDTO.getPage().getLimit(),
            sort != null ? sort : Sort.unsorted()
        );

        List<FeedbackResDTO> feedbackList = new ArrayList<>();
        long pageCount = feedbackRepository.countByIsActiveAndIsDeleted(true, false);
        
        if (pageCount > 0) {
            List<EntityFeedback> feedbacks = feedbackRepository.findByIsActiveAndIsDeleted(
                true, false, pageable);
            feedbackList = mapFeedbacksToDTO(feedbacks);
        }

        return new SearchResultDTO<>(feedbackList, pageCount, commonListDTO.getPage().getLimit());
    }

    private List<FeedbackResDTO> mapFeedbacksToDTO(List<EntityFeedback> feedbacks) {
        return feedbacks.stream()
            .map(feedback -> {
                 return feedbackMapper.toDTO(feedback);
            })
            .toList();
    }
} 