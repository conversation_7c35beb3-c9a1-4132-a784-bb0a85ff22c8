package com.mindbody.api.service.impl;

import com.mindbody.api.model.EmailVerificationToken;
import com.mindbody.api.model.EntityAdmin;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.repository.EmailVerificationTokenRepository;
import com.mindbody.api.service.EmailVerificationTokenGeneratorService;
import com.mindbody.api.util.Methods;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Optional;

@Service
public class EmailVerificationTokenGenerator implements EmailVerificationTokenGeneratorService {

    @Autowired
    private EmailVerificationTokenRepository emailVerificationTokenRepository;

    //10 minutes expiration time
    private static final Integer LINK_EXPIRATION = 10;

    // This method is used to get verification token
    public String getVerificationToken(EntityAdmin admin) {
        String token = Methods.getRandomUUID().toString() + LocalDateTime.now().toString();
        LocalDateTime expirationDateTime = LocalDateTime.now().plusMinutes(LINK_EXPIRATION);
        Optional<EmailVerificationToken> emailVerificationToken = emailVerificationTokenRepository.findByAdminId(admin.getAdminId());
        if (emailVerificationToken.isEmpty()) {
            EmailVerificationToken emailVerification = new EmailVerificationToken();
            emailVerification.setEntityAdmin(admin);
            emailVerification.setAdminId(admin.getAdminId());
            emailVerification.setVerificationToken(token);
            emailVerification.setExpirationTime(Date.from(expirationDateTime.atZone(ZoneId.systemDefault()).toInstant()));
            emailVerificationTokenRepository.save(emailVerification);
        } else {
            emailVerificationToken.get().setVerificationToken(token);
            emailVerificationToken.get().setExpirationTime(Date.from(expirationDateTime.atZone(ZoneId.systemDefault()).toInstant()));
            emailVerificationTokenRepository.save(emailVerificationToken.get());
        }
        return token;
    }


    @Override
    public String getVerificationTokenForUser(EntityUser entityUser) {
        Optional<EmailVerificationToken> emailVerificationToken = emailVerificationTokenRepository.findByUserIdAndIsActiveAndIsDeleted(entityUser.getUserId(), true, false);
        if (emailVerificationToken.isPresent()) {
            EmailVerificationToken emailVerification = emailVerificationToken.get();
            emailVerification.setActive(false);
            emailVerification.setDeleted(true);
            emailVerificationTokenRepository.save(emailVerification);
        }

        String token = Methods.getRandomUUID().toString() + LocalDateTime.now().toString();
        LocalDateTime expirationDateTime = LocalDateTime.now().plusMinutes(LINK_EXPIRATION);
        EmailVerificationToken verificationToken = new EmailVerificationToken();
        verificationToken.setEntityUser(entityUser);
        verificationToken.setUserId(entityUser.getUserId());
        verificationToken.setVerificationToken(token);
        verificationToken.setExpirationTime(Date.from(expirationDateTime.atZone(ZoneId.systemDefault()).toInstant()));
        emailVerificationTokenRepository.save(verificationToken);
        return token;
    }

}
