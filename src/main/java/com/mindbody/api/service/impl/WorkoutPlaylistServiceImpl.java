package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.UploadedFileNameResponseDTO;
import com.mindbody.api.dto.UserWorkoutPlaylistResDTO;
import com.mindbody.api.dto.cms.AddWorkoutPlaylistReqDTO;
import com.mindbody.api.dto.cms.EditWorkoutPlaylistReqDTO;
import com.mindbody.api.dto.cms.WorkoutPlaylistDetailResDTO;
import com.mindbody.api.enums.ImageType;
import com.mindbody.api.enums.WorkoutPlaylistType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.WorkoutPlaylistMapper;
import com.mindbody.api.model.EntityWorkoutPlan;
import com.mindbody.api.model.EntityWorkoutPlaylist;
import com.mindbody.api.repository.WorkoutPlanRepository;
import com.mindbody.api.repository.WorkoutPlaylistRepository;
import com.mindbody.api.service.AWSS3Service;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.WorkoutPlaylistService;
import com.mindbody.api.util.StringUtil;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class WorkoutPlaylistServiceImpl extends BaseService implements WorkoutPlaylistService {

    private final WorkoutPlaylistRepository workoutPlaylistRepository;

    private final WorkoutPlanRepository workoutPlanRepository;

    private final WorkoutPlaylistMapper workoutPlaylistMapper;

    private final AWSS3Service awss3Service;

    private final GlobalConfiguration globalConfiguration;

    public WorkoutPlaylistServiceImpl(MessageService messageService, WorkoutPlaylistRepository workoutPlaylistRepository, WorkoutPlanRepository workoutPlanRepository, WorkoutPlaylistMapper workoutPlaylistMapper, AWSS3Service awss3Service, GlobalConfiguration globalConfiguration) {
        super(messageService);
        this.workoutPlaylistRepository = workoutPlaylistRepository;
        this.workoutPlanRepository = workoutPlanRepository;
        this.workoutPlaylistMapper = workoutPlaylistMapper;
        this.awss3Service = awss3Service;
        this.globalConfiguration = globalConfiguration;
    }

    @Override
    public void addWorkoutPlaylist(MultipartFile workoutPlaylistImage, AddWorkoutPlaylistReqDTO addWorkoutCategoryReqDTO) {
        Optional<EntityWorkoutPlaylist> entityWorkoutPlaylist = workoutPlaylistRepository.findByWorkoutPlaylistNameAndWorkoutPlaylistTypeAndIsActiveAndIsDeleted(addWorkoutCategoryReqDTO.getWorkoutPlaylistName().trim(), WorkoutPlaylistType.valueOf(addWorkoutCategoryReqDTO.getWorkoutPlaylistType()), true, false);
        if (entityWorkoutPlaylist.isPresent()) {
            throw new BusinessValidationException("workout_playlist_already_exists");
        }
        EntityWorkoutPlaylist workoutPlaylist = workoutPlaylistMapper.toModel(addWorkoutCategoryReqDTO);
        if (workoutPlaylistImage != null && !workoutPlaylistImage.isEmpty()) {
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(workoutPlaylistImage, globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutPlaylistFolderName(), ImageType.workoutPlaylistImage);
            workoutPlaylist.setWorkoutPlaylistImage(uploadedFileName.getImageName());
        }
        workoutPlaylistRepository.save(workoutPlaylist);
    }

    @Override
    public void editWorkoutPlaylist(MultipartFile workoutPlaylistImage, EditWorkoutPlaylistReqDTO editWorkoutPlaylistReqDTO) {
        EntityWorkoutPlaylist entityWorkoutPlaylist = workoutPlaylistRepository.findByWorkoutPlaylistIdAndIsDeleted(editWorkoutPlaylistReqDTO.getWorkoutPlaylistId(), false);
        if (Objects.isNull(entityWorkoutPlaylist)) {
            throw new BusinessValidationException("workout_playlist_not_found");
        }
        if (!entityWorkoutPlaylist.getWorkoutPlaylistName().equals(editWorkoutPlaylistReqDTO.getWorkoutPlaylistName())) {
            Optional<EntityWorkoutPlaylist> workoutPlaylistName = workoutPlaylistRepository.findByWorkoutPlaylistNameAndWorkoutPlaylistTypeAndIsActiveAndIsDeletedAndWorkoutPlaylistIdNot(editWorkoutPlaylistReqDTO.getWorkoutPlaylistName(), WorkoutPlaylistType.valueOf(editWorkoutPlaylistReqDTO.getWorkoutPlaylistType()), true, false, editWorkoutPlaylistReqDTO.getWorkoutPlaylistId());
            if (workoutPlaylistName.isPresent()) {
                throw new BusinessValidationException("workout_playlist_already_exists");
            }
        }
        entityWorkoutPlaylist.setWorkoutPlaylistName(editWorkoutPlaylistReqDTO.getWorkoutPlaylistName());
        String newWorkoutPlaylistImageName = "";
        if (workoutPlaylistImage != null && !workoutPlaylistImage.isEmpty()) {
            if (!StringUtil.nullOrEmpty(entityWorkoutPlaylist.getWorkoutPlaylistImage())) {
                awss3Service.deleteFile(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutPlaylistFolderName() + entityWorkoutPlaylist.getWorkoutPlaylistImage(), ImageType.workoutPlaylistImage);
            }
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(workoutPlaylistImage, globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutPlaylistFolderName(), ImageType.workoutPlaylistImage);
            newWorkoutPlaylistImageName = uploadedFileName.getImageName();
        }
        if (StringUtil.nonNullNonEmpty(newWorkoutPlaylistImageName)) {
            entityWorkoutPlaylist.setWorkoutPlaylistImage(newWorkoutPlaylistImageName);
        }
        workoutPlaylistRepository.save(entityWorkoutPlaylist);
    }

    @Override
    public SearchResultDTO<WorkoutPlaylistDetailResDTO> listWorkoutPlaylist(CommonListDTO commonListDTO, boolean checkIsActiveRecord) {
        WorkoutPlaylistType workoutPlaylistType = null;
        boolean isWorkoutPlaylistTypeFilter = false;
        if (commonListDTO.getFilters() != null && StringUtil.nonNullNonEmpty(commonListDTO.getFilters().getSearchFilter())) {
            try {
                workoutPlaylistType = WorkoutPlaylistType.valueOf(commonListDTO.getFilters().getSearchFilter());
            } catch (Exception e) {
                throw new BusinessValidationException("invalid_workout_playlist_type");
            }
            isWorkoutPlaylistTypeFilter = true;
        }
        Sort sort = null;
        if (commonListDTO.getSortBy() != null && commonListDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(commonListDTO.getSortBy().getDirection(), commonListDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(commonListDTO.getPage().getPageId(), commonListDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());

        List<WorkoutPlaylistDetailResDTO> workoutPlaylistDetailResDTOList = new ArrayList<>();
        long pageCount = 0L;
        pageCount = workoutPlaylistRepository.countWorkoutPlaylists(commonListDTO.getQueryToSearch(), workoutPlaylistType, isWorkoutPlaylistTypeFilter, false);
        if (pageCount > 0) {
            String workoutPlaylistImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutPlaylistFolderName();
            workoutPlaylistDetailResDTOList = workoutPlaylistRepository.findAllWorkoutPlaylists(commonListDTO.getQueryToSearch(), workoutPlaylistImagePath, pageable, workoutPlaylistType, isWorkoutPlaylistTypeFilter, false);
        }
        return new SearchResultDTO<>(workoutPlaylistDetailResDTOList, pageCount, commonListDTO.getPage().getLimit());
    }

    @Override
    public void deleteWorkoutPlaylist(Long workoutPlaylistId) {
        EntityWorkoutPlaylist entityWorkoutPlaylist = workoutPlaylistRepository.findByWorkoutPlaylistIdAndIsDeleted(workoutPlaylistId, false);
        if (Objects.isNull(entityWorkoutPlaylist)) {
            throw new BusinessValidationException("workout_playlist_not_found");
        }

        List<EntityWorkoutPlan> workoutPlanList = workoutPlanRepository.findAllByWorkoutPlaylistIdAndIsDeleted(entityWorkoutPlaylist.getWorkoutPlaylistId(), false);
        if (!workoutPlanList.isEmpty()) {
            throw new BusinessValidationException("workout_playlist_delete_error");
        }
        entityWorkoutPlaylist.setDeleted(true);
        entityWorkoutPlaylist.setActive(false);
        workoutPlaylistRepository.save(entityWorkoutPlaylist);
    }

    @Override
    public String activeInactiveWorkoutPlaylist(Long workoutPlaylistId) {
        String activeInactiveMsg = "workout_playlist_activate";
        EntityWorkoutPlaylist entityWorkoutPlaylist = workoutPlaylistRepository.findByWorkoutPlaylistIdAndIsDeleted(workoutPlaylistId, false);
        if (Objects.isNull(entityWorkoutPlaylist)) {
            throw new BusinessValidationException("workout_playlist_not_found");
        }
        if (entityWorkoutPlaylist.isActive()) {
            List<EntityWorkoutPlan> workoutPlanList = workoutPlanRepository.findAllByWorkoutPlaylistIdAndIsDeleted(entityWorkoutPlaylist.getWorkoutPlaylistId(), false);
            if (!workoutPlanList.isEmpty()) {
                throw new BusinessValidationException("workout_playlist_inactive_error");
            }
            activeInactiveMsg = "workout_playlist_inactivate";
        }
        entityWorkoutPlaylist.setActive(!entityWorkoutPlaylist.isActive());
        workoutPlaylistRepository.save(entityWorkoutPlaylist);
        return activeInactiveMsg;
    }

    @Override
    public List<UserWorkoutPlaylistResDTO> listWorkoutPlaylistForSelectedWorkoutPlaylistType(String workoutPlaylistTypeInString) {
        WorkoutPlaylistType workoutPlaylistType = null;
        try {
            workoutPlaylistType = WorkoutPlaylistType.valueOf(workoutPlaylistTypeInString);
        } catch (Exception e) {
            throw new BusinessValidationException("invalid_workout_playlist_type");
        }
        String workoutPlaylistImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutPlaylistFolderName();
        return workoutPlaylistRepository.findAllWorkoutPlaylistsForSelectedWorkoutPlaylistType(workoutPlaylistImagePath, workoutPlaylistType, true, false);
    }


}
