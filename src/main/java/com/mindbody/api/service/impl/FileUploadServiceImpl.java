package com.mindbody.api.service.impl;

import com.amazonaws.services.s3.model.PartETag;
import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.*;
import com.mindbody.api.enums.FileUploadModuleName;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.service.AWSS3Service;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.FileUploadService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.util.StringUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class FileUploadServiceImpl extends BaseService implements FileUploadService {

    private final AWSS3Service awss3Service;

    private final GlobalConfiguration globalConfiguration;

    public FileUploadServiceImpl(MessageService messageService, AWSS3Service awss3Service, GlobalConfiguration globalConfiguration) {
        super(messageService);
        this.awss3Service = awss3Service;
        this.globalConfiguration = globalConfiguration;
    }

    @Override
    public GeneratePreSignedUrlResDTO generatePreSignedUrls(GeneratePreSignedUrlReqDTO generatePreSignedUrlReqDTO) {
        String filePath = getFilePathByModuleName(generatePreSignedUrlReqDTO.getModuleName()) + generatePreSignedUrlReqDTO.getFileName();
        String uploadId = awss3Service.generateUploadId(filePath, generatePreSignedUrlReqDTO.getContentType());
        if (StringUtil.nullOrEmpty(uploadId)) {
            throw new BusinessValidationException("uploadId_not_found");
        }
        List<PreSignedUrlDTO> preSignedUrlDTOList = new ArrayList<>();
        for (int part = 1; part <= generatePreSignedUrlReqDTO.getPartCount(); part++) {
            String preSignedUrl = awss3Service.generatePreSignedUrl(filePath, uploadId, part);
            preSignedUrlDTOList.add(new PreSignedUrlDTO(preSignedUrl, part));
        }
        return new GeneratePreSignedUrlResDTO(uploadId, preSignedUrlDTOList);
    }

    @Override
    public void completeFileUpload(CompleteFileUploadReqDTO completeFileUploadReqDTO) {
        String fileUrl = getFilePathByModuleName(completeFileUploadReqDTO.getModuleName()) + completeFileUploadReqDTO.getFileName();
        List<PartETag> partETags = new ArrayList<>();
        for (PartEtagDTO dto : completeFileUploadReqDTO.getPartETags()) {
            partETags.add(new PartETag(dto.getPartNumber(), dto.getETag()));
        }
        awss3Service.completeMultipartUpload(fileUrl, completeFileUploadReqDTO.getUploadId(), partETags);
    }

    @Override
    public void abortFileUpload(AbortFileUploadReqDTO abortFileUploadReqDTO) {
        String fileUrl = getFilePathByModuleName(abortFileUploadReqDTO.getModuleName()) + abortFileUploadReqDTO.getFileName();
        awss3Service.abortMultipartUpload(fileUrl, abortFileUploadReqDTO.getUploadId());
    }

    private String getFilePathByModuleName(String moduleName) {
        String baseUploadFolder = globalConfiguration.getAmazonS3().getUploadFolderName();
        return switch (FileUploadModuleName.valueOf(moduleName)) {
            case WORKOUT_PLAN -> baseUploadFolder + globalConfiguration.getAmazonS3().getWorkoutVideoFolderName();
            case AUDIO -> baseUploadFolder + globalConfiguration.getAmazonS3().getAudioFileFolderName();
            case EXERCISE -> baseUploadFolder + globalConfiguration.getAmazonS3().getExerciseVideoFolderName();
            default -> throw new BusinessValidationException("Unsupported module name: " + moduleName);
        };
    }

}
