package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.ReferralCodeDTOList;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.repository.UserReferralRepository;
import com.mindbody.api.service.ReferralService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReferralServiceImpl implements ReferralService {

    private final UserReferralRepository userReferralRepository;
    private final GlobalConfiguration globalConfiguration;

    @Override
    public SearchResultDTO<ReferralCodeDTOList> listReferralCodesForCMS(CommonListDTO commonListDTO) {
        log.info("Listing referral codes for CMS with page: {}, limit: {}, sortBy: {}",
                commonListDTO.getPage().getPageId(),
                commonListDTO.getPage().getLimit(),
                commonListDTO.getSortBy() != null ? commonListDTO.getSortBy().getProperty() : "none");

        // We'll use unsorted for the database query and sort in memory
        // This avoids SQL_MODE=only_full_group_by issues
        Pageable pageable = PageRequest.of(
            commonListDTO.getPage().getPageId(),
            commonListDTO.getPage().getLimit(),
            Sort.unsorted()
        );

        List<ReferralCodeDTOList> list = new ArrayList<>();
        long totalCount;

        try {
            totalCount = userReferralRepository.countAllReferrals();
            log.info("Total referral count: {}", totalCount);

            if (totalCount > 0) {
                String borderImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + 
                                       globalConfiguration.getAmazonS3().getUploadFolderName() + 
                                       globalConfiguration.getAmazonS3().getAchievementBorderFolderName();
                String badgeImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + 
                                      globalConfiguration.getAmazonS3().getUploadFolderName() + 
                                      globalConfiguration.getAmazonS3().getAchievementBadgeFolderName();
                
                log.debug("Using border image path: {}", borderImagePath);
                log.debug("Using badge image path: {}", badgeImagePath);

                try {
                    // Get the list without sorting from the repository
                    list = userReferralRepository.listReferralCodesForCMS(borderImagePath, badgeImagePath, pageable);
                    log.info("Retrieved {} referral codes from database", list.size());

                    // Apply sorting based on request
                    if (commonListDTO.getSortBy() != null && commonListDTO.getSortBy().getProperty() != null) {
                        String property = commonListDTO.getSortBy().getProperty();
                        boolean isAscending = commonListDTO.getSortBy().getDirection() == Sort.Direction.ASC;

                        log.debug("Applying sort by {} in {} order", property, isAscending ? "ascending" : "descending");

                        // Sort based on the property
                        switch (property.toLowerCase()) {
                            case "usagecount" -> list.sort((a, b) -> isAscending
                                ? Long.compare(a.getUsageCount(), b.getUsageCount())
                                : Long.compare(b.getUsageCount(), a.getUsageCount()));
                            case "username" -> list.sort((a, b) -> {
                                String nameA = a.getUserName() != null ? a.getUserName() : "";
                                String nameB = b.getUserName() != null ? b.getUserName() : "";
                                return isAscending ? nameA.compareTo(nameB) : nameB.compareTo(nameA);
                            });
                            case "referralcode" -> list.sort((a, b) -> {
                                String codeA = a.getReferralCode() != null ? a.getReferralCode() : "";
                                String codeB = b.getReferralCode() != null ? b.getReferralCode() : "";
                                return isAscending ? codeA.compareTo(codeB) : codeB.compareTo(codeA);
                            });
                            case "gendertype" -> list.sort((a, b) -> {
                                String genderA = a.getGenderType() != null ? a.getGenderType() : "";
                                String genderB = b.getGenderType() != null ? b.getGenderType() : "";
                                return isAscending ? genderA.compareTo(genderB) : genderB.compareTo(genderA);
                            });
                            default -> {
                                log.warn("Unknown sort property: {}. Defaulting to usageCount DESC", property);
                                list.sort((a, b) -> Long.compare(b.getUsageCount(), a.getUsageCount()));
                            }
                        }
                    } else {
                        // Default sort by usageCount descending if no sort is specified
                        log.debug("No sort specified. Using default sort by usageCount DESC");
                        list.sort((a, b) -> Long.compare(b.getUsageCount(), a.getUsageCount()));
                    }
                } catch (Exception e) {
                    log.error("Error retrieving referral codes: {}", e.getMessage(), e);
                    
                    // Try the simpler query as fallback
                    try {
                        log.info("Trying fallback query after error");
                        list = userReferralRepository.listSimpleReferralCodesForCMS(pageable);
                        log.info("Retrieved {} referral codes from fallback query", list.size());
                    } catch (Exception ex) {
                        log.error("Error in fallback query: {}", ex.getMessage(), ex);
                        throw new BusinessValidationException("error_retrieving_referral_codes");
                    }
                }
            }

            return new SearchResultDTO<>(list, totalCount, commonListDTO.getPage().getLimit());
        } catch (Exception e) {
            log.error("Unexpected error in listReferralCodesForCMS: {}", e.getMessage(), e);
            throw new BusinessValidationException("error_retrieving_referral_codes");
        }
    }
}
