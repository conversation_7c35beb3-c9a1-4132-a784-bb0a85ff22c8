package com.mindbody.api.service.impl;

import com.mindbody.api.dto.shopify.CategoryListResDTO;
import com.mindbody.api.dto.shopify.getCategoryListReqDTO;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.ShopifyCategoryThirdPartyService;
import com.mindbody.api.service.ShopifyCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class ShopifyCategoryThirdPartyServiceImpl extends BaseService implements ShopifyCategoryThirdPartyService {

    private static final Logger logger = LoggerFactory.getLogger(ShopifyCategoryThirdPartyServiceImpl.class);

    private final ShopifyCommonService shopifyCommonService;

    public ShopifyCategoryThirdPartyServiceImpl(MessageService messageService, ShopifyCommonService shopifyCommonService) {
        super(messageService);
        this.shopifyCommonService = shopifyCommonService;
    }

    public CategoryListResDTO getCategoryList(getCategoryListReqDTO getCategoryListReqDTO) {
        try {
            String requestBody = "{"
                    + "\"query\": \"" + getCategoryListGraphQl() + "\","
                    + "\"variables\": {"
                    + "\"first\": " + getCategoryListReqDTO.getFirst() + ","
                    + "\"after\": " + (getCategoryListReqDTO.getAfter() != null ? "\"" + getCategoryListReqDTO.getAfter() + "\"" : null)
                    + "}"
                    + "}";

            return shopifyCommonService.sendGraphQlRequestToShopify(requestBody, CategoryListResDTO.class);
        } catch (Exception e) {
            logger.error("Unexpected error occurred while fetching the shopify category details: {}", e.getMessage());
            throw new BusinessValidationException("category_details_failed", e.getMessage());
        }
    }


    public String getCategoryListGraphQl() {
        return """
                query collectionListQuery($first: Int!, $after: String) {
                  collections(first: $first, after: $after) {
                    edges {
                      node {
                        id
                        title
                        descriptionHtml
                        handle
                        image {
                          src
                          altText
                        }
                      }
                    }
                    pageInfo {
                      hasNextPage
                      endCursor
                    }
                  }
                }
                
                """;
    }
}
