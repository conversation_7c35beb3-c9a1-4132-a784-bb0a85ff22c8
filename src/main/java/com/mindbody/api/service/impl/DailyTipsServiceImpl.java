package com.mindbody.api.service.impl;

import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.cms.AddDailyTipsReqDTO;
import com.mindbody.api.dto.cms.DailyTipsResSTO;
import com.mindbody.api.dto.cms.EditDailyTipsReqDTO;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.EntityDailyTips;
import com.mindbody.api.repository.DailyTipsRepository;
import com.mindbody.api.service.AWSS3Service;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.DailyTipsService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.util.StringUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DailyTipsServiceImpl extends BaseService implements DailyTipsService {

    private static final Logger logger = LoggerFactory.getLogger(DailyTipsServiceImpl.class);

    private final DailyTipsRepository dailyTipsRepository;

    private final AWSS3Service awsS3Service;

    private static final String XLSX = "xlsx";

    private static final String XLS = "xls";

    private static final String TITLE = "Title";

    public DailyTipsServiceImpl(MessageService messageService, DailyTipsRepository dailyTipsRepository, AWSS3Service awsS3Service) {
        super(messageService);
        this.dailyTipsRepository = dailyTipsRepository;
        this.awsS3Service = awsS3Service;
    }

    @Override
    public void addDailyTips(AddDailyTipsReqDTO addDailyTipsReqDTO, MultipartFile dailyTipsFile) {
        validateExcelFile(dailyTipsFile);
        try {
            Workbook workbook = new XSSFWorkbook(dailyTipsFile.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new BusinessValidationException("invalid_file_format");
            }
            int titleColumnIndex = -1;
            for (Cell cell : headerRow) {
                if (TITLE.equalsIgnoreCase(cell.getStringCellValue().trim())) {
                    titleColumnIndex = cell.getColumnIndex();
                    break;
                }
            }
            if (titleColumnIndex == -1) {
                throw new BusinessValidationException("title_column_not_found");
            }
            List<EntityDailyTips> dailyTipsList = new ArrayList<>();
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    String title = getCellValue(row.getCell(titleColumnIndex));
                    if (!title.isEmpty()) {
                        EntityDailyTips entityDailyTips = new EntityDailyTips(ZodiacSignType.valueOf(addDailyTipsReqDTO.getZodiacSign()), title);
                        dailyTipsList.add(entityDailyTips);
                    }
                }
            }
            if (!dailyTipsList.isEmpty()) {
                dailyTipsRepository.saveAll(dailyTipsList);
            } else {
                throw new BusinessValidationException("no_valid_data");
            }
        } catch (IOException e) {
            throw new BusinessValidationException("failed_to_process_file");
        }
    }

    private void validateExcelFile(MultipartFile dailyTipsFile) {
        if (dailyTipsFile == null || dailyTipsFile.isEmpty()) {
            throw new BusinessValidationException("daily_tips_file_not_found");
        }
        String fileExtension = awsS3Service.getFileExtension(dailyTipsFile.getOriginalFilename());
        if (!fileExtension.equalsIgnoreCase(XLSX) && !fileExtension.equalsIgnoreCase(XLS)) {
            throw new BusinessValidationException("invalid_file_type");
        }
    }

    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue();
            case NUMERIC -> String.valueOf(cell.getNumericCellValue());
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            case FORMULA -> cell.getCellFormula();
            default -> "";
        };
    }

    @Override
    public void editDailyTips(EditDailyTipsReqDTO editDailyTipsReqDTO) {
        EntityDailyTips entityDailyTips = dailyTipsRepository.findByDailyTipsIdAndIsDeleted(editDailyTipsReqDTO.getDailyTipsId(), false);
        if (Objects.isNull(entityDailyTips)) {
            throw new BusinessValidationException("daily_tip_not_found");
        }
        entityDailyTips.setTitle(editDailyTipsReqDTO.getTitle());
        entityDailyTips.setZodiacSign(ZodiacSignType.valueOf(editDailyTipsReqDTO.getZodiacSign()));
        dailyTipsRepository.save(entityDailyTips);
    }

    @Override
    public void deleteDailyTip(Long dailyTipsId) {
        EntityDailyTips entityDailyTip = dailyTipsRepository.findByDailyTipsIdAndIsDeleted(dailyTipsId, false);
        if (Objects.isNull(entityDailyTip)) {
            throw new BusinessValidationException("daily_tip_not_found");
        }
        entityDailyTip.setDeleted(true);
        entityDailyTip.setActive(false);
        dailyTipsRepository.save(entityDailyTip);
    }

    @Override
    public boolean activeInactiveDailyTip(Long dailyTipsId) {
        boolean isActive = false;
        EntityDailyTips entityDailyTip = dailyTipsRepository.findByDailyTipsIdAndIsDeleted(dailyTipsId, false);
        if (Objects.isNull(entityDailyTip)) {
            throw new BusinessValidationException("daily_tip_not_found");
        }
        if (entityDailyTip.isActive()) {
            isActive = true;
        }
        entityDailyTip.setActive(!entityDailyTip.isActive());
        dailyTipsRepository.save(entityDailyTip);
        return isActive;
    }

    @Override
    public SearchResultDTO<DailyTipsResSTO> listDailyTips(CommonListDTO commonListDTO) {
        ZodiacSignType zodiacSign = null;
        boolean isZodiacFilter = false;
        if (commonListDTO.getFilters() != null && StringUtil.nonNullNonEmpty(commonListDTO.getFilters().getSearchFilter())) {
            try {
                zodiacSign = ZodiacSignType.valueOf(commonListDTO.getFilters().getSearchFilter());
            } catch (Exception e) {
                throw new BusinessValidationException("invalid_zodiac_sign");
            }
            isZodiacFilter = true;
        }
        Sort sort = null;
        if (commonListDTO.getSortBy() != null && commonListDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(commonListDTO.getSortBy().getDirection(), commonListDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(commonListDTO.getPage().getPageId(), commonListDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());

        List<DailyTipsResSTO> dailyTipsResSTOList = new ArrayList<>();
        long pageCount = 0L;
        pageCount = dailyTipsRepository.countDailyTips(commonListDTO.getQueryToSearch(), zodiacSign, isZodiacFilter, false);
        if (pageCount > 0) {
            dailyTipsResSTOList = dailyTipsRepository.findAllDailyTips(commonListDTO.getQueryToSearch(), pageable, zodiacSign, isZodiacFilter, false);
        }
        return new SearchResultDTO<>(dailyTipsResSTOList, pageCount, commonListDTO.getPage().getLimit());
    }

    /**
     * This cron will run at 12:00 AM UTC
     */
    @Override
    @Scheduled(cron = "0 0 0 * * *", zone = "UTC")
    public void updateDailyTipsScheduler() {
        try {
            logger.info("Starting the daily tips update scheduler");
            /** Fetch all unread tips sorted by the zodiacSign and title */
            List<EntityDailyTips> allDailyTips = dailyTipsRepository.findAllByIsReadAndIsActiveAndIsDeletedOrderByZodiacSignAscTitleAsc(false, true, false);
            if (allDailyTips == null || allDailyTips.isEmpty()) {
                logger.warn("No unread daily tips found");
                return;
            }
            Map<ZodiacSignType, List<EntityDailyTips>> zodiacDailyTipsMap = allDailyTips.stream()
                    .collect(Collectors.groupingBy(EntityDailyTips::getZodiacSign));

            zodiacDailyTipsMap.forEach((zodiacSignType, dailyTipsList) -> {
                if (!dailyTipsList.isEmpty()) {
                    try {
                        updateDailyTipsByZodiacSign(zodiacSignType, dailyTipsList);
                    } catch (Exception e) {
                        logger.error("Error updating tips for zodiac sign {}: {}", zodiacSignType, e.getMessage(), e);
                    }
                }
            });
            logger.info("Daily tips update scheduler completed successfully");
        } catch (Exception e) {
            logger.error("An error occurred while executing the daily tips scheduler: {}", e.getMessage(), e);
        }
    }

    private void updateDailyTipsByZodiacSign(ZodiacSignType zodiacSignType, List<EntityDailyTips> dailyTipsList) throws Exception {
        try {
            /** If less than 3 tips, update all daily tips for the zodiac sign */
            if (dailyTipsList.size() < 3) {
                dailyTipsRepository.updateAllDailyTipsByZodiacSign(zodiacSignType);
            } else {
                /** Limit to the first 3 tips */
                List<Long> tipsIdList = dailyTipsList.stream()
                        .limit(3)
                        .map(EntityDailyTips::getDailyTipsId)
                        .toList();
                /** Update only the first 3 tips */
                dailyTipsRepository.updateThreeDailyTipsById(zodiacSignType, tipsIdList);
            }
        } catch (Exception e) {
            logger.error("Failed to update daily tips for zodiac sign {}: {}", zodiacSignType, e.getMessage(), e);
            throw new Exception("Error updating daily tips for zodiac sign " + zodiacSignType, e);
        }
    }
}
