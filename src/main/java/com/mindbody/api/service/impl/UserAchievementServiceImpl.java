package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.*;
import com.mindbody.api.dto.notification.NotificationDTO;
import com.mindbody.api.enums.AchievementActivityType;
import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.enums.NotificationType;
import com.mindbody.api.enums.WarriorSubCategoryType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.*;
import com.mindbody.api.repository.*;
import com.mindbody.api.security.ExecutionContextUtil;
import com.mindbody.api.service.*;
import org.springframework.context.annotation.Lazy;
import com.mindbody.api.util.Constant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional
public class UserAchievementServiceImpl implements UserAchievementService {

    @Autowired
    private AchievementsRepository achievementsRepository;

    @Autowired
    private UserPointsTrackerRepository userPointsTrackerRepository;

    @Autowired
    private AchievementLevelUpRepository achievementLevelUpRepository;

    @Autowired
    private AchievementsService achievementsService;

    @Autowired
    private UserAchievementsRepository userAchievementsRepository;

    @Autowired
    private GlobalConfiguration globalConfiguration;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private NotificationImageRepository notificationImageRepository;

    @Autowired
    @Lazy
    private UserStatsService userStatsService;

    @Autowired
    private AchievementEventService achievementEventService;

    @Override
    public void submitAchievement(SubmitUserAchievementReqDTO submitUserAchievementReqDTO) {
        if (!existsByAchievementCategoryTypeAndUserIdAndIsDeleted(submitUserAchievementReqDTO.getAchievementActivityType(), submitUserAchievementReqDTO.getUserId())) {
            Optional<EntityAchievements> optionalAchievements = achievementsRepository.findByAchievementActivityTypeAndIsDeleted(submitUserAchievementReqDTO.getAchievementActivityType(), false);
            EntityAchievements achievements;

            if (optionalAchievements.isEmpty()) {
                throw new BusinessValidationException("achievement_not_found");
            }

            achievements = optionalAchievements.get();


            EntityUserAchievements eua = new EntityUserAchievements();

            achievements.getEntityAchievementMedals();
            achievements.getEntityAchievementMedals().getEntityAchievementRewards();
            achievements.getEntityAchievementBorder();
            //Get achievement points
            int achievementPoints = achievements.getEntityAchievementMedals().getEntityAchievementRewards().getPoints();
            int achievementWxp = achievements.getEntityAchievementMedals().getEntityAchievementRewards().getWxp();

            //Set user achievement points.
            eua.setAchievementCategoryType(achievements.getAchievementCategoryType());
            eua.setMindSubCategoryType(achievements.getMindSubCategoryType().toString());
            eua.setBodySubCategoryType(achievements.getBodySubCategoryType().toString());
            eua.setWarriorSubCategoryType(achievements.getWarriorSubCategoryType().toString());
            eua.setAchievementTitleId(achievements.getEntityAchievementTitle());
            eua.setAchievementBorderId(achievements.getEntityAchievementBorder());
            eua.setAchievementMedalId(achievements.getEntityAchievementMedals());
            eua.setUserId(submitUserAchievementReqDTO.getUserId());
            eua.setActivityDetails(achievements.getActivityDetails());
            eua.setAchievementActivityType(achievements.getAchievementActivityType());
            eua.setEntityAchievements(achievements);
            eua.setEntityAudioPlaylist(achievements.getEntityAudioPlaylist());
            eua.setEntityWorkoutPlaylist(achievements.getEntityWorkoutPlaylist());
            eua.setEntityWarriorSet(achievements.getEntityWarriorSet());
            eua.setPointsEarned(achievementPoints);
            eua.setWxpEarned(achievementWxp);
            eua.setAchievementBorderId(achievements.getEntityAchievementBorder());
            eua.setAcquiredDate(new Date());

            userAchievementsRepository.save(eua);
            EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(submitUserAchievementReqDTO.getUserId(), true, false);
            if (Objects.nonNull(entityUser)) {
                String baseS3Url = globalConfiguration.getAmazonS3().getUrlUpToBucket()
                        + globalConfiguration.getAmazonS3().getUploadFolderName();
                Map<String, String> notificationInfo = new HashMap<>();
                NotificationDTO notificationDTO = new NotificationDTO();
                if (achievements.getAchievementCategoryType().equals(AchievementCategoryType.Mind)) {
                    notificationDTO.setNotificationType(NotificationType.MIND_ACHIEVEMENT.name());
                    if (achievements.getEntityAudioPlaylist().getAudioPlaylistImage() != null) {
                        notificationDTO.setImageKey(achievements.getEntityAudioPlaylist().getAudioPlaylistImage());
                        String folderName = globalConfiguration.getAmazonS3().getAudioPlaylistFolderName();
                        notificationDTO.setImageUrl(baseS3Url + folderName + achievements.getEntityAudioPlaylist().getAudioPlaylistImage());
                    }
                } else if (achievements.getAchievementCategoryType().equals(AchievementCategoryType.Body)) {
                    notificationDTO.setNotificationType(NotificationType.BODY_ACHIEVEMENT.name());
                    if (achievements.getEntityWorkoutPlaylist().getWorkoutPlaylistImage() != null) {
                        notificationDTO.setImageKey(achievements.getEntityWorkoutPlaylist().getWorkoutPlaylistImage());
                        String folderName = globalConfiguration.getAmazonS3().getWorkoutPlaylistFolderName();
                        notificationDTO.setImageUrl(baseS3Url + folderName + achievements.getEntityWorkoutPlaylist().getWorkoutPlaylistImage());
                    }
                } else if (achievements.getAchievementCategoryType().equals(AchievementCategoryType.Warrior)) {
                    String folderName = globalConfiguration.getAmazonS3().getNotificationImageFolderName();
                    if (achievements.getWarriorSubCategoryType().equals(WarriorSubCategoryType.Astrology)) {
                        notificationDTO.setNotificationType(NotificationType.WARRIOR_ACHIEVEMENT_ASTROLOGY.name());
                        EntityNotificationImage notificationImage = notificationImageRepository.findByNotificationType(NotificationType.WARRIOR_ACHIEVEMENT_ASTROLOGY);
                        if (notificationImage != null) {
                            notificationDTO.setImageKey(notificationImage.getImage());
                            notificationDTO.setImageUrl(baseS3Url + folderName + notificationImage.getImage());
                        }
                    } else if (achievements.getWarriorSubCategoryType().equals(WarriorSubCategoryType.Connection)) {
                        notificationDTO.setNotificationType(NotificationType.WARRIOR_ACHIEVEMENT_CONNECTION.name());
                        EntityNotificationImage notificationImage = notificationImageRepository.findByNotificationType(NotificationType.WARRIOR_ACHIEVEMENT_CONNECTION);
                        if (notificationImage != null) {
                            notificationDTO.setImageKey(notificationImage.getImage());
                            notificationDTO.setImageUrl(baseS3Url + folderName + notificationImage.getImage());
                        }
                    } else if (achievements.getWarriorSubCategoryType().equals(WarriorSubCategoryType.Empowerment)) {
                        notificationDTO.setNotificationType(NotificationType.WARRIOR_ACHIEVEMENT_EMPOWERMENT.name());
                        EntityNotificationImage notificationImage = notificationImageRepository.findByNotificationType(NotificationType.WARRIOR_ACHIEVEMENT_EMPOWERMENT);
                        if (notificationImage != null) {
                            notificationDTO.setImageKey(notificationImage.getImage());
                            notificationDTO.setImageUrl(baseS3Url + folderName + notificationImage.getImage());
                        }
                    }
                }
                notificationDTO.setTitle(Constant.ACHIEVEMENT_UNLOCKED);
                notificationDTO.setMessage(achievements.getActivityDetails());

                notificationInfo.put("title", notificationDTO.getTitle());
                notificationInfo.put("message", notificationDTO.getMessage());
                notificationInfo.put("notificationType", notificationDTO.getNotificationType());
                notificationInfo.put("userId", submitUserAchievementReqDTO.getUserId().toString());
                notificationInfo.put("achievementActivityType", achievements.getAchievementActivityType().name());

                notificationDTO.setData(notificationInfo);
                notificationService.sendNotificationToAppUser(entityUser, notificationDTO);
                userStatsService.saveCategoryStatsForUser(new UserStatsTimeSpentReqDTO(submitUserAchievementReqDTO.getUserId(),null,null, (long) achievementWxp,AchievementCategoryType.Warrior.name()));
            }
        }
    }


    @Override
    public void claimUserAchievement(ClaimAchievementReqDTO claimAchievementReqDTO) {
        Optional<EntityUserAchievements> optionalUserAchievements = userAchievementsRepository.findByUserAchievementsIdAndUserIdAndIsDeleted(claimAchievementReqDTO.getUserAchievementId(), claimAchievementReqDTO.getUserId(), false);
        if (optionalUserAchievements.isEmpty()) {
            throw new BusinessValidationException("user_achievement_not_found");
        }
        EntityUserAchievements eua = optionalUserAchievements.get();
        eua.setClaimed(true);
        userAchievementsRepository.save(eua);
        if (!existsByAchievementCategoryTypeAndUserIdAndIsDeleted(eua.getAchievementActivityType(), claimAchievementReqDTO.getUserId())) {
            submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Account_Claim_Achievement, claimAchievementReqDTO.getUserId()));
        }
    }

    @Override
    public UserTitlesResDTO getUserTitlesData(UserIdDTO userIdDTO) {

        List<UserTitlesDTO> userTitlesDTO = userAchievementsRepository.findUserAchievementTitleByUserId(userIdDTO.getUserId());
        List<CategoryAndCountDTO> allCategoryAndCount = achievementsRepository.countAchievementsByCategoriesAndIsDeleted(false);
        List<CategoryAndCountDTO> userCategoryAndCount = userAchievementsRepository.countAchievementsByCategoriesAndUserIdAndIsDeleted(userIdDTO.getUserId(), false);
        return new UserTitlesResDTO(userTitlesDTO, allCategoryAndCount, userCategoryAndCount);

    }

    @Override
    public UserAchievementByIdDTO getUserAchievementByEnumAndUserId(UserAchievementReqDTO userAchievementReqDTO) {

        UserAchievementByIdDTO achievementByIdDTO = new UserAchievementByIdDTO();

        Optional<UserAchievementDTO> optionalUserAchievement = userAchievementsRepository.findByUserIdAndAchievementActivityType(userAchievementReqDTO.getUserId(), userAchievementReqDTO.getAchievementActivityType());
        UserAchievementDTO userAchievements;

        Optional<EntityUserPointsTracker> optionalUserPointsTracker = userPointsTrackerRepository.findByUserIdAndIsDeleted(userAchievementReqDTO.getUserId(), false);
        EntityUserPointsTracker userPointsTracker;


        if (optionalUserAchievement.isEmpty()) {
            throw new BusinessValidationException("user_achievement_not_found");
        }

        userAchievements = optionalUserAchievement.get();

        UserAchievementDTO userAchievementDTO = optionalUserAchievement.get();
        achievementByIdDTO.setUserAchievementDTO(userAchievementDTO);

        //Get User Achievement Points
        long achievementPoints = userAchievements.getPointsEarned();
        long achievementWxp = userAchievements.getWxpEarned();


        if (optionalUserPointsTracker.isPresent()) {
            userPointsTracker = optionalUserPointsTracker.get();

            //Get current user points
            long currentTotalPoints = userPointsTracker.getTotalPoints();
            long currentTotalWxp = userPointsTracker.getTotalWxp();
            long currentLevel = userPointsTracker.getCurrentLevel();

            //Set current level as previous level
            achievementByIdDTO.setPreviousWxp(currentTotalWxp);
            achievementByIdDTO.setPreviousLevel(currentLevel);

            //Add achievement points to current points
            userPointsTracker.setTotalPoints(currentTotalPoints + achievementPoints);
            userPointsTracker.setTotalWxp(currentTotalWxp + achievementWxp);
            userPointsTracker.setUserId(ExecutionContextUtil.getContext().getUserId());

            //check if points exceeds the level.(get next level from current points)
            Map<String, Long> currentAndNextLevel = getCurrentAndNextLevelMap((long) (currentTotalWxp + achievementWxp));

            userPointsTracker.setCurrentLevel(currentAndNextLevel.get(Constant.CURRENT_LEVEL));

            userPointsTrackerRepository.save(userPointsTracker);

            achievementByIdDTO.setCurrentLevelWxp(currentAndNextLevel.get(Constant.CURRENT_LEVEL_WXP_POINT));
            achievementByIdDTO.setCurrentLevel(currentAndNextLevel.get(Constant.CURRENT_LEVEL));


            achievementByIdDTO.setNextLevelWxp(currentAndNextLevel.get(Constant.NEXT_LEVEL) == null ? null : currentAndNextLevel.get(Constant.NEXT_LEVEL_WXP_POINT));
            achievementByIdDTO.setNextLevel(currentAndNextLevel.get(Constant.NEXT_LEVEL) == null ? null : currentAndNextLevel.get(Constant.NEXT_LEVEL));

            achievementByIdDTO.setCurrentPoint(userPointsTracker.getTotalPoints());
            achievementByIdDTO.setCurrentWxp(userPointsTracker.getTotalWxp());
            achievementByIdDTO.setCurrentLevel(userPointsTracker.getCurrentLevel());

        } else {

            Map<String, Long> levelAndPoints = getCurrentAndNextLevelMap(achievementWxp);

            //Create new object EntityUserPointsTracker and save it.
            EntityUserPointsTracker entityUserPointsTracker = new EntityUserPointsTracker();

            //Add achievement points to current points and update level to EntityUserPointsTracker.
            entityUserPointsTracker.setTotalPoints(achievementPoints);
            entityUserPointsTracker.setTotalWxp(achievementWxp);
            entityUserPointsTracker.setUserId(userAchievementReqDTO.getUserId());
            entityUserPointsTracker.setCurrentLevel(levelAndPoints.get(Constant.CURRENT_LEVEL));

            userPointsTrackerRepository.save(entityUserPointsTracker);

            achievementByIdDTO.setPreviousLevel(1L);
            achievementByIdDTO.setPreviousWxp(1L);

            Map<String, Long> currentAndNextLevel = getCurrentAndNextLevelMap(achievementWxp);
            achievementByIdDTO.setCurrentLevelWxp(currentAndNextLevel.get(Constant.CURRENT_LEVEL_WXP_POINT));
            achievementByIdDTO.setCurrentLevel(currentAndNextLevel.get(Constant.CURRENT_LEVEL));

            achievementByIdDTO.setNextLevelWxp(currentAndNextLevel.get(Constant.NEXT_LEVEL) == null ? null : currentAndNextLevel.get(Constant.NEXT_LEVEL_WXP_POINT));
            achievementByIdDTO.setNextLevel(currentAndNextLevel.get(Constant.NEXT_LEVEL) == null ? null : currentAndNextLevel.get(Constant.NEXT_LEVEL));

            achievementByIdDTO.setCurrentPoint(entityUserPointsTracker.getTotalPoints());
            achievementByIdDTO.setCurrentWxp(entityUserPointsTracker.getTotalWxp());
            achievementByIdDTO.setCurrentLevel(entityUserPointsTracker.getCurrentLevel());
        }

        if (achievementByIdDTO.getCurrentLevel() >= 3 && !existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Levels_Reach_3, userAchievementReqDTO.getUserId())) {
            submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Levels_Reach_3, userAchievementReqDTO.getUserId()));
        }
        if (achievementByIdDTO.getCurrentLevel() >= 6 && !existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Levels_Reach_6, userAchievementReqDTO.getUserId())) {
            submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Levels_Reach_6, userAchievementReqDTO.getUserId()));
        }
        if (achievementByIdDTO.getCurrentLevel() >= 10 && !existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Levels_Reach_10, userAchievementReqDTO.getUserId())) {
            submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Levels_Reach_10, userAchievementReqDTO.getUserId()));
        }
        if (achievementByIdDTO.getCurrentLevel() >= 20 && !existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Levels_Reach_20, userAchievementReqDTO.getUserId())) {
            submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Levels_Reach_20, userAchievementReqDTO.getUserId()));
        }
        if (achievementByIdDTO.getCurrentLevel() >= 50 && !existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Levels_Reach_50, userAchievementReqDTO.getUserId())) {
            submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Levels_Reach_50, userAchievementReqDTO.getUserId()));
        }

        if (achievementByIdDTO.getCurrentPoint() >= 200 && !existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Points_Earn_200, userAchievementReqDTO.getUserId())) {
            submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Points_Earn_200, userAchievementReqDTO.getUserId()));
        }
        if (achievementByIdDTO.getCurrentPoint() >= 400 && !existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Points_Earn_400, userAchievementReqDTO.getUserId())) {
            submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Points_Earn_400, userAchievementReqDTO.getUserId()));
        }
        if (achievementByIdDTO.getCurrentPoint() >= 800 && !existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Points_Earn_800, userAchievementReqDTO.getUserId())) {
            submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Points_Earn_800, userAchievementReqDTO.getUserId()));
        }
        if (achievementByIdDTO.getCurrentPoint() >= 1000 && !existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Points_Earn_1000, userAchievementReqDTO.getUserId())) {
            submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Points_Earn_1000, userAchievementReqDTO.getUserId()));
        }
        if (achievementByIdDTO.getCurrentPoint() >= 5000 && !existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Points_Earn_5000, userAchievementReqDTO.getUserId())) {
            submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Points_Earn_5000, userAchievementReqDTO.getUserId()));
        }


        return achievementByIdDTO;

    }

    //    @Override
//    public Map<String,EntityAchievementLevelUp> getCurrentAndNextLevel(int wxpEarned) {
//        List<EntityAchievementLevelUp> levels = achievementLevelUpRepository.findAllOrderedByWxpNeeded();
//
//        EntityAchievementLevelUp currentLevel = levels.get(0);
//        for (EntityAchievementLevelUp level : levels) {
//            int requiredWxp = level.getWxpNeeded();
//            if (wxpEarned >= requiredWxp) {
//                currentLevel = level;
//            } else {
//                break; // Stop when we find a level that requires more wxp than the user has
//            }
//        }
//        Map<String,EntityAchievementLevelUp> map = new HashMap<>();
//        map.put(Constant.CURRENT_LEVEL,currentLevel);
//        if (levels.contains(currentLevel) && levels.indexOf(currentLevel) < levels.size() - 1) {
//            EntityAchievementLevelUp nextLevel = levels.get(levels.indexOf(currentLevel) + 1);
//            map.put(Constant.NEXT_LEVEL,nextLevel);
//        }else {
//            map.put(Constant.NEXT_LEVEL,null);
//        }
//
//        return map;
//    }
    @Override
    public Map<String, Long> getCurrentAndNextLevelMap(Long wxpEarned) {
        long newPoint = 300;
        long currentWxp = 0;
        long currentLevel = 1;
        for (long i = 2; i < wxpEarned; i++) {
//            System.out.println("Level:" + i + " Points: " + newPoint);
            if (newPoint >= wxpEarned) {
                currentLevel = i - 1;
                break;
            }
            currentWxp = newPoint;
            newPoint = Math.round(newPoint * 1.2);
        }
        Map<String, Long> map = new HashMap<>();
        map.put(Constant.CURRENT_LEVEL, currentLevel);
        map.put(Constant.CURRENT_LEVEL_WXP_POINT, currentWxp);
        map.put(Constant.NEXT_LEVEL, currentLevel + 1);
        map.put(Constant.NEXT_LEVEL_WXP_POINT, newPoint);
        return map;
    }

    @Override
    public UserBadgesResDTO getUserBadgesData(UserIdDTO userIdDTO) {
        String badgeImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBadgeFolderName();
        List<UserBadgeDTO> userBadgesDTO = userAchievementsRepository.findUserAchievementBadgeByUserId(badgeImagePath, userIdDTO.getUserId());
        List<CategoryAndCountDTO> allCategoryAndCount = achievementsRepository.countAchievementsByCategoriesByBadgesAndIsDeleted(false);
        List<CategoryAndCountDTO> userCategoryAndCount = userAchievementsRepository.countAchievementsByCategoriesAndUserIdAndIsDeletedForBadges(userIdDTO.getUserId(), false);
        return new UserBadgesResDTO(userBadgesDTO, allCategoryAndCount, userCategoryAndCount);

    }

    @Override
    public UserBorderResDTO getUserBorderData(UserIdDTO userIdDTO) {
        String borderImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBorderFolderName();
        List<UserBorderDTO> userBadgesDTO = userAchievementsRepository.findUserAchievementBorderByUserId(borderImagePath, userIdDTO.getUserId());
        List<CategoryAndCountDTO> allCategoryAndCount = achievementsRepository.countAchievementsByCategoriesByBorderAndIsDeleted(false);
        List<CategoryAndCountDTO> userCategoryAndCount = userAchievementsRepository.countAchievementsByCategoriesAndUserIdAndIsDeletedForBorders(userIdDTO.getUserId(), false);
        return new UserBorderResDTO(userBadgesDTO, allCategoryAndCount, userCategoryAndCount);

    }

    @Override
    public boolean existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType achievementActivityType, Long userId) {
        // Delegate to the achievement event service to check if the user has the achievement
        return achievementEventService.hasAchievement(achievementActivityType, userId);
    }

    @Override
    public List<AchievementActivityType> findAchievementActivityTypeByUserIdAndIsClaimedAndIsDeleted(Long userId, boolean isClaimed, boolean isDeleted) {
        return userAchievementsRepository.findAchievementActivityTypeByUserIdAndIsClaimedAndIsDeleted(userId, isClaimed, isDeleted);
    }

    @Override
    public SearchResultDTO<UserAllAchievementDTO> findAllUserAchievementsByUserIdForCMS(UserAchievementListReqDTO userAchievementListReqDTO) {
        Sort sort = null;
        if (userAchievementListReqDTO.getSortBy() != null && userAchievementListReqDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(userAchievementListReqDTO.getSortBy().getDirection(), userAchievementListReqDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(userAchievementListReqDTO.getPage().getPageId(), userAchievementListReqDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());
        List<UserAllAchievementDTO> userAllAchievementDTO = new ArrayList<>();
        long pageCount = 0L;
        pageCount = userAchievementsRepository.countUserAchievementsByUserId(userAchievementListReqDTO.getUserId());
        if (pageCount > 0) {
            userAllAchievementDTO = userAchievementsRepository.findAllUserAchievementsByUserId(userAchievementListReqDTO.getUserId(), pageable);
        }
        return new SearchResultDTO<>(userAllAchievementDTO, pageCount, userAchievementListReqDTO.getPage().getLimit());
    }
}
