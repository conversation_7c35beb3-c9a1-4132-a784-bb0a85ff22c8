package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.UploadedFileNameResponseDTO;
import com.mindbody.api.dto.cms.*;
import com.mindbody.api.dto.notification.NotificationDTO;
import com.mindbody.api.enums.*;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.WorkoutPlanMapper;
import com.mindbody.api.model.*;
import com.mindbody.api.repository.*;
import com.mindbody.api.security.ExecutionContextUtil;
import com.mindbody.api.service.*;
import com.mindbody.api.util.StringUtil;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class WorkoutPlanServiceImpl extends BaseService implements WorkoutPlanService {

    private final WorkoutPlanRepository workoutPlanRepository;

    private final FavoriteWorkoutPlanRepository favoriteWorkoutPlanRepository;

    private final WorkoutPlanZodiacRepository workoutPlanZodiacRepository;

    private final WorkoutPlanExerciseRepository workoutPlanExerciseRepository;

    private final WorkoutPlaylistRepository workoutPlaylistRepository;

    private final ExerciseRepository exerciseRepository;

    private final WorkoutPlanMapper workoutPlanMapper;

    private final AWSS3Service awss3Service;

    private final GlobalConfiguration globalConfiguration;

    private final NotificationService notificationService;

    public WorkoutPlanServiceImpl(MessageService messageService, WorkoutPlanRepository workoutPlanRepository, FavoriteWorkoutPlanRepository favoriteWorkoutPlanRepository, WorkoutPlanZodiacRepository workoutPlanZodiacRepository, WorkoutPlanExerciseRepository workoutPlanExerciseRepository, WorkoutPlaylistRepository workoutPlaylistRepository, ExerciseRepository exerciseRepository, WorkoutPlanMapper workoutPlanMapper, AWSS3Service awss3Service, GlobalConfiguration globalConfiguration, NotificationService notificationService) {
        super(messageService);
        this.workoutPlanRepository = workoutPlanRepository;
        this.favoriteWorkoutPlanRepository = favoriteWorkoutPlanRepository;
        this.workoutPlanZodiacRepository = workoutPlanZodiacRepository;
        this.workoutPlanExerciseRepository = workoutPlanExerciseRepository;
        this.workoutPlaylistRepository = workoutPlaylistRepository;
        this.exerciseRepository = exerciseRepository;
        this.workoutPlanMapper = workoutPlanMapper;
        this.awss3Service = awss3Service;
        this.globalConfiguration = globalConfiguration;
        this.notificationService = notificationService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addWorkoutPlan(AddWorkoutPlanReqDTO addWorkoutPlanReqDTO) {
        if (Objects.isNull(addWorkoutPlanReqDTO.getWorkoutVideoThumbnailImage())) {
            throw new BusinessValidationException("workout_video_thumbnail_image_required");
        }
        if (addWorkoutPlanReqDTO.getWorkoutPlanType().equals(WorkoutPlanType.SINGLE_VIDEO_PLAN.name()) && !Objects.isNull(addWorkoutPlanReqDTO.getWorkoutVideo())) {
            validateAddWorkoutPlanDTO(addWorkoutPlanReqDTO);
        } else if (addWorkoutPlanReqDTO.getWorkoutPlanType().equals(WorkoutPlanType.MULTI_EXERCISE_PLAN.name()) && addWorkoutPlanReqDTO.getExerciseList().isEmpty()) {
            throw new BusinessValidationException("select_at_least_one_exercise_video");
        }
        Optional<EntityWorkoutPlan> entityWorkoutPlan = workoutPlanRepository.findByWorkoutPlanTitleAndIsDeleted(addWorkoutPlanReqDTO.getWorkoutPlanTitle(), false);
        if (entityWorkoutPlan.isPresent()) {
            throw new BusinessValidationException("workout_plan_already_exist");
        }
        EntityWorkoutPlan workoutPlan = workoutPlanMapper.toModel(addWorkoutPlanReqDTO);
        EntityWorkoutPlaylist entityWorkoutPlaylist = workoutPlaylistRepository.findByWorkoutPlaylistIdAndIsDeleted(addWorkoutPlanReqDTO.getWorkoutPlaylistId(), false);
        if (Objects.isNull(entityWorkoutPlaylist)) {
            throw new BusinessValidationException("workout_playlist_not_found");
        }
        if (!addWorkoutPlanReqDTO.getWorkoutPlaylistType().equalsIgnoreCase(String.valueOf(entityWorkoutPlaylist.getWorkoutPlaylistType()))) {
            throw new BusinessValidationException("workout_playlist_module_different_error");
        }
        workoutPlan.setEntityWorkoutPlaylist(entityWorkoutPlaylist);

        /** Set Total Workout Plan Duration **/
        workoutPlan.setWorkoutPlanDuration(addWorkoutPlanReqDTO.getWorkoutPlanDuration());

        /** Add workout video thubnail image **/
        if (addWorkoutPlanReqDTO.getWorkoutVideoThumbnailImage() != null && !addWorkoutPlanReqDTO.getWorkoutVideoThumbnailImage().isEmpty()) {
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(addWorkoutPlanReqDTO.getWorkoutVideoThumbnailImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutVideoThumbnailFolderName(), ImageType.workoutVideoThumbnail);
            workoutPlan.setWorkoutVideoThumbnailImage(uploadedFileName.getImageName());
        }
        if (addWorkoutPlanReqDTO.getWorkoutPlanType().equals(WorkoutPlanType.SINGLE_VIDEO_PLAN.name())) {
            if (StringUtil.nonNullNonEmpty(addWorkoutPlanReqDTO.getWorkoutVideo())) {
//                UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(addWorkoutPlanReqDTO.getWorkoutVideo(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutVideoFolderName(), ImageType.workoutVideo);
                workoutPlan.setWorkoutVideo(addWorkoutPlanReqDTO.getWorkoutVideo());
            }
            workoutPlanRepository.save(workoutPlan);
        } else {
            /** Save workout plan**/
            workoutPlanRepository.save(workoutPlan);

            /** Save exercises for workout plan **/
            List<EntityWorkoutPlanExercise> entityWorkoutPlanExerciseList = new ArrayList<>();

            // Extract exercise IDs and durations from ExerciseListDTO
            for (ExerciseListDTO exerciseDTO : addWorkoutPlanReqDTO.getExerciseList()) {
                EntityExercise exercise = exerciseRepository.findById(exerciseDTO.getExerciseId())
                        .orElseThrow(() -> new BusinessValidationException("exercise_not_found"));

                EntityWorkoutPlanExercise workoutPlanExercise = new EntityWorkoutPlanExercise();
                workoutPlanExercise.setEntityWorkoutPlan(workoutPlan);
                workoutPlanExercise.setWorkoutPlanId(workoutPlan.getWorkoutPlanId());
                workoutPlanExercise.setEntityExercise(exercise);
                workoutPlanExercise.setExerciseId(exercise.getExerciseId());
                workoutPlanExercise.setExerciseDuration(exerciseDTO.getExerciseDuration());
                entityWorkoutPlanExerciseList.add(workoutPlanExercise);
            }

            // Save all exercises linked to this workout plan
            workoutPlanExerciseRepository.saveAll(entityWorkoutPlanExerciseList);
        }

        /** Store multiple zodiac signs for workout plan **/
        List<EntityWorkoutPlanZodiacSign> zodiacSignEntities = new ArrayList<>();
        for (String zodiacSignString : addWorkoutPlanReqDTO.getZodiacSigns()) {
            try {
                ZodiacSignType zodiacSignType = ZodiacSignType.valueOf(zodiacSignString);
                EntityWorkoutPlanZodiacSign workoutPlanZodiacSign = new EntityWorkoutPlanZodiacSign();
                workoutPlanZodiacSign.setZodiacSign(zodiacSignType);
                workoutPlanZodiacSign.setEntityWorkoutPlan(workoutPlan);
                zodiacSignEntities.add(workoutPlanZodiacSign);
            } catch (Exception e) {
                throw new BusinessValidationException("invalid_zodiac_sign");
            }
        }

        // Save all zodiac signs
        workoutPlanZodiacRepository.saveAll(zodiacSignEntities);

        /** Send Notification To Subscribed Zodiac Sign Users Which is mapped with Workout Plan **/
        NotificationDTO notificationDTO = notificationService.setNewWorkoutPlanNotificationData(true,workoutPlan.getWorkoutPlanId());
        String[] topics = addWorkoutPlanReqDTO.getZodiacSigns()
                .stream()
                .map(String::toUpperCase)
                .toArray(String[]::new);
        if (topics.length > 0) {
            notificationDTO.setTopicNames(topics);
            notificationService.sendNotificationToUsersByAdmin(notificationDTO, null, null);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editWorkoutPlan(EditWorkoutPlanReqDTO editWorkoutPlanReqDTO) {
        EntityWorkoutPlan entityWorkoutPlan = workoutPlanRepository.findByWorkoutPlanIdAndIsDeleted(editWorkoutPlanReqDTO.getWorkoutPlanId(), false);
        if (Objects.isNull(entityWorkoutPlan)) {
            throw new BusinessValidationException("workout_plan_not_found");
        }
        entityWorkoutPlan.setWorkoutPlanTitle(editWorkoutPlanReqDTO.getWorkoutPlanTitle());
        entityWorkoutPlan.setWorkoutPlanDescription(editWorkoutPlanReqDTO.getWorkoutPlanDescription());
        entityWorkoutPlan.setEquipment(editWorkoutPlanReqDTO.getEquipment());
        entityWorkoutPlan.setDifficultyLevel(DifficultyLevel.valueOf(editWorkoutPlanReqDTO.getDifficultyLevel()));
        entityWorkoutPlan.setWorkoutPlaylistType(WorkoutPlaylistType.valueOf(editWorkoutPlanReqDTO.getWorkoutPlaylistType()));

        EntityWorkoutPlaylist entityWorkoutPlaylist = workoutPlaylistRepository.findByWorkoutPlaylistIdAndIsDeleted(editWorkoutPlanReqDTO.getWorkoutPlaylistId(), false);
        if (Objects.isNull(entityWorkoutPlaylist)) {
            throw new BusinessValidationException("workout_playlist_not_found");
        }
        if (!editWorkoutPlanReqDTO.getWorkoutPlaylistType().equalsIgnoreCase(String.valueOf(entityWorkoutPlaylist.getWorkoutPlaylistType()))) {
            throw new BusinessValidationException("workout_playlist_module_different_error");
        }
        entityWorkoutPlan.setEntityWorkoutPlaylist(entityWorkoutPlaylist);
        entityWorkoutPlan.setWorkoutPlaylistId(entityWorkoutPlaylist.getWorkoutPlaylistId());

        /** Update workout video thumbnail image **/
        String newWorkoutVideoThumbnailImage = "";
        if (editWorkoutPlanReqDTO.getWorkoutVideoThumbnailImage() != null && !editWorkoutPlanReqDTO.getWorkoutVideoThumbnailImage().isEmpty()) {
            if (!StringUtil.nullOrEmpty(entityWorkoutPlan.getWorkoutVideoThumbnailImage())) {
                awss3Service.deleteFile(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutVideoThumbnailFolderName() + entityWorkoutPlan.getWorkoutVideoThumbnailImage(), ImageType.workoutVideoThumbnail);
            }
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(editWorkoutPlanReqDTO.getWorkoutVideoThumbnailImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutVideoThumbnailFolderName(), ImageType.workoutVideoThumbnail);
            newWorkoutVideoThumbnailImage = uploadedFileName.getImageName();
        }
        if (StringUtil.nonNullNonEmpty(newWorkoutVideoThumbnailImage)) {
            entityWorkoutPlan.setWorkoutVideoThumbnailImage(newWorkoutVideoThumbnailImage);
        }
        /** Update workout plan duration **/
        entityWorkoutPlan.setWorkoutPlanDuration(editWorkoutPlanReqDTO.getWorkoutPlanDuration());

        if (entityWorkoutPlan.getWorkoutPlanType().equals(WorkoutPlanType.SINGLE_VIDEO_PLAN)) {
            /** Update workout video **/
            String newWorkoutVideo = "";
            if (StringUtil.nonNullNonEmpty(editWorkoutPlanReqDTO.getWorkoutVideo())) {
                if (StringUtil.nonNullNonEmpty(entityWorkoutPlan.getWorkoutVideo())) {
                    awss3Service.deleteFile(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutVideoFolderName() + entityWorkoutPlan.getWorkoutVideo(), ImageType.workoutVideo);
                }
//                UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(editWorkoutPlanReqDTO.getWorkoutVideo(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutVideoFolderName(), ImageType.workoutVideo);
                newWorkoutVideo = editWorkoutPlanReqDTO.getWorkoutVideo();
            }
            if (StringUtil.nonNullNonEmpty(newWorkoutVideo)) {
                entityWorkoutPlan.setWorkoutVideo(newWorkoutVideo);
            }
        } else {
            /** Delete existing exercise **/
            List<Long> deleteExerciseVideoIds = editWorkoutPlanReqDTO.getDeleteExerciseVideoIds();
            if (deleteExerciseVideoIds != null && !deleteExerciseVideoIds.isEmpty()) {
                List<EntityWorkoutPlanExercise> existingExerciseList = workoutPlanExerciseRepository.findByWorkoutPlanIdAndExerciseIdIn(entityWorkoutPlan.getWorkoutPlanId(), deleteExerciseVideoIds);
                workoutPlanExerciseRepository.deleteAll(existingExerciseList);
            }

            List<EntityWorkoutPlanExercise> entityWorkoutPlanExerciseList = new ArrayList<>();
            if (editWorkoutPlanReqDTO.getExerciseList() != null && !editWorkoutPlanReqDTO.getExerciseList().isEmpty()) {
                for (ExerciseListDTO exerciseListDTO : editWorkoutPlanReqDTO.getExerciseList()) {
                    /** Update the existing exercise */
                    EntityWorkoutPlanExercise entityWorkoutPlanExercise = workoutPlanExerciseRepository.findByWorkoutPlanIdAndExerciseId(entityWorkoutPlan.getWorkoutPlanId(), exerciseListDTO.getExerciseId());
                    if (Objects.nonNull(entityWorkoutPlanExercise)) {
                        entityWorkoutPlanExercise.setExerciseDuration(exerciseListDTO.getExerciseDuration());
                    }
                    /** Add new exercise **/
                    else {
                        EntityExercise exercise = exerciseRepository.findById(exerciseListDTO.getExerciseId())
                                .orElseThrow(() -> new BusinessValidationException("exercise_not_found"));
                        entityWorkoutPlanExercise = new EntityWorkoutPlanExercise();
                        entityWorkoutPlanExercise.setEntityWorkoutPlan(entityWorkoutPlan);
                        entityWorkoutPlanExercise.setWorkoutPlanId(entityWorkoutPlan.getWorkoutPlanId());
                        entityWorkoutPlanExercise.setEntityExercise(exercise);
                        entityWorkoutPlanExercise.setExerciseId(exercise.getExerciseId());
                        entityWorkoutPlanExercise.setExerciseDuration(exerciseListDTO.getExerciseDuration());
                    }
                    entityWorkoutPlanExerciseList.add(entityWorkoutPlanExercise);
                }

                workoutPlanExerciseRepository.saveAll(entityWorkoutPlanExerciseList);
            }
        }
        EntityWorkoutPlan savedWorkoutPlan = workoutPlanRepository.save(entityWorkoutPlan);

        /** Delete Requested Zodiac Signs From EntityWorkoutPlanZodiacSign table **/
        List<String> deleteZodiacSigns = editWorkoutPlanReqDTO.getDeleteZodiacSigns();
        if (deleteZodiacSigns != null && !deleteZodiacSigns.isEmpty()) {
            List<ZodiacSignType> deleteZodiacSignTypes = deleteZodiacSigns.stream()
                    .map(ZodiacSignType::valueOf)
                    .collect(Collectors.toList());
            List<EntityWorkoutPlanZodiacSign> existingZodiacSigns = workoutPlanZodiacRepository.findByEntityWorkoutPlan_WorkoutPlanIdAndZodiacSignIn(savedWorkoutPlan.getWorkoutPlanId(), deleteZodiacSignTypes);
            workoutPlanZodiacRepository.deleteAll(existingZodiacSigns);
        }

        if (editWorkoutPlanReqDTO.getZodiacSigns() != null && !editWorkoutPlanReqDTO.getZodiacSigns().isEmpty()) {
            List<ZodiacSignType> requestedZodiacSigns = editWorkoutPlanReqDTO.getZodiacSigns().stream()
                    .map(ZodiacSignType::valueOf)
                    .collect(Collectors.toList());

            // Fetch existing zodiac signs for the workout plan
            List<EntityWorkoutPlanZodiacSign> existingZodiacSigns = workoutPlanZodiacRepository.findByEntityWorkoutPlan_WorkoutPlanIdAndZodiacSignIn(entityWorkoutPlan.getWorkoutPlanId(), requestedZodiacSigns);

            Set<ZodiacSignType> existingZodiacSignSet = existingZodiacSigns.stream()
                    .map(EntityWorkoutPlanZodiacSign::getZodiacSign)
                    .collect(Collectors.toSet());

            // Filter out zodiac signs that are already in the database
            List<EntityWorkoutPlanZodiacSign> filteredAudioZodiacSignList = requestedZodiacSigns.stream()
                    .filter(zodiacSign -> !existingZodiacSignSet.contains(zodiacSign)) // Keep only the missing ones
                    .map(zodiacSign -> {
                        EntityWorkoutPlanZodiacSign entityWorkoutPlanZodiacSign = new EntityWorkoutPlanZodiacSign();
                        entityWorkoutPlanZodiacSign.setEntityWorkoutPlan(entityWorkoutPlan);
                        entityWorkoutPlanZodiacSign.setZodiacSign(zodiacSign);
                        return entityWorkoutPlanZodiacSign;
                    })
                    .collect(Collectors.toList());

            // Save all new zodiac signs
            if (!filteredAudioZodiacSignList.isEmpty()) {
                workoutPlanZodiacRepository.saveAll(filteredAudioZodiacSignList);
            }
        }

        /** Send Notification To Subscribed Zodiac Sign Users Which is mapped with Workout Plan **/
        NotificationDTO notificationDTO = notificationService.setNewWorkoutPlanNotificationData(false,savedWorkoutPlan.getWorkoutPlanId());
        String[] topics = editWorkoutPlanReqDTO.getZodiacSigns()
                .stream()
                .map(String::toUpperCase)
                .toArray(String[]::new);
        if (topics.length > 0) {
            notificationDTO.setTopicNames(topics);
            notificationService.sendNotificationToUsersByAdmin(notificationDTO, null, null);
        }
    }


    @Override
    public String activeInactiveWorkoutPlan(Long workoutPlanId) {
        String activeInactiveMsg = "workout_plan_activate";
        EntityWorkoutPlan entityWorkoutPlan = workoutPlanRepository.findByWorkoutPlanIdAndIsDeleted(workoutPlanId, false);
        if (Objects.isNull(entityWorkoutPlan)) {
            throw new BusinessValidationException("workout_plan_not_found");
        }
        if (entityWorkoutPlan.isActive()) {
            activeInactiveMsg = "workout_plan_inactivate";
        }
        entityWorkoutPlan.setActive(!entityWorkoutPlan.isActive());
        workoutPlanRepository.save(entityWorkoutPlan);
        return activeInactiveMsg;
    }

    @Override
    public void deleteWorkoutPlan(Long workoutPlanId) {
        EntityWorkoutPlan entityWorkoutPlan = workoutPlanRepository.findByWorkoutPlanIdAndIsDeleted(workoutPlanId, false);
        if (Objects.isNull(entityWorkoutPlan)) {
            throw new BusinessValidationException("workout_plan_not_found");
        }
        entityWorkoutPlan.setDeleted(true);
        entityWorkoutPlan.setActive(false);
        workoutPlanRepository.save(entityWorkoutPlan);

        /** Hard delete workout plan from EntityFavoriteWorkoutPlan table. **/
        favoriteWorkoutPlanRepository.deleteByWorkoutPlanId(entityWorkoutPlan.getWorkoutPlanId());
    }

    @Override
    public SearchResultDTO<WorkoutPlanlistDetailResDTO> listWorkoutPlanList(WorkoutPlanListReqDTO workoutPlanListReqDTO) {
        WorkoutPlaylistType workoutPlaylistType = null;
        DifficultyLevel difficultyLevel = null;
        ZodiacSignType zodiacSignType = null;
        Long workoutPlaylistId = null;
        Long userId = null;
        Boolean isActive = null;
        if (workoutPlanListReqDTO.getFilters() != null && StringUtil.nonNullNonEmpty(workoutPlanListReqDTO.getFilters().getWorkoutPlaylistType())) {
            workoutPlaylistType = WorkoutPlaylistType.valueOf(workoutPlanListReqDTO.getFilters().getWorkoutPlaylistType());
        }
        if (workoutPlanListReqDTO.getFilters() != null && workoutPlanListReqDTO.getFilters().getWorkoutPlaylistId() != null) {
            workoutPlaylistId = workoutPlanListReqDTO.getFilters().getWorkoutPlaylistId();
        }
        if (workoutPlanListReqDTO.getFilters() != null && StringUtil.nonNullNonEmpty(workoutPlanListReqDTO.getFilters().getDifficultyLevel())) {
            difficultyLevel = DifficultyLevel.valueOf(workoutPlanListReqDTO.getFilters().getDifficultyLevel());
        }
        if (workoutPlanListReqDTO.getFilters() != null && StringUtil.nonNullNonEmpty(workoutPlanListReqDTO.getFilters().getZodiacSign())) {
            zodiacSignType = ZodiacSignType.valueOf(workoutPlanListReqDTO.getFilters().getZodiacSign());
        }
        if (workoutPlanListReqDTO.getFilters() != null && workoutPlanListReqDTO.getFilters().getUserId() != null) {
            userId = workoutPlanListReqDTO.getFilters().getUserId();
        }
        if (workoutPlanListReqDTO.getFilters() != null && workoutPlanListReqDTO.getFilters().isActive() != null) {
            isActive = workoutPlanListReqDTO.getFilters().isActive();
        }
        Sort sort = null;
        if (workoutPlanListReqDTO.getSortBy() != null && workoutPlanListReqDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(workoutPlanListReqDTO.getSortBy().getDirection(), workoutPlanListReqDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(workoutPlanListReqDTO.getPage().getPageId(), workoutPlanListReqDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());
        List<WorkoutPlanlistDetailResDTO> workoutPlanlistDetailResDTOList = new ArrayList<>(); // Fetch workout plans

        long pageCount = workoutPlanRepository.countWorkoutPlans(workoutPlanListReqDTO.getQueryToSearch(), userId, workoutPlaylistType, workoutPlaylistId, difficultyLevel, zodiacSignType, isActive, false);
        if (pageCount > 0) {
            String workoutVideoPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutVideoFolderName();
            String workoutVideoThumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutVideoThumbnailFolderName();
            workoutPlanlistDetailResDTOList = workoutPlanRepository.findAllWorkoutPlans(workoutPlanListReqDTO.getQueryToSearch(), pageable, workoutPlaylistType, workoutPlaylistId, userId, difficultyLevel, zodiacSignType, workoutVideoPath, workoutVideoThumbnailImagePath, isActive, false);

            // Fetch zodiac signs by audio IDs
            List<Long> audioIds = workoutPlanlistDetailResDTOList.stream().map(WorkoutPlanlistDetailResDTO::getWorkoutPlanId).collect(Collectors.toList());
            List<WorkoutPlanZodiacSignDTO> zodiacSigns = workoutPlanZodiacRepository.findZodiacSignsByAudioIds(audioIds);

            // Map zodiac signs to audio IDs
            Map<Long, List<ZodiacSignType>> zodiacMap = zodiacSigns.stream()
                    .collect(Collectors.groupingBy(
                            WorkoutPlanZodiacSignDTO::getWorkoutPlanId,
                            Collectors.mapping(WorkoutPlanZodiacSignDTO::getZodiacSign, Collectors.toList())
                    ));

            /** Manually add exercise list for each workout plan where workout plan type is MULTI_EXERCISE_PLAN **/
            for (WorkoutPlanlistDetailResDTO workoutPlan : workoutPlanlistDetailResDTOList) {
                // Fetch exercises based on the workout plan ID
                String exerciseVideoPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseVideoFolderName();
                String exerciseThumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseThumbnailFolderName();
                List<ExerciseDetailResDTO> entityWorkoutPlanExerciseList = workoutPlanExerciseRepository.findExerciseDetailsByWorkoutPlanIdAndType(workoutPlan.getWorkoutPlanId(), WorkoutPlanType.MULTI_EXERCISE_PLAN, exerciseVideoPath, exerciseThumbnailImagePath);
                if (!entityWorkoutPlanExerciseList.isEmpty()) {
                    workoutPlan.setExerciseList(entityWorkoutPlanExerciseList);
                }

                List<ZodiacSignType> signs = zodiacMap.get(workoutPlan.getWorkoutPlanId());
                workoutPlan.setZodiacSigns(signs != null ? signs : new ArrayList<>());
            }
        }
        return new SearchResultDTO<>(workoutPlanlistDetailResDTOList, pageCount, workoutPlanListReqDTO.getPage().getLimit());
    }

    @Override
    public WorkoutPlanlistDetailResDTO viewWorkoutPlan(Long workoutPlanId) {
        Long userId = ExecutionContextUtil.getContext().getUserId();
        EntityWorkoutPlan entityWorkoutPlan = workoutPlanRepository.findByWorkoutPlanIdAndIsDeleted(workoutPlanId, false);
        if (Objects.isNull(entityWorkoutPlan)) {
            throw new BusinessValidationException("workout_plan_not_found");
        }

        String workoutVideoPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getWorkoutVideoFolderName();

        String workoutVideoThumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getWorkoutVideoThumbnailFolderName();

        // Fetch workout plan details
        WorkoutPlanlistDetailResDTO workoutPlan = workoutPlanRepository.findWorkoutPlanById(userId,
                workoutPlanId, workoutVideoPath, workoutVideoThumbnailImagePath);


        // Fetch zodiac signs related to this workout plan
        List<WorkoutPlanZodiacSignDTO> zodiacSigns = workoutPlanZodiacRepository.findZodiacSignsByWorkoutPlanId(workoutPlanId);
        List<ZodiacSignType> zodiacSignList = zodiacSigns.stream()
                .map(WorkoutPlanZodiacSignDTO::getZodiacSign)
                .collect(Collectors.toList());

        workoutPlan.setZodiacSigns(zodiacSignList);

        // Fetch exercises if the workout plan is of type MULTI_EXERCISE_PLAN
        if (workoutPlan.getWorkoutPlanType() == WorkoutPlanType.MULTI_EXERCISE_PLAN) {
            String exerciseVideoPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                    globalConfiguration.getAmazonS3().getUploadFolderName() +
                    globalConfiguration.getAmazonS3().getExerciseVideoFolderName();

            String exerciseThumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                    globalConfiguration.getAmazonS3().getUploadFolderName() +
                    globalConfiguration.getAmazonS3().getExerciseThumbnailFolderName();

            List<ExerciseDetailResDTO> exerciseList = workoutPlanExerciseRepository.findExerciseDetailsByWorkoutPlanIdAndType(
                    workoutPlanId, WorkoutPlanType.MULTI_EXERCISE_PLAN, exerciseVideoPath, exerciseThumbnailImagePath);

            workoutPlan.setExerciseList(exerciseList);
        }
        return workoutPlan;
    }

    public boolean validateAddWorkoutPlanDTO(AddWorkoutPlanReqDTO addWorkoutPlanReqDTO) {
        // Define valid video extensions
        String[] validExtensions = {"mp4", "mov", "avi", "mkv", "wmv", "flv", "webm", "3gp", "mpeg", "mpg"};
        String extension = awss3Service.getFileExtension(addWorkoutPlanReqDTO.getWorkoutVideo());
        for (String ext : validExtensions) {
            if (ext.equals(extension)) {
                return true;
            }
        }
        throw new BusinessValidationException("workout_video_file_extension_not_support");
    }
}

