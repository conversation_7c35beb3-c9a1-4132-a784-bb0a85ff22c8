package com.mindbody.api.service.impl;

import com.mindbody.api.dto.SubmitUserAchievementReqDTO;
import com.mindbody.api.enums.AchievementActivityType;
import com.mindbody.api.repository.UserAchievementsRepository;
import com.mindbody.api.service.AchievementEventService;
import com.mindbody.api.service.UserAchievementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Implementation of the AchievementEventService.
 * This service acts as a mediator between UserStatsService and UserAchievementService
 * to break the circular dependency between them.
 */
@Service
@Transactional
public class AchievementEventServiceImpl implements AchievementEventService {

    private static final Logger logger = LoggerFactory.getLogger(AchievementEventServiceImpl.class);

    private final UserAchievementService userAchievementService;
    private final UserAchievementsRepository userAchievementsRepository;

    /**
     * Constructor with @Lazy annotation to break circular dependency.
     * 
     * @param userAchievementService The user achievement service.
     * @param userAchievementsRepository The user achievements repository.
     */
    public AchievementEventServiceImpl(@Lazy UserAchievementService userAchievementService,
                                      UserAchievementsRepository userAchievementsRepository) {
        this.userAchievementService = userAchievementService;
        this.userAchievementsRepository = userAchievementsRepository;
    }

    @Override
    public void processLeaderboardAchievement(Long userId, long rank) {
        // Check if the user is on the leaderboard (top 100)
        if (rank <= 100) {
            // Submit achievement for making it onto the leaderboard
            if (!hasAchievement(AchievementActivityType.Warrior_Empowerment_Leaderboard, userId)) {
                userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(
                        AchievementActivityType.Warrior_Empowerment_Leaderboard, userId));
                logger.info("Submitted achievement Warrior_Empowerment_Leaderboard for user: {}", userId);
            }

            // Check if the user is in the top 10
            if (rank <= 10) {
                // Submit achievement for making it into the top 10
                if (!hasAchievement(AchievementActivityType.Warrior_Empowerment_Leaderboard_top_10, userId)) {
                    userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(
                            AchievementActivityType.Warrior_Empowerment_Leaderboard_top_10, userId));
                    logger.info("Submitted achievement Warrior_Empowerment_Leaderboard_top_10 for user: {}", userId);
                }

                // Check if the user is #1
                if (rank == 1) {
                    // Submit achievement for making it to #1
                    if (!hasAchievement(AchievementActivityType.Warrior_Empowerment_Leaderboard_top_1, userId)) {
                        userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(
                                AchievementActivityType.Warrior_Empowerment_Leaderboard_top_1, userId));
                        logger.info("Submitted achievement Warrior_Empowerment_Leaderboard_top_1 for user: {}", userId);
                    }
                }
            }
        }
    }

    @Override
    public boolean hasAchievement(AchievementActivityType achievementActivityType, Long userId) {
        return userAchievementsRepository.existsByAchievementActivityTypeAndUserIdAndIsDeleted(
                achievementActivityType, userId, false);
    }
}
