package com.mindbody.api.service.impl;

import com.mindbody.api.dto.achievement.AchievementTitleResDTO;
import com.mindbody.api.dto.cms.AchievementTitleListResDTO;
import com.mindbody.api.dto.cms.AddAchievementTitleReqDTO;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.AchievementTitleMapper;
import com.mindbody.api.model.EntityAchievementTitle;
import com.mindbody.api.repository.AchievementTitleRepository;
import com.mindbody.api.service.AchievementTitleService;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class AchievementTitleServiceImpl extends BaseService implements AchievementTitleService {

    private final AchievementTitleRepository achievementTitleRepository;

    private final AchievementTitleMapper achievementTitleMapper;


    public AchievementTitleServiceImpl(MessageService messageService, AchievementTitleRepository achievementTitleRepository, AchievementTitleMapper achievementTitleMapper) {
        super(messageService);
        this.achievementTitleRepository = achievementTitleRepository;
        this.achievementTitleMapper = achievementTitleMapper;
    }

    @Override
    public AchievementTitleResDTO addAchievementTitle(AddAchievementTitleReqDTO addAchievementTitleReqDTO) {
        EntityAchievementTitle entityAchievementTitle = achievementTitleRepository.findByTitleAndIsActiveAndIsDeleted(addAchievementTitleReqDTO.getTitle(), true, false);
        if (!Objects.isNull(entityAchievementTitle)) {
            throw new BusinessValidationException("achievement_title_already_exist");
        }
        EntityAchievementTitle achievementTitle = achievementTitleMapper.toModel(addAchievementTitleReqDTO);
        achievementTitleRepository.save(achievementTitle);
        return achievementTitleMapper.toDTO(achievementTitle);
    }

    @Override
    public List<AchievementTitleListResDTO> listAchievementTitle() {
        return achievementTitleRepository.findAllActiveTitles(true, false);
    }
}
