package com.mindbody.api.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.shopify.*;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.ShopifyCommonService;
import com.mindbody.api.service.ShopifyOrderThirdPartyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class ShopifyOrderThirdPartyServiceImpl extends BaseService implements ShopifyOrderThirdPartyService {

    private static final Logger logger = LoggerFactory.getLogger(ShopifyOrderThirdPartyServiceImpl.class);
    private final GlobalConfiguration globalConfiguration;
    private final RestTemplate restTemplate;
    private final ShopifyCommonService shopifyCommonService;

    public ShopifyOrderThirdPartyServiceImpl(MessageService messageService, GlobalConfiguration globalConfiguration, RestTemplate restTemplate, ShopifyCommonService shopifyCommonService) {
        super(messageService);
        this.globalConfiguration = globalConfiguration;
        this.restTemplate = restTemplate;
        this.shopifyCommonService = shopifyCommonService;
    }

    @Override
    public OrderListResDTO getAllOrderList(OrderListReqDTO orderListReqDTO) {
        try {

            String orderRequestParameters = "?customer_id="
                    + orderListReqDTO.getCustomerId() + "&status=any" + "?limit=" + orderListReqDTO.getLimit();

            HttpHeaders headers = new HttpHeaders();
            headers.set("X-Shopify-Access-Token", globalConfiguration.getShopifyConfig().getShopifyAccessToken());  // Set your access token here
            headers.set("Content-Type", "application/json");      // Set content type to JSON
            headers.set("Accept", "application/json");

            ResponseEntity<String> response = restTemplate.exchange(
                    "https://mind-body-warrior.myshopify.com/admin/api/2024-10/orders.json" + orderRequestParameters,
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    String.class
            );
            System.out.println("order response: " + response.getBody());

            // Initialize response DTO
            OrderListResDTO orderListResDTO = new OrderListResDTO();
            List<OrderListResDTO.OrderDTO> orders = new ArrayList<>();

            // Parse response
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode ordersJson = objectMapper.readTree(response.getBody()).get("orders");

            for (JsonNode orderNode : ordersJson) {
                OrderListResDTO.OrderDTO orderDTO = new OrderListResDTO.OrderDTO();
                orderDTO.setId(orderNode.get("id").asText());
                orderDTO.setAdminGraphqlApiId(orderNode.get("admin_graphql_api_id").asText());
                orderDTO.setOrderNumber(orderNode.get("order_number").asText());

                String totalDiscountsString = orderNode.get("total_discounts").asText();
                if (!totalDiscountsString.isEmpty()) {
                    orderDTO.setTotalDiscounts(Double.parseDouble(totalDiscountsString));
                }

                String totalPriceString = orderNode.get("total_price").asText();
                if (!totalDiscountsString.isEmpty()) {
                    orderDTO.setTotalPrice(Double.parseDouble(totalPriceString));
                }

                orderDTO.setCurrency(orderNode.get("currency").asText());
                orderDTO.setName(orderNode.get("name").asText());
                orderDTO.setCreatedAt(orderNode.get("created_at").asText());
                orderDTO.setUpdatedAt(orderNode.get("updated_at").asText());
                orderDTO.setFinancialStatus(orderNode.get("financial_status").asText());
                orderDTO.setFulfillmentStatus(orderNode.get("fulfillment_status").asText());
                orderDTO.setConfirmed(orderNode.get("confirmed").asBoolean());

                // Process shipping address
                JsonNode shippingAddressNode = orderNode.get("shipping_address");
                if (shippingAddressNode != null) {
                    OrderListResDTO.AddressDTO shippingAddress = new OrderListResDTO.AddressDTO();
                    if (shippingAddressNode.hasNonNull("province")) {
                        shippingAddress.setProvince(shippingAddressNode.get("province").asText());
                    }
                    if (shippingAddressNode.hasNonNull("country")) {
                        shippingAddress.setCountry(shippingAddressNode.get("country").asText());
                    }
                    if (shippingAddressNode.hasNonNull("country_code")) {
                        shippingAddress.setCountryCode(shippingAddressNode.get("country_code").asText());
                    }
                    if (shippingAddressNode.hasNonNull("province_code")) {
                        shippingAddress.setProvinceCode(shippingAddressNode.get("province_code").asText());
                    }
                    orderDTO.setShippingAddress(shippingAddress);
                }

                // Process billing address
                JsonNode billingAddressNode = orderNode.get("billing_address");
                if (billingAddressNode != null) {
                    OrderListResDTO.AddressDTO billingAddress = new OrderListResDTO.AddressDTO();
                    if (billingAddressNode.hasNonNull("province")) {
                        billingAddress.setProvince(billingAddressNode.get("province").asText());
                    }
                    if (billingAddressNode.hasNonNull("country")) {
                        billingAddress.setCountry(billingAddressNode.get("country").asText());
                    }
                    if (billingAddressNode.hasNonNull("country_code")) {
                        billingAddress.setCountryCode(billingAddressNode.get("country_code").asText());
                    }
                    if (billingAddressNode.hasNonNull("province_code")) {
                        billingAddress.setProvinceCode(billingAddressNode.get("province_code").asText());
                    }
                    orderDTO.setBillingAddress(billingAddress);
                }

                // Process line items
                JsonNode lineItemsNode = orderNode.get("line_items");
                if (lineItemsNode != null && lineItemsNode.isArray()) {
                    List<OrderListResDTO.LineItemDTO> lineItems = new ArrayList<>();
                    for (JsonNode lineItemNode : lineItemsNode) {
                        OrderListResDTO.LineItemDTO lineItemDTO = new OrderListResDTO.LineItemDTO();
                        lineItemDTO.setId(lineItemNode.get("id").asText());

                        String productId = lineItemNode.get("product_id") != null ?
                                lineItemNode.get("product_id").asText() : "";
                        lineItemDTO.setProductId(productId);

                        // Fetch product details
                        ResponseEntity<String> productResponse = restTemplate.exchange(
                                "https://mind-body-warrior.myshopify.com/admin/api/2024-10/products.json?ids=" + productId,
                                HttpMethod.GET,
                                new HttpEntity<>(headers),
                                String.class
                        );

                        System.out.println("Product response: " + productResponse.getBody());

                        // Parse product response
                        List<String> images = new ArrayList<>();
                        JsonNode productsJson = objectMapper.readTree(productResponse.getBody()).get("products");
                        if (productsJson != null && productsJson.isArray()) {
                            for (JsonNode productNode : productsJson) {
                                JsonNode imagesNode = productNode.get("images");
                                if (imagesNode != null && imagesNode.isArray()) {
                                    for (JsonNode imageNode : imagesNode) {
                                        images.add(imageNode.get("src").asText());
                                    }
                                }
                            }
                        }

                        lineItemDTO.setImages(images);
                        lineItemDTO.setTitle(lineItemNode.get("title").asText());
                        lineItemDTO.setName(lineItemNode.get("name").asText());
                        lineItemDTO.setPrice(lineItemNode.get("price").asDouble());
                        lineItemDTO.setTotalDiscount(lineItemNode.get("total_discount").asDouble());
                        lineItemDTO.setQuantity(lineItemNode.get("quantity").asInt());
                        lineItemDTO.setTaxable(lineItemNode.get("taxable").asBoolean());
                        lineItemDTO.setVariantId(lineItemNode.get("variant_id").asText());
                        lineItemDTO.setVendor(lineItemNode.get("vendor").asText());

                        lineItems.add(lineItemDTO);
                    }
                    orderDTO.setLineItems(lineItems);
                }

                // Process Shipping lines
                JsonNode shippingLineItemsNode = orderNode.get("shipping_lines");
                if (shippingLineItemsNode != null && shippingLineItemsNode.isArray()) {
                    List<OrderListResDTO.ShippingLinesDTO> shippingLineItems = new ArrayList<>();
                    for (JsonNode shippingItemNode : shippingLineItemsNode) {
                        OrderListResDTO.ShippingLinesDTO shippingLineItem = new OrderListResDTO.ShippingLinesDTO();

                        // Set the price
                        shippingLineItem.setPrice(shippingItemNode.get("price").asDouble());
                        // Extract and set price_set details
                        JsonNode priceSetNode = shippingItemNode.get("price_set");
                        if (priceSetNode != null) {
                            JsonNode shopMoneyNode = priceSetNode.get("shop_money");
                            JsonNode presentmentMoneyNode = priceSetNode.get("presentment_money");

                            if (shopMoneyNode != null) {
                                shippingLineItem.setShopMoneyAmount(shopMoneyNode.get("amount").asDouble());
                                shippingLineItem.setShopMoneyCurrency(shopMoneyNode.get("currency_code").asText());
                            }

                            if (presentmentMoneyNode != null) {
                                shippingLineItem.setPresentmentMoneyAmount(presentmentMoneyNode.get("amount").asDouble());
                                shippingLineItem.setPresentmentMoneyCurrency(presentmentMoneyNode.get("currency_code").asText());
                            }
                        }

                        shippingLineItems.add(shippingLineItem);

                    }
                    orderDTO.setShippingLines(shippingLineItems);
                }
                orders.add(orderDTO);
            }
            orderListResDTO.setOrders(orders);
            return orderListResDTO;
        } catch (Exception e) {
            logger.info("Unexpected error occurred while getting all orders: {}", e.getMessage());
            throw new BusinessValidationException("order_details_failed", e.getMessage());
        }
    }

    @Override
    public OrdersListGraphqlResDTO getAllOrdersListShopifyAdminGraphql(OrdersListGraphqlReqDTO ordersListGraphqlReqDTO) {
        try {
            // Construct the query with proper escaping
            String requestBody = "{"
                    + "\"query\": \"" + getOrdersByCustomerGraphQl() + "\","
                    + "\"variables\": {"
                    + "\"query\": \"customer_id:" + ordersListGraphqlReqDTO.getCustomerId() + "\","
                    + "\"first\": " + ordersListGraphqlReqDTO.getFirst() + ","
                    + "\"after\": " + (ordersListGraphqlReqDTO.getAfter() != null ? "\"" + ordersListGraphqlReqDTO.getAfter() + "\"" : null) + ","
                    + "\"sortKey\": " + (isValidSortKey(ordersListGraphqlReqDTO.getSortKey()) ? "\"" + ordersListGraphqlReqDTO.getSortKey() + "\"" : null) + ","
                    + "\"reverse\": " + (ordersListGraphqlReqDTO.getReverse() != null ? ordersListGraphqlReqDTO.getReverse() : null)
                    + "}"
                    + "}";

            // Close the variables and request body
            logger.info("Request Body: {}", requestBody);
            return shopifyCommonService.sendGraphQlRequestToShopifyAdmin(requestBody, OrdersListGraphqlResDTO.class);
        } catch (Exception e) {
            logger.info("Unexpected error occurred while getting all orders: {}", e.getMessage());
            throw new BusinessValidationException("order_details_failed", e.getMessage());
        }
    }

    public boolean isValidSortKey(String sortKey) {
        return sortKey != null && (
                sortKey.equals("TITLE") ||
                        sortKey.equals("ID") ||
                        sortKey.equals("BEST_SELLING") ||
                        sortKey.equals("CREATED") ||
                        sortKey.equals("UPDATED") ||
                        sortKey.equals("PRICE") ||
                        sortKey.equals("PRODUCT_TYPE") ||
                        sortKey.equals("VENDOR") ||
                        sortKey.equals("CREATED_AT") ||
                        sortKey.equals("UPDATED_AT") ||
                        sortKey.equals("FINANCIAL_STATUS") ||
                        sortKey.equals("FULFILLMENT_STATUS") ||
                        sortKey.equals("TOTAL_PRICE") ||
                        sortKey.equals("ORDER_NUMBER") ||
                        sortKey.equals("PROCESSED_AT")
        );
    }

    @Override
    public TrackOrderResDTO trackOrder(String id) {
        try {
            TrackOrderResDTO trackOrderResDTO = new TrackOrderResDTO();
            HttpHeaders headers = new HttpHeaders();
            headers.set("X-Shopify-Access-Token", globalConfiguration.getShopifyConfig().getShopifyAccessToken());  // Set your access token here
            headers.set("Content-Type", "application/json");      // Set content type to JSON
            headers.set("Accept", "application/json");

            ResponseEntity<String> response = restTemplate.exchange(
                    "https://mind-body-warrior.myshopify.com/admin/api/2024-10/orders/" + id + "/fulfillments.json",
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    String.class
            );
            System.out.println("Track order response: " + response.getBody());

            // Parse the JSON response
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(response.getBody());
            List<TrackOrderResDTO.Fulfillment> fulfillmentList = new ArrayList<>();

            for (JsonNode fulfillmentNode : rootNode.get("fulfillments")) {
                TrackOrderResDTO.Fulfillment fulfillment = new TrackOrderResDTO.Fulfillment();

                fulfillment.setId(fulfillmentNode.get("id").asLong());
                fulfillment.setOrderId(fulfillmentNode.get("order_id").asLong());
                fulfillment.setStatus(fulfillmentNode.get("status").asText());
                fulfillment.setCreatedAt(fulfillmentNode.get("created_at").asText());
                fulfillment.setService(fulfillmentNode.get("service").asText());
                fulfillment.setUpdatedAt(fulfillmentNode.get("updated_at").asText());
                fulfillment.setTrackingCompany(fulfillmentNode.get("tracking_company").asText());
                fulfillment.setTrackingNumber(fulfillmentNode.get("tracking_number").asText());
                // Set tracking numbers
                List<String> trackingNumbers = new ArrayList<>();
                for (JsonNode urlNode : fulfillmentNode.get("tracking_numbers")) {
                    trackingNumbers.add(urlNode.asText());
                }
                fulfillment.setTrackingNumbers(trackingNumbers);

                // Set line items
                List<TrackOrderResDTO.LineItem> lineItems = new ArrayList<>();
                for (JsonNode itemNode : fulfillmentNode.get("line_items")) {
                    TrackOrderResDTO.LineItem lineItem = new TrackOrderResDTO.LineItem();
                    lineItem.setId(itemNode.get("id").asLong());
                    lineItem.setTitle(itemNode.get("title").asText());
                    lineItem.setName(itemNode.get("name").asText());
                    lineItem.setQuantity(itemNode.get("quantity").asInt());
                    lineItem.setPrice(itemNode.get("price").asText());
                    lineItem.setTotalDiscount(itemNode.get("total_discount").asText());
                    lineItem.setFulfillmentStatus(itemNode.get("fulfillment_status").asText());
                    lineItem.setProductId(itemNode.get("product_id").asLong());
                    lineItem.setProductExists(itemNode.get("product_exists").asBoolean());
                    lineItem.setTaxable(itemNode.get("taxable").asBoolean());
                    lineItem.setGiftCard(itemNode.get("gift_card").asBoolean());
                    lineItems.add(lineItem);
                }
                fulfillment.setLineItems(lineItems);

                // Set tracking URLs
                List<String> trackingUrls = new ArrayList<>();
                for (JsonNode urlNode : fulfillmentNode.get("tracking_urls")) {
                    trackingUrls.add(urlNode.asText());
                }
                fulfillment.setTrackingUrls(trackingUrls);
                fulfillment.setTrackingUrl(fulfillmentNode.get("tracking_url").asText());
                fulfillmentList.add(fulfillment);
            }

            trackOrderResDTO.setFulfillments(fulfillmentList);
            return trackOrderResDTO;

        } catch (Exception e) {
            logger.info("Unexpected error occurred while tracking order: {}", e.getMessage());
            throw new BusinessValidationException("order_details_failed_for_tracking", e.getMessage());
        }
    }

    @Override
    public int getNumberOfProductsPurchasedByCustomer(TotalNumberOfProductsReqDTO totalNumberOfProductsReqDTO) {
        try {
            String requestBody = "{"
                    + "\"query\": \"" + getTotalProductsPurchasedGraphQl() + "\","
                    + "\"variables\": {"
                    + "  \"customerId\": \"gid://shopify/Customer/" + totalNumberOfProductsReqDTO.getCustomerId() + "\","
                    + "  \"first\": " + totalNumberOfProductsReqDTO.getFirst()
                    + "}"
                    + "}";

            // Log the request body for debugging
            logger.info("Request Body: {}", requestBody);

            // Send request and get response as Map
            Map<String, Object> response = shopifyCommonService.sendGraphQlRequestToShopifyAdmin(requestBody, Map.class);

            if (response != null) {
                // Extract order edges from response
                Map<String, Object> data = (Map<String, Object>) response.get("data");
                if (data != null) {
                    Map<String, Object> customer = (Map<String, Object>) data.get("customer");
                    if (customer != null) {
                        Map<String, Object> orders = (Map<String, Object>) customer.get("orders");
                        if (orders != null) {
                            List<Map<String, Object>> orderEdges = (List<Map<String, Object>>) orders.get("edges");

                            // Compute total quantity using Java Streams
                            int totalProductsPurchased = orderEdges.stream()
                                    .map(edge -> (Map<String, Object>) edge.get("node")) // Get order node
                                    .map(node -> (Map<String, Object>) node.get("lineItems")) // Get lineItems
                                    .map(lineItems -> (List<Map<String, Object>>) lineItems.get("edges")) // Get lineItems edges
                                    .flatMap(List::stream) // Flatten all line item edges
                                    .map(lineItemEdge -> (Map<String, Object>) lineItemEdge.get("node")) // Get lineItem node
                                    .mapToInt(lineItemNode -> (int) lineItemNode.get("quantity")) // Extract quantity
                                    .sum();

                            logger.info("Total products purchased: {}", totalProductsPurchased);
                            return totalProductsPurchased;
                        }
                    }
                }
            }

            logger.info("No orders found for customer ID: {}", totalNumberOfProductsReqDTO.getCustomerId());
            return 0; // Return 0 if no purchases exist
        } catch (Exception e) {
            logger.info("Unexpected error occurred while getting total products purchased: {}", e.getMessage());
            throw new BusinessValidationException("product_count_failed", e.getMessage());
        }
    }

    public String getOrdersByCustomerGraphQl() {
        return "query ordersQuery($query: String, $first: Int, $after: String, $sortKey: OrderSortKeys, $reverse: Boolean) {"
                + "  orders(query: $query, first: $first, after: $after, sortKey: $sortKey, reverse: $reverse) {"
                + "    edges {"
                + "      node {"
                + "        id"
                + "        name"
                + "        displayFinancialStatus"
                + "        displayFulfillmentStatus"
                + "        confirmed"
                + "        closed"
                + "        closedAt"
                + "        createdAt"
                + "        updatedAt"
                + "        fulfillments(first: 10) {"
                + "          createdAt"
                + "          deliveredAt"
                + "          displayStatus"
                + "          estimatedDeliveryAt"
                + "        }"
                + "        lineItems(first: 10) {"
                + "          edges {"
                + "            node {"
                + "              id"
                + "              name"
                + "              title"
                + "              quantity"
                + "              vendor"
                + "              variant {"
                + "                id"
                + "                title"
                + "                sku"
                + "                price"
                + "              }"
                + "              image {"
                + "                id"
                + "                url"
                + "                altText"
                + "                width"
                + "                height"
                + "              }"
                + "              product {"
                + "                description"
                + "                productType"
                + "              }"
                + "            }"
                + "          }"
                + "          pageInfo {"
                + "            hasNextPage"
                + "            endCursor"
                + "          }"
                + "        }"
                + "        shippingLine {"
                + "          carrierIdentifier"
                + "          code"
                + "          currentDiscountedPriceSet {"
                + "            presentmentMoney {"
                + "              amount"
                + "              currencyCode"
                + "            }"
                + "            shopMoney {"
                + "              amount"
                + "              currencyCode"
                + "            }"
                + "          }"
                + "        }"
                + "        totalPriceSet {"
                + "          presentmentMoney {"
                + "            amount"
                + "            currencyCode"
                + "          }"
                + "          shopMoney {"
                + "            amount"
                + "            currencyCode"
                + "          }"
                + "        }"
                + "      }"
                + "    }"
                + "    pageInfo {"
                + "      hasNextPage"
                + "      endCursor"
                + "    }"
                + "  }"
                + "}";
    }

    public String getCustomerOrderCountGraphQl() {
        return "query getCustomerOrderCount($customerId: ID!) {"
                + "  customer(id: $customerId) {"
                + "    numberOfOrders"
                + "  }"
                + "}";
    }

    public String getTotalProductsPurchasedGraphQl() {
        return "query getTotalProductsPurchased($customerId: ID!, $first: Int) {"
                + "  customer(id: $customerId) {"
                + "    orders(first: $first) {"
                + "      edges {"
                + "        node {"
                + "          lineItems(first: 250) {"
                + "            edges {"
                + "              node {"
                + "                quantity"
                + "              }"
                + "            }"
                + "          }"
                + "        }"
                + "      }"
                + "    }"
                + "  }"
                + "}";
    }
}
