package com.mindbody.api.service.impl;

import com.mindbody.api.dto.cms.AchievementRewardsResDTO;
import com.mindbody.api.dto.cms.EditAchievementRewardReqDTO;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.EntityAchievementRewards;
import com.mindbody.api.repository.AchievementRewardsRepository;
import com.mindbody.api.service.AchievementRewardsService;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class AchievementRewardsServiceImpl extends BaseService implements AchievementRewardsService {

    private final AchievementRewardsRepository achievementRewardsRepository;

    public AchievementRewardsServiceImpl(MessageService messageService,AchievementRewardsRepository achievementRewardsRepository) {
        super(messageService);
        this.achievementRewardsRepository = achievementRewardsRepository;
    }

    @Override
    public List<AchievementRewardsResDTO> viewAchievementRewards() {
        return achievementRewardsRepository.findAllAchievementRewards();
    }

    @Override
    public void editAchievementRewards(EditAchievementRewardReqDTO editAchievementRewardReqDTO) {
        EntityAchievementRewards entityAchievementRewards = achievementRewardsRepository.findByAchievementRewardsId(editAchievementRewardReqDTO.getAchievementRewardsId());
        if (Objects.isNull(entityAchievementRewards)) {
            throw new BusinessValidationException("achievement_reward_not_found");
        }
        entityAchievementRewards.setWxp(editAchievementRewardReqDTO.getWxp());
        entityAchievementRewards.setPoints(editAchievementRewardReqDTO.getPoints());
        achievementRewardsRepository.save(entityAchievementRewards);
    }


}
