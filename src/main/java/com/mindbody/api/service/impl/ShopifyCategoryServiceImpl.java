package com.mindbody.api.service.impl;

import com.mindbody.api.dto.shopify.CategoryListResDTO;
import com.mindbody.api.dto.shopify.getCategoryListReqDTO;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.ShopifyCategoryService;
import com.mindbody.api.service.ShopifyCategoryThirdPartyService;
import org.springframework.stereotype.Service;

@Service
public class ShopifyCategoryServiceImpl extends BaseService implements ShopifyCategoryService {

    private final ShopifyCategoryThirdPartyService shopifyCategoryThirdPartyService;

    public ShopifyCategoryServiceImpl(MessageService messageService, ShopifyCategoryThirdPartyService shopifyCategoryThirdPartyService) {
        super(messageService);
        this.shopifyCategoryThirdPartyService = shopifyCategoryThirdPartyService;
    }

    @Override
    public CategoryListResDTO getCategoryList(getCategoryListReqDTO getCategoryListReqDTO) {
        return shopifyCategoryThirdPartyService.getCategoryList(getCategoryListReqDTO);
    }
}
