package com.mindbody.api.service.impl;

import com.mindbody.api.dto.FavoriteWorkoutPlanReqDTO;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.*;
import com.mindbody.api.repository.FavoriteWorkoutPlanRepository;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.repository.WorkoutPlanRepository;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.FavoriteWorkoutPlanService;
import com.mindbody.api.service.MessageService;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class FavoriteWorkoutPlanServiceImpl extends BaseService implements FavoriteWorkoutPlanService {

    private final UserRepository userRepository;

    private final WorkoutPlanRepository workoutPlanRepository;

    private final FavoriteWorkoutPlanRepository favoriteWorkoutPlanRepository;

    public FavoriteWorkoutPlanServiceImpl(MessageService messageService, UserRepository userRepository, WorkoutPlanRepository workoutPlanRepository, FavoriteWorkoutPlanRepository favoriteWorkoutPlanRepository) {
        super(messageService);
        this.userRepository = userRepository;
        this.workoutPlanRepository = workoutPlanRepository;
        this.favoriteWorkoutPlanRepository = favoriteWorkoutPlanRepository;
    }

    @Override
    public boolean favoriteWorkoutPlan(FavoriteWorkoutPlanReqDTO favoriteWorkoutPlanReqDTO) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(favoriteWorkoutPlanReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        EntityWorkoutPlan entityWorkoutPlan = workoutPlanRepository.findByWorkoutPlanIdAndIsActiveAndIsDeleted(favoriteWorkoutPlanReqDTO.getWorkoutPlanId(), true, false);
        if (Objects.isNull(entityWorkoutPlan)) {
            throw new BusinessValidationException("workout_plan_not_found");
        }
        boolean isFavorite = false;
        EntityFavoriteWorkoutPlan favoriteWorkoutPlan = favoriteWorkoutPlanRepository.findByUserIdAndWorkoutPlanIdAndIsActiveAndIsDeleted(favoriteWorkoutPlanReqDTO.getUserId(), favoriteWorkoutPlanReqDTO.getWorkoutPlanId(), true, false);

        if (Objects.nonNull(favoriteWorkoutPlan)) {
            if (favoriteWorkoutPlan.isFavorite()) {
                isFavorite = true;
            }
        } else {
            favoriteWorkoutPlan = new EntityFavoriteWorkoutPlan();
        }
        favoriteWorkoutPlan.setEntityUser(entityUser);
        favoriteWorkoutPlan.setUserId(entityUser.getUserId());
        favoriteWorkoutPlan.setEntityWorkoutPlan(entityWorkoutPlan);
        favoriteWorkoutPlan.setWorkoutPlanId(entityWorkoutPlan.getWorkoutPlanId());
        favoriteWorkoutPlan.setFavorite(!favoriteWorkoutPlan.isFavorite());
        favoriteWorkoutPlanRepository.save(favoriteWorkoutPlan);
        return isFavorite;
    }
}
