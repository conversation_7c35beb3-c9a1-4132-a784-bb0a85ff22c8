package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.UploadedFileNameResponseDTO;
import com.mindbody.api.dto.cms.*;
import com.mindbody.api.enums.ImageType;
import com.mindbody.api.enums.RegisterType;
import com.mindbody.api.enums.RoleType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.EmailVerificationToken;
import com.mindbody.api.model.EntityAccessToken;
import com.mindbody.api.model.EntityAdmin;
import com.mindbody.api.repository.AccessTokenRepository;
import com.mindbody.api.repository.AdminRepository;
import com.mindbody.api.repository.EmailVerificationTokenRepository;
import com.mindbody.api.security.CustomUserDetailsService;
import com.mindbody.api.security.ExecutionContextUtil;
import com.mindbody.api.security.JwtTokenUtil;
import com.mindbody.api.service.AWSS3Service;
import com.mindbody.api.service.AdminService;
import com.mindbody.api.service.EmailService;
import com.mindbody.api.util.EncryptUtil;
import com.mindbody.api.util.FieldConstant;
import com.mindbody.api.util.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.*;

@Service
public class AdminServiceImpl implements AdminService {

    @Value("${reset.passwordUrl}")
    String resetPasswordUrl;

    private final AdminRepository adminRepository;

    private final PasswordEncoder passwordEncoder;

    private final JwtTokenUtil jwtTokenUtil;

    private final AccessTokenRepository accessTokenRepository;

    private final EmailVerificationTokenGenerator emailVerificationTokenGenerator;

    private final EmailService emailService;

    private final EmailVerificationTokenRepository emailVerificationTokenRepository;

    private final AWSS3Service awss3Service;

    private final GlobalConfiguration globalConfiguration;

    private final CustomUserDetailsService customUserDetailsService;

    public AdminServiceImpl(AdminRepository adminRepository, PasswordEncoder passwordEncoder, JwtTokenUtil jwtTokenUtil, AccessTokenRepository accessTokenRepository, EmailVerificationTokenGenerator emailVerificationTokenGenerator, EmailService emailService, EmailVerificationTokenRepository emailVerificationTokenRepository, AWSS3Service awss3Service, GlobalConfiguration globalConfiguration, CustomUserDetailsService customUserDetailsService) {
        this.adminRepository = adminRepository;
        this.passwordEncoder = passwordEncoder;
        this.jwtTokenUtil = jwtTokenUtil;
        this.accessTokenRepository = accessTokenRepository;
        this.emailVerificationTokenGenerator = emailVerificationTokenGenerator;
        this.emailService = emailService;
        this.emailVerificationTokenRepository = emailVerificationTokenRepository;
        this.awss3Service = awss3Service;
        this.globalConfiguration = globalConfiguration;
        this.customUserDetailsService = customUserDetailsService;
    }

    public EntityAdmin findUserAdminByUserId(Long userId) {
        Optional<EntityAdmin> admin = adminRepository.findByAdminIdAndIsActiveAndIsDeleted(userId,true,false);
        if (admin.isEmpty()) {
            throw new BusinessValidationException("admin_not_found_error");
        }
        return admin.get();
    }

    @Override
    public AdminAccessTokenDTO adminLogin(AdminLoginReqDTO adminLoginReqDTO) {
        EntityAdmin entityAdmin = adminRepository.findAdminByEmail(adminLoginReqDTO.getEmail(), true, false);
        if (Objects.isNull(entityAdmin)) {
            throw new BusinessValidationException("admin_not_found_error");
        }
        //Check Pwd
        if (!passwordEncoder.matches(adminLoginReqDTO.getPassword(), entityAdmin.getPassword())) {
            throw new BusinessValidationException("error_invalid_password");
        }

        /** Generate the jwt token based on the email */
        String accessToken = jwtTokenUtil.generateJwtForUser(adminLoginReqDTO.getEmail(),null,null, RoleType.ADMIN, RegisterType.NORMAL.name());
        EntityAccessToken entityAccessToken = new EntityAccessToken(entityAdmin.getAdminId(),RoleType.ADMIN,EncryptUtil.encryptKey(accessToken),null);
        accessTokenRepository.save(entityAccessToken);

        AdminAccessTokenDTO adminAccessTokenDTO = new AdminAccessTokenDTO();
        adminAccessTokenDTO.setAdminId(entityAdmin.getAdminId());
        adminAccessTokenDTO.setAccessToken(accessToken);
        adminAccessTokenDTO.setFirstName(entityAdmin.getFirstName());
        adminAccessTokenDTO.setLastName(entityAdmin.getLastName());
        adminAccessTokenDTO.setEmail(entityAdmin.getEmail());
        adminAccessTokenDTO.setActive(entityAdmin.isActive());
        if (StringUtil.nonNullNonEmpty(entityAdmin.getProfileImage())) {
            adminAccessTokenDTO.setProfileImage(globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAdminFolderName() + entityAdmin.getProfileImage());
        }
        return adminAccessTokenDTO;
    }

    @Override
    public void editAdminProfile(MultipartFile profileImage,EditAdminProfileReqDTO editAdminProfileReqDTO) {
        Long userId= ExecutionContextUtil.getContext().getUserId();
        if(userId==null) {
            throw new BusinessValidationException("user_id_not_found");
        }
            EntityAdmin entityAdmin = findUserAdminByUserId(userId);
            String newImageName = "";
            if (profileImage != null && !profileImage.isEmpty()) {
                if (entityAdmin.getProfileImage() != null) {
                    awss3Service.deleteFile(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAdminFolderName() + entityAdmin.getProfileImage(), ImageType.profileImage);
                }
                UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(profileImage, globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAdminFolderName(), ImageType.profileImage);
                newImageName = uploadedFileName.getImageName();
            }
            entityAdmin.setFirstName(editAdminProfileReqDTO.getFirstName());
            entityAdmin.setLastName(editAdminProfileReqDTO.getLastName());
            if (StringUtil.nonNullNonEmpty(newImageName)) {
                entityAdmin.setProfileImage(newImageName);
            }
            adminRepository.save(entityAdmin);
    }

    @Override
    @Transactional
    public void adminLogout(String accessToken) {
        if (StringUtil.nullOrEmpty(accessToken))
            throw new BusinessValidationException("access_token_not_found_error");
        Long userId = ExecutionContextUtil.getContext().getUserId();
        findUserAdminByUserId(userId);
        accessTokenRepository.deleteByAccessTokenAndUserId(EncryptUtil.encryptKey(accessToken),userId);
    }

    @Override
    public void changePassword(ChangePasswordReqDTO changePasswordReqDTO) {
        Long userId= ExecutionContextUtil.getContext().getUserId();
        if(userId==null) {
            throw new BusinessValidationException("user_id_not_found");
        }
            EntityAdmin admin = findUserAdminByUserId(userId);
            if (passwordEncoder.matches(changePasswordReqDTO.getOldPassword(), admin.getPassword())) {
                if (changePasswordReqDTO.getNewPassword().equals(changePasswordReqDTO.getConfirmPassword())) {
                    Optional<EntityAdmin> adminDetail = adminRepository.findByAdminIdAndIsActiveAndIsDeleted(admin.getAdminId(), true, false);
                    if (adminDetail.isPresent()) {
                        adminDetail.get().setPassword(passwordEncoder.encode(changePasswordReqDTO.getNewPassword()));
                        adminRepository.save(adminDetail.get());
                    } else {
                        throw new BusinessValidationException("admin_not_found_error");
                    }
                } else {
                    throw new BusinessValidationException("incorrect_confirm_password");
                }
            } else {
                throw new BusinessValidationException("incorrect_old_password");
            }
    }

    @Override
    public void sendForgotPasswordLink(ForgotPasswordReqDTO forgotPasswordReqDTO) {
        EntityAdmin entityAdmin = adminRepository.findAdminByEmail(forgotPasswordReqDTO.getEmail(), true, false);
        if (Objects.isNull(entityAdmin)) {
            throw new BusinessValidationException("admin_not_found_error");
        }

        //Generate Verification token
        String verificationToken = emailVerificationTokenGenerator.getVerificationToken(entityAdmin);

        String url = resetPasswordUrl + "/" +verificationToken;

        Map<String, Object> emailMap = new HashMap<>();
        emailMap.put("url", url);
        emailMap.put("year", Integer.valueOf(LocalDate.now().getYear()).toString());

        emailService.sendEmail(FieldConstant.EmailTemplateName.TEMPLATE_FORGOT_PASSWORD_LINK,
                "MindBodyWarrior - Reset Password",
                forgotPasswordReqDTO.getEmail(),
                emailMap);

    }

    @Override
    public void resetPassword(ResetPasswordReqDTO resetPasswordReqDTO) {
        Optional<EmailVerificationToken> emailVerification = emailVerificationTokenRepository.findByVerificationToken(resetPasswordReqDTO.getVerificationToken());
        if (emailVerification.isEmpty()) {
            throw new BusinessValidationException("error_reset_password_link_expired");
        } else {
            EntityAdmin admin = adminRepository.findById(emailVerification.get().getAdminId())
                    .orElseThrow(() -> new BusinessValidationException("admin_not_found_error"));
            if (!emailVerification.get().getExpirationTime().after(new Date())) {
                throw new BusinessValidationException("error_verification_link_expired");
            }
            if (passwordEncoder.matches(resetPasswordReqDTO.getNewPassword(), admin.getPassword())) {
                throw new BusinessValidationException("error_new_password_same_as_old_password");
            }
            admin.setPassword(passwordEncoder.encode(resetPasswordReqDTO.getNewPassword()));
            adminRepository.save(admin);
        }
    }

    @Override
    public ViewAdminProfileResDTO viewAdminProfile() {
        Long userId= ExecutionContextUtil.getContext().getUserId();
        if(userId==null) {
            throw new BusinessValidationException("user_id_not_found");
        }
        ViewAdminProfileResDTO viewAdminProfileResDTO = new ViewAdminProfileResDTO();
        EntityAdmin admin = findUserAdminByUserId(userId);
        viewAdminProfileResDTO.setAdminId(admin.getAdminId());
        viewAdminProfileResDTO.setFirstName(admin.getFirstName());
        viewAdminProfileResDTO.setLastName(admin.getLastName());
        viewAdminProfileResDTO.setEmail(admin.getEmail());
        if (StringUtil.nonNullNonEmpty(admin.getProfileImage())) {
            viewAdminProfileResDTO.setProfileImage(globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAdminFolderName() + admin.getProfileImage());
        }
        return viewAdminProfileResDTO;
}

}
