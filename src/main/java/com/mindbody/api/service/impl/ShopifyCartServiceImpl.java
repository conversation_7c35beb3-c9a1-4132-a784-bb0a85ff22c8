package com.mindbody.api.service.impl;

import com.mindbody.api.dto.shopify.*;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.model.EntityUserCart;
import com.mindbody.api.repository.UserCartRepository;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.ShopifyCartService;
import com.mindbody.api.service.ShopifyCartThirdPartyService;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class ShopifyCartServiceImpl extends BaseService implements ShopifyCartService {

    private final ShopifyCartThirdPartyService shopifyCartThirdPartyService;

    private final UserCartRepository userCartRepository;

    private final UserRepository userRepository;

    public ShopifyCartServiceImpl(MessageService messageService, ShopifyCartThirdPartyService shopifyCartThirdPartyService, UserCartRepository userCartRepository, UserRepository userRepository) {
        super(messageService);
        this.shopifyCartThirdPartyService = shopifyCartThirdPartyService;
        this.userCartRepository = userCartRepository;
        this.userRepository = userRepository;
    }

    @Override
    public CreateCartWithFirstProductResDTO createCartWithFirstProduct(CreateCartWithFirstProductReqDTO createCartWithFirstProductReqDTO) {

        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(createCartWithFirstProductReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        CreateCartWithFirstProductResDTO cartWithFirstProduct = shopifyCartThirdPartyService.createCartWithFirstProduct(createCartWithFirstProductReqDTO);
        if (Objects.nonNull(cartWithFirstProduct.getData()) &&
                Objects.nonNull(cartWithFirstProduct.getData().getCartCreate()) &&
                Objects.nonNull(cartWithFirstProduct.getData().getCartCreate().getCart()) &&
                Objects.nonNull(cartWithFirstProduct.getData().getCartCreate().getCart().getId())) {
            String cartId = cartWithFirstProduct.getData().getCartCreate().getCart().getId();

            EntityUserCart entityUserCart = userCartRepository.findByUserIdAndIsActiveAndIsDeleted(createCartWithFirstProductReqDTO.getUserId(), true, false);
            if (Objects.isNull(entityUserCart)) {
                entityUserCart = new EntityUserCart();
            }
            entityUserCart.setEntityUser(entityUser);
            entityUserCart.setUserId(entityUser.getUserId());
            entityUserCart.setCartId(cartId);
            userCartRepository.save(entityUserCart);
        }
        return cartWithFirstProduct;
    }

    @Override
    public AddProductToCartResDTO addProductToCart(AddProductToCartReqDTO addProductToCartReqDTO) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(addProductToCartReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        EntityUserCart entityUserCart = userCartRepository.findByUserIdAndIsActiveAndIsDeleted(entityUser.getUserId(), true, false);
        if (Objects.isNull(entityUserCart)) {
            entityUserCart = new EntityUserCart();
        }
        entityUserCart.setEntityUser(entityUser);
        entityUserCart.setUserId(entityUser.getUserId());

        AddProductToCartResDTO addProductToCartResDTO = shopifyCartThirdPartyService.addProductToCart(addProductToCartReqDTO);
        if (addProductToCartResDTO != null && addProductToCartResDTO.getData() != null && addProductToCartResDTO.getData().getCartLinesAdd().getCart() != null && addProductToCartResDTO.getData().getCartLinesAdd().getCart().getId() != null) {
            entityUserCart.setCartId(addProductToCartResDTO.getData().getCartLinesAdd().getCart().getId());
        }
        userCartRepository.save(entityUserCart);
        return addProductToCartResDTO;
        //        return shopifyCartThirdPartyService.addProductToCart(addProductToCartReqDTO);
    }

    @Override
    public UpdateCartResDTO updateCart(UpdateCartReqDTO updateCartReqDTO) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(updateCartReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        EntityUserCart entityUserCart = userCartRepository.findByUserIdAndCartIdAndIsActiveAndIsDeleted(updateCartReqDTO.getUserId(), updateCartReqDTO.getCartId(), true, false);
        if (Objects.isNull(entityUserCart)) {
            throw new BusinessValidationException("user_cart_found_error");
        }
        return shopifyCartThirdPartyService.updateCart(updateCartReqDTO);
    }

    @Override
    public ViewCartResDTO viewCart(ViewCartReqDTO viewCartReqDTO) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(viewCartReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        EntityUserCart entityUserCart = userCartRepository.findByUserIdAndCartIdAndIsActiveAndIsDeleted(viewCartReqDTO.getUserId(), viewCartReqDTO.getCartId(), true, false);
        if (Objects.isNull(entityUserCart)) {
            throw new BusinessValidationException("user_cart_found_error");
        }
        return shopifyCartThirdPartyService.viewCart(viewCartReqDTO);
    }


    @Override
    public RemoveProductFromCartResDTO removeProductFromCart(RemoveProductFromCartReqDTO removeProductFromCartReqDTO) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(removeProductFromCartReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        EntityUserCart entityUserCart = userCartRepository.findByUserIdAndCartIdAndIsActiveAndIsDeleted(removeProductFromCartReqDTO.getUserId(), removeProductFromCartReqDTO.getCartId(), true, false);
        if (Objects.isNull(entityUserCart)) {
            throw new BusinessValidationException("user_cart_found_error");
        }
        return shopifyCartThirdPartyService.removeProductFromCart(removeProductFromCartReqDTO);
    }
}
