package com.mindbody.api.service.impl;

import com.mindbody.api.dto.cms.AchievementBorderListResDTO;
import com.mindbody.api.repository.AchievementBorderRepository;
import com.mindbody.api.service.AchievementBorderService;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AchievementBorderServiceImpl extends BaseService implements AchievementBorderService {

    private final AchievementBorderRepository achievementBorderRepository;


    public AchievementBorderServiceImpl(MessageService messageService, AchievementBorderRepository achievementBorderRepository) {
        super(messageService);
        this.achievementBorderRepository = achievementBorderRepository;
    }


    @Override
    public List<AchievementBorderListResDTO> listAchievementBorder() {
        return achievementBorderRepository.findAllActiveBorders(true, false);
    }
}
