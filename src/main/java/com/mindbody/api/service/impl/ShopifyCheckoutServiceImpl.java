package com.mindbody.api.service.impl;

import com.mindbody.api.dto.shopify.UpdateBuyerIdentityReqDTO;
import com.mindbody.api.dto.shopify.UpdateBuyerIdentityResDTO;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.ShopifyCheckoutService;
import org.springframework.stereotype.Service;

@Service
public class ShopifyCheckoutServiceImpl extends BaseService implements ShopifyCheckoutService {
    public ShopifyCheckoutServiceImpl(MessageService messageService) {
        super(messageService);
    }


    @Override
    public UpdateBuyerIdentityResDTO addShippingAddress(UpdateBuyerIdentityReqDTO updateBuyerIdentityReqDTO) {
        return null;
    }
}
