package com.mindbody.api.service.impl;

import com.mindbody.api.model.EntityUser;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.ShopifyCustomerService;
import org.springframework.stereotype.Service;

@Service
public class ShopifyCustomerServiceImpl extends BaseService implements ShopifyCustomerService {


    private final UserRepository userRepository;

    public ShopifyCustomerServiceImpl(MessageService messageService, UserRepository userRepository) {
        super(messageService);
        this.userRepository = userRepository;
    }

    @Override
    public EntityUser getUserByEmail(String email) {
        return userRepository.findUserByAllEmail(email.toLowerCase(), true, false);
    }

}
