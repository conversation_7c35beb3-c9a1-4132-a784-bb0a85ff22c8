package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.*;
import com.mindbody.api.dto.notification.NotificationDTO;
import com.mindbody.api.enums.AccountStatus;
import com.mindbody.api.enums.AchievementActivityType;
import com.mindbody.api.enums.OtpModuleName;
import com.mindbody.api.enums.RoleType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.UserMapper;
import com.mindbody.api.model.EntityAccessToken;
import com.mindbody.api.model.EntityOTP;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.repository.AccessTokenRepository;
import com.mindbody.api.repository.OTPRepository;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.service.*;
import com.mindbody.api.util.EncryptUtil;
import com.mindbody.api.util.FieldConstant;
import com.mindbody.api.util.StringUtil;
import com.twilio.Twilio;
import com.twilio.rest.api.v2010.account.Message;
import com.twilio.type.PhoneNumber;
import jakarta.validation.Valid;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;

@Service
public class OTPServiceImpl extends BaseService implements OTPService {

    private final OTPRepository otpRepository;

    private final UserRepository userRepository;

    private final EmailService emailService;

    private final GlobalConfiguration globalConfiguration;

    private final EmailVerificationTokenGeneratorService emailVerificationTokenGeneratorService;

    private final UserMapper userMapper;

    private final AccessTokenRepository accessTokenRepository;

    private final UserAchievementService userAchievementService;

    private final NotificationService notificationService;

    private final UserService userService;

    private final UserDeviceService userDeviceService;

    public OTPServiceImpl(MessageService messageService, OTPRepository otpRepository, UserRepository userRepository, EmailService emailService, GlobalConfiguration globalConfiguration, EmailVerificationTokenGeneratorService emailVerificationTokenGeneratorService, UserMapper userMapper, AccessTokenRepository accessTokenRepository, @Lazy UserAchievementService userAchievementService, NotificationService notificationService, UserService userService, UserDeviceService userDeviceService) {
        super(messageService);
        this.otpRepository = otpRepository;
        this.userRepository = userRepository;
        this.emailService = emailService;
        this.globalConfiguration = globalConfiguration;
        this.emailVerificationTokenGeneratorService = emailVerificationTokenGeneratorService;
        this.userMapper = userMapper;
        this.accessTokenRepository = accessTokenRepository;
        this.userAchievementService = userAchievementService;
        this.notificationService = notificationService;
        this.userService = userService;
        this.userDeviceService = userDeviceService;
    }


    @Override
    public UserResDTO sendVerificationOTP(SendOTPReqDTO sendOTPReqDTO) {
        if ((StringUtil.nonNullNonEmpty(sendOTPReqDTO.getEmail()) && (StringUtil.nonNullNonEmpty(sendOTPReqDTO.getCountryCode()) || StringUtil.nonNullNonEmpty(sendOTPReqDTO.getPhoneNumber()))) ||
                (StringUtil.nullOrEmpty(sendOTPReqDTO.getEmail()) && (StringUtil.nullOrEmpty(sendOTPReqDTO.getCountryCode()) || StringUtil.nullOrEmpty(sendOTPReqDTO.getPhoneNumber())))) {
            throw new BusinessValidationException("either_provide_email_or_phone_number");
        }

        /** Send OTP on email */
        EntityUser entityUser = new EntityUser();
        if (sendOTPReqDTO.getEmail() != null) {
            entityUser = userRepository.findUserByAllEmail(sendOTPReqDTO.getEmail(), true, false);
            if (Objects.isNull(entityUser)) {
                throw new BusinessValidationException("user_not_found_with_email_error");
            }
            Map<String, Object> map = new HashMap<>();
            map.put("year", Integer.valueOf(LocalDate.now().getYear()).toString());
            map.put("otp", generateAndSaveOTP(entityUser, sendOTPReqDTO.getModuleName()));
            if (sendOTPReqDTO.getModuleName().equals(OtpModuleName.ACCOUNT_VERIFICATION)) {
                emailService.sendEmail(FieldConstant.EmailTemplateName.TEMPLATE_ACCOUNT_VERIFICATION_OTP,
                        "MindBodyWarrior - OTP for Email Verification",
                        sendOTPReqDTO.getEmail(),
                        map);
            } else {
                emailService.sendEmail(FieldConstant.EmailTemplateName.TEMPLATE_RESET_PASSWORD_OTP,
                        "MindBodyWarrior - Reset Password",
                        sendOTPReqDTO.getEmail(),
                        map);
            }
        }
        /** Send OPT on phone number */
        else {

            entityUser = userRepository.findUserByCountryCodeAndPhoneNumber(sendOTPReqDTO.getCountryCode(), sendOTPReqDTO.getPhoneNumber(), true, false);
            if (Objects.isNull(entityUser)) {
                throw new BusinessValidationException("user_not_found_with_phone_number_error");
            }

            if (sendOTPReqDTO.getModuleName().equals(OtpModuleName.ACCOUNT_VERIFICATION)) {
                String otp = generateAndSaveOTP(entityUser, OtpModuleName.ACCOUNT_VERIFICATION);
                String otpMessage = "Thank you for choosing MindBodyWarrior. Use this OTP ##" + otp + "## to complete your Sign Up procedures and verify your account on MindBodyWarrior.";
                sendOTPViaPhoneNumber(sendOTPReqDTO, otpMessage);
            } else {
                String otp = generateAndSaveOTP(entityUser, OtpModuleName.RESET_PASSWORD);
                String otpMessage = "Use this OTP ##" + otp + "## to reset your password. this OTP will expire in 10 minutes.";
                sendOTPViaPhoneNumber(sendOTPReqDTO, otpMessage);
            }

        }
        return userMapper.toDTO(entityUser);

    }

    private void sendOTPViaPhoneNumber(SendOTPReqDTO sendOTPReqDTO, String otpMessage) {
        try {
            Twilio.init(globalConfiguration.getTwillioConfig().getAccountSid(), globalConfiguration.getTwillioConfig().getAuthToken());
            //TODO need to change the toPhoneNumber
            PhoneNumber to = new PhoneNumber(sendOTPReqDTO.getCountryCode() + sendOTPReqDTO.getPhoneNumber());
            PhoneNumber from = new PhoneNumber(globalConfiguration.getTwillioConfig().getFromNumber());
            Message message = Message.creator(to, from, otpMessage).create();
        } catch (Exception e) {
            throw new BusinessValidationException("twillio_send_message_error");
        }
    }

    @Override
    public String generateAndSaveOTP(EntityUser entityUser, OtpModuleName moduleName) {

        /** Set isActive-false  and isDeleted true to existing OTPs for current userId  */
        Optional<EntityOTP> entityOTPOptional = otpRepository.findByUserIdAndModuleNameAndIsActiveAndIsDeleted(entityUser.getUserId(), moduleName, true, false);
        if (entityOTPOptional.isPresent()) {
            entityOTPOptional.get().setActive(false);
            entityOTPOptional.get().setDeleted(true);
            otpRepository.save(entityOTPOptional.get());
        }

        StringBuilder generatedSbOTP = new StringBuilder();
        SplittableRandom splittableRandom = new SplittableRandom();

        /** generate the 4 digit OTP  */
        for (int i = 0; i < 4; i++) {
            int randomNumber = splittableRandom.nextInt(0, 9);
            generatedSbOTP.append(randomNumber);
        }
        String generatedOTP = generatedSbOTP.toString();

        /** Set expiration time to 10 minutes */
        Date expiryDate = DateUtils.addMinutes(new Date(), 10);
        EntityOTP entityOTP = new EntityOTP(entityUser, entityUser.getUserId(), generatedOTP, expiryDate, moduleName);
        otpRepository.save(entityOTP);

        return generatedOTP;
    }

    @Override
    public VerifyOTPResDTO verifyOTP(@Valid VerifyOTPReqDTO verifyOTPReqDTO) {
        String resetPasswordToken = "";
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(verifyOTPReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        Optional<EntityOTP> entityOTPOptional = otpRepository.findByOtpAndUserIdAndModuleNameAndExpiryDateAndIsActiveAndIsDeleted(verifyOTPReqDTO.getOtp(), verifyOTPReqDTO.getUserId(), verifyOTPReqDTO.getModuleName(), new Date(), true, false);
        if (entityOTPOptional.isPresent()) {
            EntityOTP entityOTP = entityOTPOptional.get();
            entityOTP.setActive(false);
            entityOTP.setDeleted(true);
            otpRepository.save(entityOTP);

            if (verifyOTPReqDTO.getModuleName().equals(OtpModuleName.ACCOUNT_VERIFICATION)) {
                entityUser.setAccountStatus(AccountStatus.VERIFIED);
                userRepository.save(entityUser);
                if (!userAchievementService.existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Account_Verify_email_phone, entityUser.getUserId())) {
                    userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Account_Verify_email_phone, entityUser.getUserId()));
                    ;
                }
                /** Save FCM Token **/
                userService.saveFcmTokenDetail(entityUser, verifyOTPReqDTO.getDeviceUniqueId(), verifyOTPReqDTO.getFcmToken(), verifyOTPReqDTO.getDeviceType());

                /** Send Welcome Notification To User **/
                NotificationDTO notificationDTO = notificationService.setWelcomeNotificationData();
                notificationService.sendNotificationToAppUser(entityUser, notificationDTO);
            }
            /** generate the security token for the reset password*/
            else {
                resetPasswordToken = emailVerificationTokenGeneratorService.getVerificationTokenForUser(entityUser);
            }
            // Retrieve user information for response
            UserSignupResDTO userAllInformation = getUserAllInformation(entityUser.getUserId());

            String accessToken = "";
            if (verifyOTPReqDTO.getModuleName().equals(OtpModuleName.ACCOUNT_VERIFICATION)) {
                // Fetch the latest access token for the user and role
                Optional<EntityAccessToken> latestAccessToken = accessTokenRepository
                        .findFirstByUserIdAndRoleTypeOrderByCreatedAtDesc(entityUser.getUserId(), RoleType.USER);
                if (latestAccessToken.isPresent() && !StringUtil.nullOrEmpty(latestAccessToken.get().getAccessToken())) {
                    accessToken = EncryptUtil.decryptKey(latestAccessToken.get().getAccessToken());
                }
            }

            userDeviceService.trackUserDevice(
                    entityUser,
                    verifyOTPReqDTO.getDeviceUniqueId(),
                    userDeviceService.getCurrentIpAddress(),
                    verifyOTPReqDTO.getDeviceType());

            // Return both the token and user information in the response DTO
            return new VerifyOTPResDTO(
                    userAllInformation.getUserId(),
                    userAllInformation.getEmail(),
                    userAllInformation.getCountryCode(),
                    userAllInformation.getPhoneNumber(),
                    userAllInformation.getRoleType(),
                    userAllInformation.getUserType(),
                    entityUser.getAccountStatus(), // Updated to fetch from entityUser
                    userAllInformation.getCreatedAt(),
                    userAllInformation.getUpdatedAt(),
                    userAllInformation.isDeleted(),
                    userAllInformation.isActive(),
                    userAllInformation.getName(),
                    userAllInformation.getDateOfBirth(),
                    userAllInformation.getPlaceOfBirth(),
                    userAllInformation.getProfileImage(),
                    userAllInformation.getGenderType(),
                    userAllInformation.getZodiacSign(),
                    userAllInformation.getTimezone(),
                    accessToken,
                    resetPasswordToken,
                    userAllInformation.getThemeMode());
        } else {
            throw new BusinessValidationException("otp_not_found_error");
        }
    }

    public UserSignupResDTO getUserAllInformation(Long userId) {
        String profilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getUserProfileFolderName();
        String borderImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBorderFolderName();
        String badgeImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBadgeFolderName();
        return userRepository.findAllUserInformation(userId, profilePath, borderImagePath, badgeImagePath);
    }

}
