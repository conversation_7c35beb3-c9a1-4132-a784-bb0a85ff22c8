package com.mindbody.api.service.impl;

import com.mindbody.api.dto.cms.AchievementMedalListResDTO;
import com.mindbody.api.repository.AchievementMedalsRepository;
import com.mindbody.api.service.AchievementMedalService;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AchievementMedalServiceImpl extends BaseService implements AchievementMedalService {

    private final AchievementMedalsRepository achievementMedalsRepository;

    public AchievementMedalServiceImpl(MessageService messageService, AchievementMedalsRepository achievementMedalsRepository) {
        super(messageService);
        this.achievementMedalsRepository = achievementMedalsRepository;
    }

    @Override
    public List<AchievementMedalListResDTO> listAchievementMedal() {
        return achievementMedalsRepository.findAllActiveMedals(true, false);
    }
}
