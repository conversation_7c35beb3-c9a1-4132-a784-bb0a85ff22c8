package com.mindbody.api.service.impl;

import com.mindbody.api.dto.*;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.UserAstrologyInfoMapper;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.model.EntityUserAstrologyInfo;
import com.mindbody.api.model.EntityUserInfo;
import com.mindbody.api.repository.UserAstrologyInfoRepository;
import com.mindbody.api.repository.UserInfoRepository;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.service.AstrologyService;
import com.mindbody.api.service.AstrologyThirdPartyService;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.util.FieldConstant;
import com.mindbody.api.util.Methods;
import com.mindbody.api.util.StringUtil;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class AstrologyServiceImpl extends BaseService implements AstrologyService {

    private final UserRepository userRepository;

    private final UserInfoRepository userInfoRepository;

    private final UserAstrologyInfoRepository userAstrologyInfoRepository;

    private final UserAstrologyInfoMapper userAstrologyInfoMapper;

    private final AstrologyThirdPartyService astrologyThirdPartyService;

    public AstrologyServiceImpl(MessageService messageService, UserRepository userRepository, UserInfoRepository userInfoRepository, UserAstrologyInfoRepository userAstrologyInfoRepository, UserAstrologyInfoMapper userAstrologyInfoMapper, AstrologyThirdPartyService astrologyThirdPartyService) {
        super(messageService);
        this.userRepository = userRepository;
        this.userInfoRepository = userInfoRepository;
        this.userAstrologyInfoRepository = userAstrologyInfoRepository;
        this.userAstrologyInfoMapper = userAstrologyInfoMapper;
        this.astrologyThirdPartyService = astrologyThirdPartyService;
    }

    @Override
    public GenerateUserAstrologyResDTO generateUserAstrologyDetails(UserIdDTO userIdDTO) {

        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(userIdDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(entityUser.getUserId(), true, false);
        if (Objects.isNull(entityUserInfo)) {
            throw new BusinessValidationException("user_info_not_found_error");
        }

        LocalDateTime date = Methods.convertUtcToUserTimeZone(entityUserInfo.getDateOfBirth(), entityUserInfo.getTimezone());
        double latitude = entityUserInfo.getLatitude();
        double longitude = entityUserInfo.getLongitude();

        Integer timeZoneWithDst = null;

        String wheelChartUrl;
        if (StringUtil.nullOrEmpty(entityUserInfo.getWheelChartUrl())) {
            timeZoneWithDst = getTimeZoneWithDstIfNeeded(timeZoneWithDst, latitude, longitude, entityUserInfo.getDateOfBirth());
            UserWheelChartResDTO userWheelChartResDTO = astrologyThirdPartyService.getUserWheelChartDetails(new UserWheelChartReqDTO(
                    date.getDayOfMonth(), date.getMonthValue(), date.getYear(),
                    date.getHour(), date.getMinute(), latitude, longitude,
                    timeZoneWithDst, FieldConstant.Astrology.PLACIDUS,
                    FieldConstant.Astrology.INNER_CIRCLE_BACKGROUND,
                    FieldConstant.Astrology.PLANET_ICON_COLOR,
                    FieldConstant.Astrology.SIGN_ICON_COLOR,
                    FieldConstant.Astrology.SIGN_BACKGROUND,
                    FieldConstant.Astrology.CHART_SIZE,
                    FieldConstant.Astrology.IMAGE_TYPE));
            wheelChartUrl = userWheelChartResDTO.getChartUrl();
            entityUserInfo.setWheelChartUrl(wheelChartUrl);
            userInfoRepository.save(entityUserInfo);
        } else {
            wheelChartUrl = entityUserInfo.getWheelChartUrl();
        }
        timeZoneWithDst = getTimeZoneWithDstIfNeeded(timeZoneWithDst, latitude, longitude, entityUserInfo.getDateOfBirth());

        List<EntityUserAstrologyInfo> existingAstrologyInfoList = userAstrologyInfoRepository.findByUserIdAndIsActiveAndIsDeleted(userIdDTO.getUserId(), true, false);

        List<EntityUserAstrologyInfo> updatedAstrologyInfoList = new ArrayList<>();

        List<UserAstrologyDetailResDTO> planetDetails = astrologyThirdPartyService.getUserPlanetDetails(new UserAstrologyReqDTO(
                        date.getDayOfMonth(), date.getMonthValue(), date.getYear(),
                        date.getHour(), date.getMinute(), latitude, longitude,
                        timeZoneWithDst, FieldConstant.Astrology.PLACIDUS))
                .stream()
                .filter(dto -> !"Ascendant".equals(dto.getName()))
                .toList();

        if (existingAstrologyInfoList != null && !existingAstrologyInfoList.isEmpty()) {
            Map<String, EntityUserAstrologyInfo> astrologyInfoMap = existingAstrologyInfoList.stream().collect(Collectors.toMap(EntityUserAstrologyInfo::getName, info -> info));
            for (UserAstrologyDetailResDTO userAstrologyDetailResDTO : planetDetails) {
                String planetName = userAstrologyDetailResDTO.getName();
                EntityUserAstrologyInfo userAstrologyInfo = astrologyInfoMap.get(planetName);
                if (StringUtil.nullOrEmpty(userAstrologyInfo.getReport()) && !userAstrologyInfo.getName().equals("Ascendant")) {
                    UserPlanetSignReportResDTO userPlanetSignReportResDTO = astrologyThirdPartyService.getUserPlanetSignReport(new UserAstrologyReqDTO(
                            date.getDayOfMonth(), date.getMonthValue(), date.getYear(),
                            date.getHour(), date.getMinute(), latitude, longitude,
                            timeZoneWithDst, FieldConstant.Astrology.PLACIDUS), planetName);
                    userAstrologyInfo.setReport(userPlanetSignReportResDTO.getReport());
                    updatedAstrologyInfoList.add(userAstrologyInfo);
                }
            }
        } else {
            for (UserAstrologyDetailResDTO userAstrologyDetailResDTO : planetDetails) {
                UserPlanetSignReportResDTO userPlanetSignReportResDTO = astrologyThirdPartyService.getUserPlanetSignReport(new UserAstrologyReqDTO(
                        date.getDayOfMonth(), date.getMonthValue(), date.getYear(),
                        date.getHour(), date.getMinute(), latitude, longitude,
                        timeZoneWithDst, FieldConstant.Astrology.PLACIDUS), userAstrologyDetailResDTO.getName());
                EntityUserAstrologyInfo userAstrologyInfo = userAstrologyInfoMapper.toModel(userAstrologyDetailResDTO);
                userAstrologyInfo.setReport(userPlanetSignReportResDTO.getReport());
                userAstrologyInfo.setEntityUser(entityUser);
                userAstrologyInfo.setUserId(entityUser.getUserId());
                updatedAstrologyInfoList.add(userAstrologyInfo);
            }
        }

        if (!updatedAstrologyInfoList.isEmpty()) {
            userAstrologyInfoRepository.saveAll(updatedAstrologyInfoList);
        }

        List<UserAstrologyDetailResDTO> userAstrologyDetailResDTOS = ((existingAstrologyInfoList != null && !existingAstrologyInfoList.isEmpty()) ? existingAstrologyInfoList : updatedAstrologyInfoList)
                .stream()
                .map(userAstrologyInfoMapper::toDTO)
                .filter(dto -> !"Ascendant".equals(dto.getName()))
                .toList();

        GenerateUserAstrologyResDTO generateUserAstrologyResDTO = new GenerateUserAstrologyResDTO();
        generateUserAstrologyResDTO.setWheelCharUrl(wheelChartUrl);
        generateUserAstrologyResDTO.setUserAstrologyDetailResDTOList(userAstrologyDetailResDTOS);
        return generateUserAstrologyResDTO;
    }

    @Override
    public String updateUserAstrologyDetails(Long userId, double latitude, double longitude, LocalDateTime dateOfBirth, String timezone) {

        LocalDateTime date = Methods.convertUtcToUserTimeZone(dateOfBirth, timezone);
        String formattedDate = date.format(DateTimeFormatter.ofPattern(FieldConstant.DATE_PATTERN));
        TimezoneWithDstResDTO timeZoneWithDst = astrologyThirdPartyService.getTimeZoneWithDst(new TimeZoneWithDstReqDTO(latitude, longitude, formattedDate));

        UserWheelChartResDTO userWheelChartResDTO = astrologyThirdPartyService.getUserWheelChartDetails(new UserWheelChartReqDTO(
                date.getDayOfMonth(), date.getMonthValue(), date.getYear(),
                date.getHour(), date.getMinute(), latitude, longitude,
                timeZoneWithDst.getTimezone(), FieldConstant.Astrology.PLACIDUS,
                FieldConstant.Astrology.INNER_CIRCLE_BACKGROUND,
                FieldConstant.Astrology.PLANET_ICON_COLOR,
                FieldConstant.Astrology.SIGN_ICON_COLOR,
                FieldConstant.Astrology.SIGN_BACKGROUND,
                FieldConstant.Astrology.CHART_SIZE,
                FieldConstant.Astrology.IMAGE_TYPE));

        List<EntityUserAstrologyInfo> existingAstrologyInfoList = userAstrologyInfoRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false);

        if (existingAstrologyInfoList != null && !existingAstrologyInfoList.isEmpty()) {
            List<UserAstrologyDetailResDTO> planetDetails = astrologyThirdPartyService.getUserPlanetDetails(new UserAstrologyReqDTO(
                            date.getDayOfMonth(), date.getMonthValue(), date.getYear(),
                            date.getHour(), date.getMinute(), latitude, longitude,
                            timeZoneWithDst.getTimezone(), FieldConstant.Astrology.PLACIDUS))
                    .stream()
                    .filter(dto -> !"Ascendant".equals(dto.getName()))
                    .toList();

            Map<String, EntityUserAstrologyInfo> astrologyInfoMap = existingAstrologyInfoList.stream().collect(Collectors.toMap(EntityUserAstrologyInfo::getName, info -> info));

            for (UserAstrologyDetailResDTO userAstrologyDetailResDTO : planetDetails) {
                String planetName = userAstrologyDetailResDTO.getName();
                EntityUserAstrologyInfo userAstrologyInfo = astrologyInfoMap.get(planetName);

                if (userAstrologyInfo != null) {
                    userAstrologyInfo.setHouse(userAstrologyDetailResDTO.getHouse());
                    userAstrologyInfo.setSign(userAstrologyDetailResDTO.getSign());
                    UserPlanetSignReportResDTO userPlanetSignReportResDTO = astrologyThirdPartyService.getUserPlanetSignReport(new UserAstrologyReqDTO(
                            date.getDayOfMonth(), date.getMonthValue(), date.getYear(),
                            date.getHour(), date.getMinute(), latitude, longitude,
                            timeZoneWithDst.getTimezone(), FieldConstant.Astrology.PLACIDUS), planetName);
                    userAstrologyInfo.setReport(userPlanetSignReportResDTO.getReport());
                }
            }
            userAstrologyInfoRepository.saveAll(existingAstrologyInfoList);
        }
        return userWheelChartResDTO.getChartUrl();
    }

    private Integer getTimeZoneWithDstIfNeeded(Integer timeZoneWithDst, double latitude, double longitude, LocalDateTime date) {
        if (timeZoneWithDst == null) {
            String formattedDate = date.format(DateTimeFormatter.ofPattern(FieldConstant.DATE_PATTERN));
            TimezoneWithDstResDTO timezoneWithDstResDTO = astrologyThirdPartyService.getTimeZoneWithDst(new TimeZoneWithDstReqDTO(latitude, longitude, formattedDate));
            return timezoneWithDstResDTO.getTimezone();
        } else {
            return timeZoneWithDst;
        }
    }

    @Override
    public List<UserAstrologyDetailResDTO> saveUserPlanetDetails(LocalDateTime dateOfBirth, String timezone, double latitude, double longitude, EntityUser entityUser) {
        LocalDateTime date = Methods.convertUtcToUserTimeZone(dateOfBirth, timezone);
        String stringDate = date.format(DateTimeFormatter.ofPattern(FieldConstant.DATE_PATTERN));
        TimezoneWithDstResDTO timeZoneWithDst = astrologyThirdPartyService.getTimeZoneWithDst(new TimeZoneWithDstReqDTO(latitude, longitude, stringDate));
        List<UserAstrologyDetailResDTO> userPlanetDetails = astrologyThirdPartyService.getUserPlanetDetails(new UserAstrologyReqDTO(date.getDayOfMonth(), date.getMonthValue(), date.getYear(), date.getHour(), date.getMinute(), latitude, longitude, timeZoneWithDst.getTimezone(), FieldConstant.Astrology.PLACIDUS));
        userPlanetDetails.removeIf(detail -> "Ascendant".equalsIgnoreCase(detail.getName()));
        List<EntityUserAstrologyInfo> entityUserAstrologyInfoList = new ArrayList<>();
        for (UserAstrologyDetailResDTO userAstrologyDetailResDTO : userPlanetDetails) {
            EntityUserAstrologyInfo entityUserAstrologyInfo = userAstrologyInfoMapper.toModel(userAstrologyDetailResDTO);
            entityUserAstrologyInfo.setEntityUser(entityUser);
            entityUserAstrologyInfo.setUserId(entityUser.getUserId());
            entityUserAstrologyInfoList.add(entityUserAstrologyInfo);
        }
        userAstrologyInfoRepository.saveAll(entityUserAstrologyInfoList);
        return userPlanetDetails;
    }

    @Override
    public UserDailyHoroscopeResDTO generateUserDailyHoroscope(UserIdDTO userIdDTO) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(userIdDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(entityUser.getUserId(), true, false);
        if (Objects.isNull(entityUserInfo)) {
            throw new BusinessValidationException("user_info_not_found_error");
        }
        double latitude = entityUserInfo.getLatitude();
        double longitude = entityUserInfo.getLongitude();

        Integer timeZoneWithDst = getTimeZoneWithDstIfNeeded(null, latitude, longitude, entityUserInfo.getDateOfBirth());
        return astrologyThirdPartyService.getUserDailyHoroscope(new UserHoroscopeReqDTO(timeZoneWithDst), String.valueOf(entityUserInfo.getZodiacSign()).toLowerCase());
    }

    @Override
    public UserMonthlyHoroscopeResDTO generateUserMonthlyHoroscope(UserIdDTO userIdDTO) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(userIdDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(entityUser.getUserId(), true, false);
        if (Objects.isNull(entityUserInfo)) {
            throw new BusinessValidationException("user_info_not_found_error");
        }
        double latitude = entityUserInfo.getLatitude();
        double longitude = entityUserInfo.getLongitude();

        Integer timeZoneWithDst = getTimeZoneWithDstIfNeeded(null, latitude, longitude, entityUserInfo.getDateOfBirth());
        return astrologyThirdPartyService.getUserMonthlyHoroscope(new UserHoroscopeReqDTO(timeZoneWithDst), String.valueOf(entityUserInfo.getZodiacSign()).toLowerCase());
    }


}
