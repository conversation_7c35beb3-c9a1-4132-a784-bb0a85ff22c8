package com.mindbody.api.service.impl;


import com.mindbody.api.dto.cms.DashboardResDTO;
import com.mindbody.api.enums.UserType;
import com.mindbody.api.enums.WorkoutPlaylistType;
import com.mindbody.api.repository.*;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.DashboardService;
import com.mindbody.api.service.MessageService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class DashboardServiceImpl extends BaseService implements DashboardService {

    private final ExerciseRepository exerciseRepository;

    private final WorkoutPlanRepository workoutPlanRepository;

    private final AudioRepository audioRepository;

    private final UserRepository userRepository;

    private final UserPointsTrackerRepository userPointsTrackerRepository;

    public DashboardServiceImpl(MessageService messageService, ExerciseRepository exerciseRepository, WorkoutPlanRepository workoutPlanRepository, AudioRepository audioRepository, UserRepository userRepository, UserPointsTrackerRepository userPointsTrackerRepository) {
        super(messageService);
        this.exerciseRepository = exerciseRepository;
        this.workoutPlanRepository = workoutPlanRepository;
        this.audioRepository = audioRepository;
        this.userRepository = userRepository;
        this.userPointsTrackerRepository = userPointsTrackerRepository;
    }

    @Override
    public DashboardResDTO viewDashboardData() {
        DashboardResDTO dashboardResDTO = new DashboardResDTO();
        dashboardResDTO.setStrengthWorkoutPlanCount(workoutPlanRepository.countWorkoutPlanForCMSDashboard(false, true, WorkoutPlaylistType.Strength));
        dashboardResDTO.setFlexibilityWorkoutPlanCount(workoutPlanRepository.countWorkoutPlanForCMSDashboard(false, true, WorkoutPlaylistType.Flexibility));
        dashboardResDTO.setEnduranceWorkoutPlanCount(workoutPlanRepository.countWorkoutPlanForCMSDashboard(false, true, WorkoutPlaylistType.Endurance));
        dashboardResDTO.setAudioCount(audioRepository.countAudioForCMSDashboard(false, true));
        dashboardResDTO.setRegisteredAndAnonymousUsersCount(getUserStats());
        dashboardResDTO.setUsersCountByAchievementLevel(getUserLevelStats());
        return dashboardResDTO;
    }

    public List<DashboardResDTO.UserStatsDTO> getUserStats() {
        LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);
        LocalDateTime todayStart = now.toLocalDate().atStartOfDay();
        LocalDateTime weekStart = now.minusDays(now.getDayOfWeek().getValue() - 1).toLocalDate().atStartOfDay();
        LocalDateTime monthStart = now.withDayOfMonth(1).toLocalDate().atStartOfDay();
        LocalDateTime quarterStart = now.withMonth(((now.getMonthValue() - 1) / 3) * 3 + 1).withDayOfMonth(1).toLocalDate().atStartOfDay();
        LocalDateTime yearStart = now.withDayOfYear(1).toLocalDate().atStartOfDay();

        List<DashboardResDTO.UserStatsDTO> userStatsList = new ArrayList<>();

        userStatsList.add(new DashboardResDTO.UserStatsDTO("Today",
                userRepository.countUsersByDateAndType(todayStart, UserType.NORMAL),
                userRepository.countUsersByDateAndType(todayStart, UserType.ANONYMOUS)));

        userStatsList.add(new DashboardResDTO.UserStatsDTO("This Week",
                userRepository.countUsersByDateAndType(weekStart, UserType.NORMAL),
                userRepository.countUsersByDateAndType(weekStart, UserType.ANONYMOUS)));

        userStatsList.add(new DashboardResDTO.UserStatsDTO("This Month",
                userRepository.countUsersByDateAndType(monthStart, UserType.NORMAL),
                userRepository.countUsersByDateAndType(monthStart, UserType.ANONYMOUS)));

        userStatsList.add(new DashboardResDTO.UserStatsDTO("This Quarter",
                userRepository.countUsersByDateAndType(quarterStart, UserType.NORMAL),
                userRepository.countUsersByDateAndType(quarterStart, UserType.ANONYMOUS)));

        userStatsList.add(new DashboardResDTO.UserStatsDTO("This Year",
                userRepository.countUsersByDateAndType(yearStart, UserType.NORMAL),
                userRepository.countUsersByDateAndType(yearStart, UserType.ANONYMOUS)));
        return userStatsList;
    }

    public List<DashboardResDTO.UserLevelStatsDTO> getUserLevelStats() {
        return Stream.of(
                new AbstractMap.SimpleEntry<>(1, 10),
                new AbstractMap.SimpleEntry<>(11, 20),
                new AbstractMap.SimpleEntry<>(21, 30),
                new AbstractMap.SimpleEntry<>(31, 40),
                new AbstractMap.SimpleEntry<>(41, 50)
        ).map(entry -> new DashboardResDTO.UserLevelStatsDTO(
                entry.getKey() + "-" + entry.getValue(),
                userPointsTrackerRepository.countUsersByLevelRange(entry.getKey(), entry.getValue())
        )).collect(Collectors.toList());
    }
}
