package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.*;
import com.mindbody.api.dto.notification.NotificationDTO;
import com.mindbody.api.enums.NotificationType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.EntityAudio;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.model.EntityUserReferral;
import com.mindbody.api.repository.AudioRepository;
import com.mindbody.api.repository.UserReferralRepository;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.service.NotificationService;
import com.mindbody.api.service.UserDeviceService;
import com.mindbody.api.service.UserReferralService;
import com.mindbody.api.service.UserUnlockedAudioService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserReferralServiceImpl implements UserReferralService {

    private final UserReferralRepository userReferralRepository;
    private final UserRepository userRepository;
    private final GlobalConfiguration globalConfiguration;
    private final AudioRepository audioRepository;
    private final UserUnlockedAudioService userUnlockedAudioService;
    private final NotificationService notificationService;
    private final UserDeviceService userDeviceService;

    // Maximum number of exclusive audios that can be unlocked through referrals
    private static final int MAX_REFERRAL_UNLOCKS = 5;

    @Override
    @Transactional
    public void submitReferralCode(SubmitReferralCodeDTO submitReferralCodeDTO) {
        log.info("Submit referral code API called for user ID: {}", submitReferralCodeDTO.getUserId());

        // Get current user
        Optional<EntityUser> currentUser = userRepository.findById(submitReferralCodeDTO.getUserId());
        if (currentUser.isEmpty()) {
            throw new BusinessValidationException("user_not_found_error");
        }

        // Check if the user is trying to enter their own referral code
        if (Objects.nonNull(currentUser.get().getReferralCode()) && currentUser.get().getReferralCode().equals(submitReferralCodeDTO.getCode())) {
            throw new BusinessValidationException("cannot_enter_own_referral_code_error");
        }

        // Check if the user has already entered a referral code
        boolean hasAlreadyEnteredCode = userReferralRepository.existsByToUserIdAndIsDeleted(submitReferralCodeDTO.getUserId(), false);
        if (hasAlreadyEnteredCode) {
            throw new BusinessValidationException("referral_code_already_entered_error");
        }

        // Find the user who owns the referral code
        Optional<EntityUser> fromUser = userRepository.findByReferralCodeAndIsDeletedAndIsActive(submitReferralCodeDTO.getCode(), false, true);
        if (fromUser.isEmpty()) {
            throw new BusinessValidationException("referral_code_invalid_error");
        }

        // Get the current IP address
        String ipAddress = userDeviceService.getCurrentIpAddress();
        log.info("Referral submission from IP: {}", ipAddress);

        // Check if the referral is coming from the same device or IP as the referrer
        if (userDeviceService.isDeviceOrIpAssociatedWithOtherUser(
                submitReferralCodeDTO.getUserId(),
                submitReferralCodeDTO.getDeviceUniqueId(),
                ipAddress)) {
            log.warn("Attempted referral from same device/IP as another user. User ID: {}, Device ID: {}, IP: {}",
                    submitReferralCodeDTO.getUserId(), submitReferralCodeDTO.getDeviceUniqueId(), ipAddress);
            throw new BusinessValidationException("referral_must_come_from_different_device_error");
        }

        // Check if any referral code was entered using the same device in the past
        if (userDeviceService.hasDeviceEnteredAnyReferralCodeBefore(submitReferralCodeDTO.getDeviceUniqueId())) {
            log.warn("Attempted referral from a device that has already been used to enter a referral code. User ID: {}, Device ID: {}",
                    submitReferralCodeDTO.getUserId(), submitReferralCodeDTO.getDeviceUniqueId());
            throw new BusinessValidationException("device_already_used_for_referral_error");
        }

        // Track the user's device and IP
        userDeviceService.trackUserDevice(
                currentUser.get(),
                submitReferralCodeDTO.getDeviceUniqueId(),
                ipAddress,
                submitReferralCodeDTO.getDeviceType());

        // Create the referral record
        EntityUserReferral entityUserReferral = EntityUserReferral.builder()
                .fromUserId(fromUser.get().getUserId())
                .code(submitReferralCodeDTO.getCode())
                .toUserId(submitReferralCodeDTO.getUserId())
                .isActive(true)
                .isDeleted(false)
                .build();

        userReferralRepository.save(entityUserReferral);

        // Send notification to the referrer (fromUser) about someone using their code
        NotificationDTO referralUsedNotification = new NotificationDTO();
        referralUsedNotification.setTitle("🎉 Woohoo! Someone Used Your Referral Code! 🌟");
        referralUsedNotification.setMessage("Amazing news! A fellow warrior has joined using your referral code. Your circle of influence is growing! ✨ Thank you for spreading the positive energy!");
        referralUsedNotification.setNotificationType(NotificationType.REFERRAL_USED.toString());

        Map<String, String> notificationInfo = new HashMap<>();
        notificationInfo.put("title", referralUsedNotification.getTitle());
        notificationInfo.put("message", referralUsedNotification.getMessage());
        notificationInfo.put("notificationType", NotificationType.REFERRAL_USED.name());
        referralUsedNotification.setData(notificationInfo);

        notificationService.sendNotificationToAppUser(fromUser.get(), referralUsedNotification);

        // Unlock exclusive audio for the user whose referral code was used
        unlockExclusiveAudioForReferrer(fromUser.get().getUserId());

        // Unlock one exclusive audio for the user who entered the referral code (first time only)
        unlockExclusiveAudioForNewUser(submitReferralCodeDTO.getUserId());
    }

    /**
     * Unlocks a new exclusive audio for the user whose referral code was used
     * Will only unlock up to MAX_REFERRAL_UNLOCKS exclusive audios
     *
     * @param referrerId The ID of the user whose referral code was used
     */
    private void unlockExclusiveAudioForReferrer(Long referrerId) {
        log.info("Attempting to unlock exclusive audio for referrer with ID: {}", referrerId);

        // Count how many referrals the user has
        long referralCount = userReferralRepository.countReferrals(referrerId, false);
        log.info("User has {} referrals", referralCount);

        // Only unlock if the user has MAX_REFERRAL_UNLOCKS or fewer referrals
        if (referralCount <= MAX_REFERRAL_UNLOCKS) {
            // Get all exclusive audios
            List<EntityAudio> exclusiveAudios = audioRepository.findByIsExclusiveAndIsActiveAndIsDeleted(true, true, false);

            if (exclusiveAudios.isEmpty()) {
                log.warn("No exclusive audios found to unlock");
                return;
            }

            // Find an exclusive audio that is not yet unlocked for this user
            for (EntityAudio audio : exclusiveAudios) {
                if (!userUnlockedAudioService.isAudioUnlocked(referrerId, audio.getAudioId())) {
                    // Unlock this audio for the user
                    userUnlockedAudioService.unlockAudio(referrerId, audio.getAudioId());
                    log.info("Unlocked exclusive audio ID: {} for user ID: {}", audio.getAudioId(), referrerId);

                    // Send notification to the referrer about unlocked content
                    Optional<EntityUser> referrerUser = userRepository.findById(referrerId);
                    if (referrerUser.isPresent()) {
                        // Create notification with the actual unlocked audio details
                        NotificationDTO notificationDTO = notificationService.setExclusiveContentUnlockedNotificationData(true, audio);
                        notificationService.sendNotificationToAppUser(referrerUser.get(), notificationDTO);
                        log.info("Sent notification about unlocked audio to referrer user ID: {}", referrerId);
                    }

                    return; // Only unlock one audio per referral
                }
            }

            log.info("All exclusive audios are already unlocked for user ID: {}", referrerId);
        } else {
            log.info("User ID: {} has already reached the maximum number of referral unlocks ({})",
                    referrerId, MAX_REFERRAL_UNLOCKS);
        }
    }

    /**
     * Unlocks one exclusive audio for a user who has entered a referral code for the first time
     *
     * @param userId The ID of the user who entered the referral code
     */
    private void unlockExclusiveAudioForNewUser(Long userId) {
        log.info("Attempting to unlock exclusive audio for new user with ID: {}", userId);

        // Get all exclusive audios
        List<EntityAudio> exclusiveAudios = audioRepository.findByIsExclusiveAndIsActiveAndIsDeleted(true, true, false);

        if (exclusiveAudios.isEmpty()) {
            log.warn("No exclusive audios found to unlock for new user");
            return;
        }

        // Count how many exclusive audios the user already has unlocked
        long unlockedCount = userUnlockedAudioService.countUnlockedAudios(userId);

        // If the user already has unlocked audios, they might have used a referral code before
        // or unlocked audios through other means, so we'll check if they have any exclusive audios unlocked
        if (unlockedCount > 0) {
            // Check if any exclusive audio is already unlocked
            boolean hasUnlockedExclusive = false;
            for (EntityAudio audio : exclusiveAudios) {
                if (userUnlockedAudioService.isAudioUnlocked(userId, audio.getAudioId())) {
                    hasUnlockedExclusive = true;
                    break;
                }
            }

            // If they already have an exclusive audio unlocked, don't unlock another one
            if (hasUnlockedExclusive) {
                log.info("User ID: {} already has exclusive audio(s) unlocked", userId);
                return;
            }
        }

        // Find the first exclusive audio that is not yet unlocked for this user
        for (EntityAudio audio : exclusiveAudios) {
            if (!userUnlockedAudioService.isAudioUnlocked(userId, audio.getAudioId())) {
                // Unlock this audio for the user
                userUnlockedAudioService.unlockAudio(userId, audio.getAudioId());
                log.info("Unlocked exclusive audio ID: {} for new user ID: {}", audio.getAudioId(), userId);

                // Send notification to the new user about unlocked content
                Optional<EntityUser> newUser = userRepository.findById(userId);
                if (newUser.isPresent()) {
                    // Create notification with the actual unlocked audio details
                    NotificationDTO notificationDTO = notificationService.setExclusiveContentUnlockedNotificationData(false, audio);
                    notificationService.sendNotificationToAppUser(newUser.get(), notificationDTO);
                    log.info("Sent notification about unlocked audio to new user ID: {}", userId);
                }

                return; // Only unlock one audio
            }
        }

        log.info("All exclusive audios are already unlocked for new user ID: {}", userId);
    }

    @Override
    public SearchResultDTO<ReferralCodeDTOList> listReferrals(ReferralListReqDTO referralListReqDTO) {

        Sort sort = null;
        if (referralListReqDTO.getSortBy() != null && referralListReqDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(referralListReqDTO.getSortBy().getDirection(), referralListReqDTO.getSortBy().getProperty());
        }

        Pageable pageable = PageRequest.of(referralListReqDTO.getPage().getPageId(), referralListReqDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());

        List<ReferralCodeDTOList> list = new ArrayList<>();

        long pageCount = 0L;
        pageCount = userReferralRepository.countReferrals(referralListReqDTO.getUserId(), false);

        if (pageCount > 0) {
            String borderImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBorderFolderName();
            String badgeImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBadgeFolderName();
            list = userReferralRepository.listReferrals(referralListReqDTO.getUserId(), borderImagePath, badgeImagePath, pageable);
        }

        return new SearchResultDTO<>(list, pageCount, referralListReqDTO.getPage().getLimit());
    }

}
