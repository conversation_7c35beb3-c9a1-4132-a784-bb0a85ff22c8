package com.mindbody.api.service.impl;

import com.google.firebase.messaging.*;
import com.google.gson.Gson;
import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.ChatAudioDetailDTO;
import com.mindbody.api.dto.ChatMessageDTO;
import com.mindbody.api.dto.ChatMessageHoroscopeDTO;
import com.mindbody.api.dto.ChatMessageMusicDTO;
import com.mindbody.api.dto.notification.NotificationDTO;
import com.mindbody.api.dto.notification.NotificationDetailResDTO;
import com.mindbody.api.dto.notification.SendNotificationToUsersReqDTO;
import com.mindbody.api.dto.notification.SubscribeTopicReqDTO;
import com.mindbody.api.enums.Category;
import com.mindbody.api.enums.NotificationType;
import com.mindbody.api.model.*;
import com.mindbody.api.repository.*;
import com.mindbody.api.security.ExecutionContextUtil;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.NotificationService;
import com.mindbody.api.service.scheduler.ScheduledNotificationService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class NotificationServiceImpl extends BaseService implements NotificationService {

    protected static final Logger logger = LoggerFactory.getLogger(NotificationServiceImpl.class);
    private final NotificationRepository notificationRepository;
    private final FcmTokenRepository fcmTokenRepository;
    private final UserRepository userRepository;
    private final NotificationImageRepository notificationImageRepository;
    private final GlobalConfiguration globalConfiguration;
    private final UserTopicSubscriptionsRepository userTopicSubscriptionsRepository;
    private final ScheduledNotificationService scheduledNotificationService;
    private final String baseS3Url;
    private final UserInfoRepository userInfoRepository;
    private final ChatMessagesRepository chatMessagesRepository;
    private final AudioRepository audioRepository;

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationServiceImpl.class);

    public NotificationServiceImpl(MessageService messageService, NotificationRepository notificationRepository, FcmTokenRepository fcmTokenRepository, UserRepository userRepository, NotificationImageRepository notificationImageRepository, GlobalConfiguration globalConfiguration, UserTopicSubscriptionsRepository userTopicSubscriptionsRepository, @Lazy ScheduledNotificationService scheduledNotificationService, UserInfoRepository userInfoRepository, ChatMessagesRepository chatMessagesRepository, AudioRepository audioRepository) {
        super(messageService);
        this.notificationRepository = notificationRepository;
        this.fcmTokenRepository = fcmTokenRepository;
        this.userRepository = userRepository;
        this.notificationImageRepository = notificationImageRepository;
        this.globalConfiguration = globalConfiguration;
        this.userTopicSubscriptionsRepository = userTopicSubscriptionsRepository;
        this.scheduledNotificationService = scheduledNotificationService;
        this.baseS3Url = globalConfiguration.getAmazonS3().getUrlUpToBucket()
                + globalConfiguration.getAmazonS3().getUploadFolderName();
        this.userInfoRepository = userInfoRepository;
        this.chatMessagesRepository = chatMessagesRepository;
        this.audioRepository = audioRepository;
    }

    private AndroidConfig getAndroidConfig() {
        return AndroidConfig.builder()
                .setTtl(Duration.ofMinutes(10).toMillis())
                .setPriority(AndroidConfig.Priority.HIGH)
                .setNotification(AndroidNotification.builder().setSound("default")
                        //.setNotificationCount()
                        .setColor("#FFFF00").build())
                .build();
    }

    private ApnsConfig getApnsConfig() {
        return ApnsConfig.builder()
                .putHeader("apns-priority", "10")
                .putHeader("apns-expiration", "1604750400")
                .setAps(Aps
                        .builder()
                        .setSound("default")
//                        .setBadge()
                        .build())
                .build();
    }

    @Override
    @Async
    public void sendNotificationToUsersFromCMS(SendNotificationToUsersReqDTO sendNotificationToUsersReqDTO) {
        List<EntityFcmToken> entityFcmTokenList;
        List<Long> userIdsList = new ArrayList<>();
        List<String> tokenList = new ArrayList<>();

        // Check if a topic is provided
        String[] topics = sendNotificationToUsersReqDTO.getTopicNames();
        if (topics == null || topics.length == 0) {
            // No topic provided, send to specific users
            if (sendNotificationToUsersReqDTO.getUserIds() != null && sendNotificationToUsersReqDTO.getUserIds().length > 0) {
                userIdsList = Arrays.asList(sendNotificationToUsersReqDTO.getUserIds());
            }

            // Fetch FCM tokens for users
            entityFcmTokenList = fcmTokenRepository.findByUserIdIn(userIdsList);
            if (!entityFcmTokenList.isEmpty()) {
                tokenList = entityFcmTokenList.stream()
                        .map(EntityFcmToken::getFcmToken)
                        .filter(StringUtil::nonNullNonEmpty)
                        .distinct()
                        .collect(Collectors.toList());
            }
        }

        // Build notification data
        Map<String, String> notificationInfo = new HashMap<>();
        NotificationDTO notificationDTO = new NotificationDTO();
        notificationDTO.setTitle(sendNotificationToUsersReqDTO.getTitle());
        notificationDTO.setMessage(sendNotificationToUsersReqDTO.getMessage());
        notificationDTO.setNotificationType(sendNotificationToUsersReqDTO.getNotificationType());
        notificationDTO.setAiChatCategory(StringUtil.nonNullNonEmpty(sendNotificationToUsersReqDTO.getCategory())?sendNotificationToUsersReqDTO.getCategory():null);
        EntityNotificationImage entityNotificationImage = null;
        if (sendNotificationToUsersReqDTO.getNotificationType() != null) {
            entityNotificationImage = notificationImageRepository.findByNotificationType(NotificationType.valueOf(sendNotificationToUsersReqDTO.getNotificationType()));
        }
        if (entityNotificationImage != null) {
            String folderName = globalConfiguration.getAmazonS3().getNotificationImageFolderName();
            notificationDTO.setImageUrl(baseS3Url + folderName + entityNotificationImage.getImage());
            notificationDTO.setImageKey(entityNotificationImage.getImage());
        }
        notificationInfo.put("title", notificationDTO.getTitle());
        notificationInfo.put("message", notificationDTO.getMessage());
        notificationInfo.put("notificationType", sendNotificationToUsersReqDTO.getNotificationType());
        notificationDTO.setData(notificationInfo);
        if (sendNotificationToUsersReqDTO.isScheduled()) {
            bulkNotificationSendSave(sendNotificationToUsersReqDTO, topics, notificationDTO);
//            Optional.ofNullable(sendNotificationToUsersReqDTO.getUserIds())
//                    .ifPresent(userIds -> Arrays.stream(userIds).forEach(userId -> scheduledNotificationService.scheduleNotification(
//                            notificationDTO.getTitle(),
//                            notificationDTO.getMessage(),
//                            userId,
//                            sendNotificationToUsersReqDTO.getScheduledTime(),
//                            NotificationType.valueOf(sendNotificationToUsersReqDTO.getNotificationType()),
//                            null
//                    )));
        } else {
            if (topics != null && topics.length > 0) {
                notificationDTO.setTopicNames(topics);
                sendNotificationToUsersByAdmin(notificationDTO, null, null);
            } else if (!tokenList.isEmpty()) {
                // Send to user-specific FCM tokens
                sendNotificationToUsersByAdmin(notificationDTO, tokenList, userIdsList);
            } else {
                logger.warn("No valid topic or user tokens found. Notification not sent.");
            }
        }
    }

    @Async
    protected void bulkNotificationSendSave(SendNotificationToUsersReqDTO sendNotificationToUsersReqDTO, String[] topics, NotificationDTO notificationDTO) {
        List<EntityNotificationSchedule> listNotificationSchedule = new ArrayList<>();
        Optional<List<EntityUserInfo>> optionalUserInfoList = Optional.empty();
        if (topics != null && topics.length > 0) {
            List<Long> userIds =userTopicSubscriptionsRepository.findUserIdsByTopicNames(Arrays.asList(topics));
            optionalUserInfoList = userInfoRepository.findByUserIdInAndIsActiveAndIsDeleted(userIds.toArray(new Long[]{}),true, false);
        }else if(sendNotificationToUsersReqDTO.getUserIds() != null && sendNotificationToUsersReqDTO.getUserIds().length > 0){
            optionalUserInfoList = userInfoRepository.findByUserIdInAndIsActiveAndIsDeleted(sendNotificationToUsersReqDTO.getUserIds(),true, false);
        }
        if(optionalUserInfoList.isPresent()){
            List<EntityUserInfo> userInfoList= optionalUserInfoList.get();
            for (EntityUserInfo userInfo : userInfoList) {
                ZonedDateTime systemZonedDateTime = sendNotificationToUsersReqDTO.getScheduledTime().atZone(ZoneId.of(userInfo.getTimezone()));
                LocalDateTime userLocalDateTime = systemZonedDateTime.withZoneSameInstant(ZoneId.of(TimeZone.getDefault().getID())).toLocalDateTime();

                listNotificationSchedule.add(new EntityNotificationSchedule(
                        notificationDTO.getTitle(),
                        notificationDTO.getMessage(),
                        userInfo.getUserId(),
                        userLocalDateTime,
                        false,
                        NotificationType.valueOf(sendNotificationToUsersReqDTO.getNotificationType()),
                        null
                ));
            }
            scheduledNotificationService.saveAll(listNotificationSchedule);
        }
    }

    @Override
    @Transactional
    public List<NotificationDetailResDTO> listNotificationsForUser() {
        Long userId = ExecutionContextUtil.getContext().getUserId();
        LocalDateTime currentDateTimeInUTC = LocalDateTime.now(ZoneOffset.UTC);
        LocalDateTime threeMonthsAgo = currentDateTimeInUTC.minusMonths(3);

        // Update notifications as read
        notificationRepository.markNotificationsAsRead(userId);

        // Fetch notifications
        List<NotificationDetailResDTO> notificationList = notificationRepository.findAllNotifications(userId, threeMonthsAgo);
        // Base S3 URL
        String baseS3Url = globalConfiguration.getAmazonS3().getUrlUpToBucket()
                + globalConfiguration.getAmazonS3().getUploadFolderName();

        // Update image URLs based on notification type
        notificationList.forEach(notification -> {
            NotificationType notificationType = notification.getNotificationType();
            // Determine folder name based on notification type
            String folderName;
            if (notificationType != null) {
                folderName = switch (notificationType) {
                    case WELCOME -> globalConfiguration.getAmazonS3().getNotificationImageFolderName();
                    case MIND_ACHIEVEMENT -> globalConfiguration.getAmazonS3().getAudioPlaylistFolderName();
                    case BODY_ACHIEVEMENT -> globalConfiguration.getAmazonS3().getWorkoutPlaylistFolderName();
                    default -> globalConfiguration.getAmazonS3().getNotificationImageFolderName();
                };
            } else {
                folderName = globalConfiguration.getAmazonS3().getNotificationImageFolderName();
            }

            // Ensure we do not append the base URL if the image already contains it
            if (notification.getImage() != null && !notification.getImage().isEmpty()) {
                if (!notification.getImage().startsWith("http")) {
                    notification.setImage(baseS3Url + folderName + notification.getImage());
                }
            } else {
                // Set a default image for missing images
                notification.setImage("");
            }
        });
        return notificationList;
    }

    @Override
    @Transactional
    public void subscribeUserForTopic(SubscribeTopicReqDTO subscribeTopicReqDTO) {
        EntityUser entityUser = userRepository.findByUserId(subscribeTopicReqDTO.getUserId());
        if (entityUser != null) {
            List<String> topicNames = subscribeTopicReqDTO.getTopicNames();
            // Fetch existing subscribed topics to avoid duplicate entries
            List<String> existingSubscriptions = userTopicSubscriptionsRepository
                    .findTopicNamesByUserId(entityUser.getUserId());
            if (!topicNames.isEmpty()) {
                // Filter only new topics that are not already subscribed
                List<EntityUserTopicSubscriptions> newSubscriptions = topicNames.stream()
                        .filter(topic -> !existingSubscriptions.contains(topic)) // Avoid duplicates
                        .map(topicName -> {
                            EntityUserTopicSubscriptions subscription = new EntityUserTopicSubscriptions();
                            subscription.setEntityUser(entityUser);
                            subscription.setUserId(entityUser.getUserId());
                            subscription.setTopicName(topicName);
                            return subscription;
                        })
                        .toList();

                if (!newSubscriptions.isEmpty()) {
                    userTopicSubscriptionsRepository.saveAll(newSubscriptions);
                }
            }
        }
    }


    @Async
    public void sendNotificationToUsersByAdmin(NotificationDTO notificationDTO, List<String> tokens, List<Long> userIds) {
        try {
            AndroidConfig androidConfig = getAndroidConfig();
            ApnsConfig apnsConfig = getApnsConfig();
            Notification notification = Notification.builder()
                    .setTitle(notificationDTO.getTitle())
                    .setBody(notificationDTO.getMessage())
                    .setImage(notificationDTO.getImageUrl())
                    .build();
            if (notificationDTO.getTopicNames() != null && notificationDTO.getTopicNames().length > 0) {
                List<EntityUser> subscribedUsers = getSubscribedUsers(notificationDTO.getTopicNames());
                String topicNamesString;
                if (notificationDTO.getTopicNames().length == 1) {
                    topicNamesString = notificationDTO.getTopicNames()[0];
                    sendNotificationToTopic(topicNamesString, notification, androidConfig, apnsConfig);
                } else {
                    topicNamesString = String.join(",", notificationDTO.getTopicNames()); // Convert List<String> to comma-separated String
                    sendNotificationToMultipleTopics(notificationDTO.getTopicNames(), notification, androidConfig, apnsConfig, notificationDTO.getNotificationType());
                }
                saveNotificationsForUsers(notificationDTO, topicNamesString, subscribedUsers);
            } else if (tokens != null && !tokens.isEmpty()) {
                // Track if notification was successfully sent
                boolean isSent;
                // Send Notification to Multiple Device Tokens
                int batchSize = 500; // FCM allows max 500 tokens per request
                int successCount = 0;
                for (int i = 0; i < tokens.size(); i += batchSize) {
                    List<String> batchTokens = tokens
                            .subList(i, Math.min(tokens.size(), i + batchSize));

                    MulticastMessage message = MulticastMessage.builder()
                            .setAndroidConfig(androidConfig)
                            .setApnsConfig(apnsConfig)
                            .addAllTokens(batchTokens)  // Token-based notification
                            .setNotification(notification)
                            .putAllData(notificationDTO.getData())
                            .build();

                    BatchResponse batchResponse = FirebaseMessaging.getInstance().sendEachForMulticast(message);
                    System.out.println("Batch " + (i / batchSize + 1) + " sent. Success count: " + batchResponse.getSuccessCount());

                    successCount += batchResponse.getSuccessCount();
                }
                isSent = successCount > 0;
                // Only save notification if it was successfully sent
                if (isSent) {
                    saveNotificationSentByAdmin(notificationDTO, userIds);
                    System.out.println("Notification sent and saved successfully.");
                } else {
                    System.out.println("Notification failed to send.");
                }
            }


        } catch (Exception ex) {
            logger.error("Error sending FCM message: {}", ex.getMessage());
        }
    }

    private List<EntityUser> getSubscribedUsers(String[] topicNames) {
        List<Long> topicSubscribedUserIds = userTopicSubscriptionsRepository.findUserIdsByTopicNames(Arrays.asList(topicNames));
        return userRepository.findByUserIdIn(topicSubscribedUserIds);
    }

    private void sendNotificationToTopic(String topicName, Notification notification, AndroidConfig androidConfig, ApnsConfig apnsConfig)
            throws FirebaseMessagingException {
        Message message = Message.builder()
                .setAndroidConfig(androidConfig)
                .setApnsConfig(apnsConfig)
                .setTopic(topicName)
                .setNotification(notification)
                .build();

        String response = FirebaseMessaging.getInstance().send(message);
        System.out.println("Topic Notification Sent. Response: " + response);
    }

    private void sendNotificationToMultipleTopics(String[] topicNames, Notification notification,
                                                  AndroidConfig androidConfig, ApnsConfig apnsConfig, String notificationType)
            throws FirebaseMessagingException {
        String condition;
        if (notificationType.equals(NotificationType.NEW_WORKOUT_PLAN.name())) {
            condition = Arrays.stream(topicNames)
                    .map(topic -> "'" + topic + "' in topics")
                    .collect(Collectors.joining(" || "));
            System.out.println("Condition: " + condition);
        } else {
            condition = Arrays.stream(topicNames)
                    .map(topic -> "'" + topic + "' in topics")
                    .collect(Collectors.joining(" && "));
            System.out.println("Condition: " + condition);
        }

        Message message = Message.builder()
                .setAndroidConfig(androidConfig)
                .setApnsConfig(apnsConfig)
                .setCondition(condition)
                .setNotification(notification)
                .build();

        String response = FirebaseMessaging.getInstance().send(message);
        System.out.println("Multiple Topic Notification Sent. Response: " + response);
    }

    @Override
    public void sendNotificationToAppUser(EntityUser entityUser, NotificationDTO notificationDTO) {
        try {
            List<String> tokenList;
            List<EntityFcmToken> entityFcmTokenList = fcmTokenRepository.findByUserIdAndEntityUser_IsActive(entityUser.getUserId(), true);

            if (entityFcmTokenList != null && !entityFcmTokenList.isEmpty()) {
                tokenList = entityFcmTokenList.stream()
                        .map(EntityFcmToken::getFcmToken)
                        .filter(StringUtil::nonNullNonEmpty)
                        .distinct()
                        .collect(Collectors.toList());

                if (tokenList.isEmpty()) {
                    logger.info("No valid FCM tokens found for user: {}", entityUser.getUserId());
                }

                AndroidConfig androidConfig = getAndroidConfig();
                ApnsConfig apnsConfig = getApnsConfig();

                Notification notification = Notification.builder()
                        .setTitle(notificationDTO.getTitle())
                        .setBody(notificationDTO.getMessage())
                        .setImage(notificationDTO.getImageUrl())
                        .build();

                MulticastMessage message = MulticastMessage.builder()
                        .setAndroidConfig(androidConfig)
                        .setApnsConfig(apnsConfig)
                        .addAllTokens(tokenList)  // Token-based notification
                        .setNotification(notification)
                        .putAllData(notificationDTO.getData())
                        .build();

                BatchResponse response = FirebaseMessaging.getInstance().sendEachForMulticast(message);

                /** Save Notification Data **/
                NotificationType notificationType = NotificationType.valueOf(notificationDTO.getNotificationType());

                // Check if this is an exclusive content unlocked notification
                if (notificationType == NotificationType.EXCLUSIVE_CONTENT_UNLOCKED) {
                    // Get the audio ID from the notification data
                    String audioIdStr = notificationDTO.getData().get("audioId");
                    if (audioIdStr != null) {
                        try {
                            Long audioId = Long.parseLong(audioIdStr);
                            // Get the audio details
                            Optional<EntityAudio> audioOpt = audioRepository.findById(audioId);
                            if (audioOpt.isPresent()) {
                                // Save the notification with audio details
                                savePushNotificationWithAudioDetails(entityUser, false, notificationDTO.getTitle(),
                                        notificationDTO.getMessage(), notificationType, notificationDTO.getImageKey(), audioOpt.get());
                            } else {
                                // Audio not found, save without audio details
                                savePushNotification(entityUser, false, notificationDTO.getTitle(),
                                        notificationDTO.getMessage(), notificationType, notificationDTO.getImageKey());
                            }
                        } catch (NumberFormatException e) {
                            // Invalid audio ID, save without audio details
                            savePushNotification(entityUser, false, notificationDTO.getTitle(),
                                    notificationDTO.getMessage(), notificationType, notificationDTO.getImageKey());
                        }
                    } else {
                        // No audio ID in the notification data, save without audio details
                        savePushNotification(entityUser, false, notificationDTO.getTitle(),
                                notificationDTO.getMessage(), notificationType, notificationDTO.getImageKey());
                    }
                } else {
                    // Not an exclusive content unlocked notification, save without audio details
                    savePushNotification(entityUser, false, notificationDTO.getTitle(),
                            notificationDTO.getMessage(), notificationType, notificationDTO.getImageKey());
                }

                logger.info("Notification sent to user {}: {}", entityUser.getUserId(), response);
            }
        } catch (Exception ex) {
            logger.error("Error sending FCM message to user {}: {}", entityUser.getUserId(), ex.getMessage());
        }
    }

    @Override
    public NotificationDTO setWelcomeNotificationData() {
        Map<String, String> notificationInfo = new HashMap<>();
        NotificationDTO notificationDTO = new NotificationDTO();
        notificationDTO.setTitle("Welcome to Mind Body Warrior!");
        notificationDTO.setMessage("We are excited to have you on board! Explore amazing features and stay connected.");
        EntityNotificationImage entityNotificationImage = notificationImageRepository.findByNotificationType(NotificationType.WELCOME);
        if (entityNotificationImage != null) {
            notificationDTO.setImageKey(entityNotificationImage.getImage());
            String folderName = globalConfiguration.getAmazonS3().getNotificationImageFolderName();
            notificationDTO.setImageUrl(baseS3Url + folderName + entityNotificationImage.getImage());
        }
        notificationDTO.setNotificationType(NotificationType.WELCOME.name());
        notificationInfo.put("title", notificationDTO.getTitle());
        notificationInfo.put("message", notificationDTO.getMessage());
        notificationInfo.put("notificationType", notificationDTO.getNotificationType());
        notificationDTO.setData(notificationInfo);
        return notificationDTO;
    }


    public void savePushNotification(EntityUser entityUser, boolean isNotificationRead, String title, String message, NotificationType notificationType, String imageKey) {
        EntityNotification entityNotification = new EntityNotification();
        entityNotification.setTitle(title);
        entityNotification.setIsNotificationRead(isNotificationRead);
        entityNotification.setMessage(message);
        entityNotification.setEntityUser(entityUser);
        entityNotification.setUserId(entityUser.getUserId());
        entityNotification.setNotificationType(notificationType);
        entityNotification.setImage(imageKey);
        notificationRepository.save(entityNotification);

        chatMessagesRepository.save(generateChatMessageObject(notificationType.toString(), entityUser, entityNotification.getMessage(), entityNotification.getTitle(), imageKey, null));
    }

    /**
     * Save a push notification with audio details for exclusive content unlocked notifications
     *
     * @param entityUser The user to send the notification to
     * @param isNotificationRead Whether the notification has been read
     * @param title The notification title
     * @param message The notification message
     * @param notificationType The notification type
     * @param imageKey The image key for the notification
     * @param unlockedAudio The unlocked audio details
     */
    public void savePushNotificationWithAudioDetails(EntityUser entityUser, boolean isNotificationRead, String title, String message, NotificationType notificationType, String imageKey, EntityAudio unlockedAudio) {
        EntityNotification entityNotification = new EntityNotification();
        entityNotification.setTitle(title);
        entityNotification.setIsNotificationRead(isNotificationRead);
        entityNotification.setMessage(message);
        entityNotification.setEntityUser(entityUser);
        entityNotification.setUserId(entityUser.getUserId());
        entityNotification.setNotificationType(notificationType);
        entityNotification.setImage(imageKey);
        notificationRepository.save(entityNotification);

        // Create a chat message with the audio details
        EntityChatMessages chatMessage = generateChatMessageObject(notificationType.toString(), entityUser, entityNotification.getMessage(), entityNotification.getTitle(), imageKey, null);

        // If this is an exclusive content unlocked notification, add the audio details
        if (notificationType == NotificationType.EXCLUSIVE_CONTENT_UNLOCKED && unlockedAudio != null) {
            // Get the message as a ChatMessageDTO
            chatMessage.setIsNotification(false);
            ChatMessageDTO chatMessageDTO = new Gson().fromJson(chatMessage.getMessage(), ChatMessageDTO.class);

            // Create a ChatAudioDetailDTO with the unlocked audio details
            ChatAudioDetailDTO audioDetailDTO = new ChatAudioDetailDTO();
            audioDetailDTO.setAudioId(unlockedAudio.getAudioId());
            audioDetailDTO.setTitle(unlockedAudio.getTitle());
            audioDetailDTO.setAudioPlaylistType(unlockedAudio.getAudioPlaylistType().toString());

            // Set thumbnail image URL
            String thumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket()
                    + globalConfiguration.getAmazonS3().getUploadFolderName()
                    + globalConfiguration.getAmazonS3().getAudioThumbnailFolderName();
            audioDetailDTO.setThumbnailImage(thumbnailImagePath + unlockedAudio.getThumbnailImage());

            // Set audio file URL
            String audioFilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket()
                    + globalConfiguration.getAmazonS3().getUploadFolderName()
                    + globalConfiguration.getAmazonS3().getAudioFileFolderName();
            audioDetailDTO.setAudioFile(audioFilePath + unlockedAudio.getAudioFile());

            // Set audio playlist info if available
            if (unlockedAudio.getEntityAudioPlaylist() != null) {
                audioDetailDTO.setAudioPlaylistId(unlockedAudio.getAudioPlaylistId());
                audioDetailDTO.setAudioPlaylistName(unlockedAudio.getEntityAudioPlaylist().getAudioPlaylistName());
            }

            // Create a ChatMessageMusicDTO with the audio details
            ChatMessageMusicDTO chatMessageMusicDTO = new ChatMessageMusicDTO();
            chatMessageMusicDTO.setPrefixText("You've unlocked a new meditation:");
            chatMessageMusicDTO.setList(audioDetailDTO);
            chatMessageMusicDTO.setSuffixText("Enjoy this exclusive content!");

            // Convert the ChatMessageMusicDTO to a map
            Map<String, Object> musicMap = new HashMap<>();
            musicMap.put("prefixText", chatMessageMusicDTO.getPrefixText());
            musicMap.put("list", chatMessageMusicDTO.getList());
            musicMap.put("suffixText", chatMessageMusicDTO.getSuffixText());

            // Update the ChatMessageDTO with the music details
            chatMessageDTO.setMusic(musicMap);

            // Convert the ChatMessageDTO back to a JSON string
            String updatedMessage = new Gson().toJson(chatMessageDTO);

            // Update the chat message with the new message
            chatMessage.setMessage(updatedMessage);
        }

        chatMessagesRepository.save(chatMessage);
    }

    private void saveNotificationSentByAdmin(NotificationDTO notificationDTO, List<Long> userIds) {
        // Fetch users from DB based on userIds
        List<EntityUser> users = userRepository.findAllById(userIds);
        Map<Long, EntityUser> userMap = users.stream().collect(Collectors.toMap(EntityUser::getUserId, user -> user));

        // Create list of EntityNotification objects
        List<EntityNotification> notifications = userIds.stream().map(userId -> {
            EntityNotification entityNotification = new EntityNotification();
            entityNotification.setTitle(notificationDTO.getTitle());
            entityNotification.setMessage(notificationDTO.getMessage());
            entityNotification.setNotificationType(NotificationType.valueOf(notificationDTO.getNotificationType()));
            entityNotification.setEntityUser(userMap.get(userId));
            entityNotification.setUserId(userId);
            entityNotification.setImage(notificationDTO.getImageKey());
            return entityNotification;
        }).collect(Collectors.toList());

        // Save all notifications in batch
        notificationRepository.saveAll(notifications);
        List<EntityChatMessages> listChatMessages = notifications.stream()
                .map(notification -> generateChatMessageObject(notificationDTO.getNotificationType(), notification.getEntityUser(), notification.getMessage(), notification.getTitle(), notification.getImage(), notificationDTO.getAiChatCategory()))
                .collect(Collectors.toList());

        chatMessagesRepository.saveAll(listChatMessages);

    }


    private void saveTopicNotificationSentByAdmin(NotificationDTO notificationDTO, String topicName, EntityUser entityUser) {
        EntityNotification entityNotification = new EntityNotification();
        entityNotification.setTitle(notificationDTO.getTitle());
        entityNotification.setMessage(notificationDTO.getMessage());
        entityNotification.setNotificationType(NotificationType.valueOf(notificationDTO.getNotificationType()));
        entityNotification.setTopicName(topicName);
        entityNotification.setImage(notificationDTO.getImageKey());
        entityNotification.setEntityUser(entityUser);
        entityNotification.setUserId(entityUser.getUserId());
        notificationRepository.save(entityNotification);

        chatMessagesRepository.save(generateChatMessageObject(notificationDTO.getNotificationType(), entityUser, entityNotification.getMessage(),entityNotification.getTitle(), notificationDTO.getImageKey(), notificationDTO.getAiChatCategory()));

    }

    private EntityChatMessages generateChatMessageObject(String notificationType, EntityUser entityUser, String notificationMessage, String notificationTitle, String imageKey, String category) {
        Category categoryEnum = null;
        if(Objects.isNull(category)){
            categoryEnum = switch (NotificationType.valueOf(notificationType)) {
                case SUBSCRIPTION, BIRTHDAY, NEW_WORKOUT_PLAN, NONE, EXCLUSIVE_CONTENT_UNLOCKED  -> Category.NEWS_AND_UPDATES;
                case DAILY_READING -> Category.MY_DAILY_READING;
                default -> Category.EMPOWERMENT_COACH;
            };
        }else {
            categoryEnum = Category.valueOf(category);
        }

        ChatMessageMusicDTO chatMessageMusicDTO = new ChatMessageMusicDTO();
        chatMessageMusicDTO.setPrefixText("");
        chatMessageMusicDTO.setList(null);
        chatMessageMusicDTO.setSuffixText("");

        ChatMessageHoroscopeDTO chatMessageHoroscopeDTO = new ChatMessageHoroscopeDTO();
        chatMessageHoroscopeDTO.setPrediction(new HashMap<>());
        chatMessageHoroscopeDTO.setPredictionDate("");

        ChatMessageDTO chatMessageDTO = new ChatMessageDTO();
        chatMessageDTO.set_sender(true);
        if(notificationType.equals(NotificationType.EXCLUSIVE_CONTENT_UNLOCKED.toString())) {
            chatMessageDTO.setIntent("Music Recommendation");
            chatMessageDTO.set_sender(false);
        }else{
            chatMessageDTO.setIntent("Other");
        }
        chatMessageDTO.setExercise(new HashMap<>());
        chatMessageDTO.setGreetings("");
        chatMessageDTO.setZodiac_sign("");
        chatMessageDTO.setPartner_compatibility(new HashMap<>());
        chatMessageDTO.setMusic(chatMessageMusicDTO);
        chatMessageDTO.setHoroscope(chatMessageHoroscopeDTO);
        chatMessageDTO.setNotification_type(notificationType);
        chatMessageDTO.setNotification_title(notificationTitle);
        chatMessageDTO.setNormal_response(notificationMessage);

        String message = new Gson().toJson(chatMessageDTO);

        return EntityChatMessages.builder()
                .entityUser(entityUser)
                .userId(entityUser.getUserId())
                .message(message)
                .category(categoryEnum.toString())
                .isNotification(true)
                .messageFrom(Constant.SYSTEM)
                .image(imageKey)
                .historyMessage(notificationTitle)
                .build();

    }

    private void saveNotificationsForUsers(NotificationDTO notificationDTO, String topicNames, List<EntityUser> users) {
        users.forEach(user -> saveTopicNotificationSentByAdmin(notificationDTO, topicNames, user));
    }

    @Override
    public NotificationDTO setNewWorkoutPlanNotificationData(boolean isAddWorkoutPlan, Long workoutPlanId) {
        Map<String, String> notificationInfo = new HashMap<>();
        NotificationDTO notificationDTO = new NotificationDTO();
        if (isAddWorkoutPlan) {
            /** New Workout Plan Case **/
            notificationDTO.setTitle("New Workout Plan");
            notificationDTO.setMessage("Checkout new workout plan which has been added for your zodiac sign");
        } else {
            /** Edit Workout Plan Case **/
            notificationDTO.setTitle("Modified Workout Plan");
            notificationDTO.setMessage("Workout plan has been modified for your zodiac sign");
        }
        EntityNotificationImage entityNotificationImage = notificationImageRepository.findByNotificationType(NotificationType.NEW_WORKOUT_PLAN);
        if (entityNotificationImage != null) {
            notificationDTO.setImageKey(entityNotificationImage.getImage());
            String folderName = globalConfiguration.getAmazonS3().getNotificationImageFolderName();
            notificationDTO.setImageUrl(baseS3Url + folderName + entityNotificationImage.getImage());
        }
        notificationDTO.setNotificationType(NotificationType.NEW_WORKOUT_PLAN.name());
        notificationInfo.put("title", notificationDTO.getTitle());
        notificationInfo.put("message", notificationDTO.getMessage());
        notificationInfo.put("notificationType", notificationDTO.getNotificationType());
        notificationInfo.put("workoutPlanId", String.valueOf(workoutPlanId));
        notificationDTO.setData(notificationInfo);
        return notificationDTO;
    }

    @Override
    public NotificationDTO setExclusiveContentUnlockedNotificationData(boolean isReferrer, EntityAudio unlockedAudio) {
        Map<String, String> notificationInfo = new HashMap<>();
        NotificationDTO notificationDTO = new NotificationDTO();

        // Create ChatAudioDetailDTO for the unlocked audio
        ChatAudioDetailDTO audioDetailDTO = new ChatAudioDetailDTO();
        audioDetailDTO.setAudioId(unlockedAudio.getAudioId());
        audioDetailDTO.setTitle(unlockedAudio.getTitle());
        audioDetailDTO.setAudioPlaylistType(unlockedAudio.getAudioPlaylistType().toString());

        // Set thumbnail image URL
        String thumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket()
                + globalConfiguration.getAmazonS3().getUploadFolderName()
                + globalConfiguration.getAmazonS3().getAudioThumbnailFolderName();
        audioDetailDTO.setThumbnailImage(thumbnailImagePath + unlockedAudio.getThumbnailImage());

        // Set audio file URL
        String audioFilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket()
                + globalConfiguration.getAmazonS3().getUploadFolderName()
                + globalConfiguration.getAmazonS3().getAudioFileFolderName();
        audioDetailDTO.setAudioFile(audioFilePath + unlockedAudio.getAudioFile());

        // Set audio playlist info if available
        if (unlockedAudio.getEntityAudioPlaylist() != null) {
            audioDetailDTO.setAudioPlaylistId(unlockedAudio.getAudioPlaylistId());
            audioDetailDTO.setAudioPlaylistName(unlockedAudio.getEntityAudioPlaylist().getAudioPlaylistName());
        }

        // Convert audioDetailDTO to JSON string
        String audioDetailJson = new Gson().toJson(audioDetailDTO);

        if (isReferrer) {
            // Notification for the user whose referral code was used
            notificationDTO.setTitle("🎉 Amazing! Your Warrior Circle is Growing! 🌟");
            notificationDTO.setMessage("Wonderful news! Your influence has unlocked an incredible meditation for you: '" + unlockedAudio.getTitle() + "'. Your warrior spirit is inspiring others! ✨");
        } else {
            // Notification for the user who entered someone else's referral code
            notificationDTO.setTitle("✨ Welcome to the Warrior Circle! 🌟");
            notificationDTO.setMessage("Fantastic! You've connected with a fellow warrior and unlocked something special: '" + unlockedAudio.getTitle() + "'. Get ready for an amazing meditation journey! 🎵");
        }

        // Set notification image
        EntityNotificationImage entityNotificationImage = notificationImageRepository.findByNotificationType(NotificationType.EXCLUSIVE_CONTENT_UNLOCKED);
        if (entityNotificationImage != null) {
            notificationDTO.setImageKey(entityNotificationImage.getImage());
            String folderName = globalConfiguration.getAmazonS3().getNotificationImageFolderName();
            notificationDTO.setImageUrl(baseS3Url + folderName + entityNotificationImage.getImage());
        } else {
            // If no specific image for EXCLUSIVE_CONTENT_UNLOCKED, use the audio's thumbnail
            notificationDTO.setImageKey(unlockedAudio.getThumbnailImage());
            notificationDTO.setImageUrl(thumbnailImagePath + unlockedAudio.getThumbnailImage());
        }

        notificationDTO.setNotificationType(NotificationType.EXCLUSIVE_CONTENT_UNLOCKED.name());
        notificationInfo.put("title", notificationDTO.getTitle());
        notificationInfo.put("message", notificationDTO.getMessage());
        notificationInfo.put("notificationType", notificationDTO.getNotificationType());
        notificationDTO.setData(notificationInfo);

        return notificationDTO;
    }

}
