package com.mindbody.api.service.impl;

import com.mindbody.api.dto.cms.EditStaticPageReqDTO;
import com.mindbody.api.dto.cms.StaticPageResDTO;
import com.mindbody.api.enums.StaticPageType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.StaticPageMapper;
import com.mindbody.api.model.EntityStaticPage;
import com.mindbody.api.repository.StaticPageRepository;
import com.mindbody.api.service.StaticPageService;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class StaticPageServiceImpl implements StaticPageService {

    private final StaticPageRepository staticPageRepository;

    private final StaticPageMapper staticPageMapper;

    public StaticPageServiceImpl(StaticPageRepository staticPageRepository, StaticPageMapper staticPageMapper) {
        this.staticPageRepository = staticPageRepository;
        this.staticPageMapper = staticPageMapper;
    }

    @Override
    public void editStaticPage(EditStaticPageReqDTO editStaticPageReqDTO) {
        EntityStaticPage entityStaticPage = staticPageRepository.findByStaticPageIdAndIsDeleted(editStaticPageReqDTO.getStaticPageId(),false);
        if (Objects.isNull(entityStaticPage)) {
            throw new BusinessValidationException("static_page_not_found");
        }
        entityStaticPage.setStaticPageContent(editStaticPageReqDTO.getStaticPageContent());
        staticPageRepository.save(entityStaticPage);
    }

    @Override
    public StaticPageResDTO viewStaticPage(String staticPageType) {
        StaticPageType staticPageTypeEnum;
        try {
            staticPageTypeEnum = StaticPageType.valueOf(staticPageType);
        } catch (IllegalArgumentException e) {
            throw new BusinessValidationException("invalid_static_page_type");
        }
        EntityStaticPage entityStaticPage = staticPageRepository.findByStaticPageTypeAndIsDeleted(staticPageTypeEnum,false);
        if (Objects.isNull(entityStaticPage)) {
            throw new BusinessValidationException("static_page_not_found");
        }
        return staticPageMapper.toDTO(entityStaticPage);
    }

}
