package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.*;
import com.mindbody.api.dto.notification.NotificationDTO;
import com.mindbody.api.enums.NotificationType;
import com.mindbody.api.enums.RewardType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.*;
import com.mindbody.api.repository.*;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.NotificationService;
import com.mindbody.api.service.UserRedeemableItemService;
import com.mindbody.api.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserRedeemableItemServiceImpl extends BaseService implements UserRedeemableItemService {

    private final UserRepository userRepository;
    private final AudioRepository audioRepository;
    private final RewardRepository rewardRepository;
    private final UserRedeemedAudioRepository userRedeemedAudioRepository;
    private final UserRedeemedRewardRepository userRedeemedRewardRepository;
    private final UserPointsTrackerRepository userPointsTrackerRepository;
    private final GlobalConfiguration globalConfiguration;
    private final SecureRandom secureRandom;
    private final ZodiacWallpaperBundleRepository zodiacWallpaperBundleRepository;
    private final NotificationService notificationService;

    public UserRedeemableItemServiceImpl(
            MessageService messageService,
            UserRepository userRepository,
            AudioRepository audioRepository,
            RewardRepository rewardRepository,
            UserRedeemedAudioRepository userRedeemedAudioRepository,
            UserRedeemedRewardRepository userRedeemedRewardRepository,
            UserPointsTrackerRepository userPointsTrackerRepository,
            GlobalConfiguration globalConfiguration,
            ZodiacWallpaperBundleRepository zodiacWallpaperBundleRepository,
            NotificationService notificationService) {
        super(messageService);
        this.userRepository = userRepository;
        this.audioRepository = audioRepository;
        this.rewardRepository = rewardRepository;
        this.userRedeemedAudioRepository = userRedeemedAudioRepository;
        this.userRedeemedRewardRepository = userRedeemedRewardRepository;
        this.userPointsTrackerRepository = userPointsTrackerRepository;
        this.globalConfiguration = globalConfiguration;
        this.secureRandom = new SecureRandom();
        this.zodiacWallpaperBundleRepository = zodiacWallpaperBundleRepository;
        this.notificationService = notificationService;
    }

    @Override
    public RedeemableItemsWithPointsDTO getRedeemableItemsWithStatus(
            Long userId,
            String queryToSearch,
            String itemType,
            Pageable pageable) {
        
        // Validate user and get points
        EntityUser entityUser = validateUser(userId);
        EntityUserPointsTracker pointsTracker = validateUserPoints(userId);

        List<RedeemableItemDTO> items = new ArrayList<>();
        long totalCount = 0;

        // Get redeemable audios if requested
        if (itemType == null || "AUDIO".equals(itemType)) {
            List<EntityAudio> redeemableAudios = audioRepository.findRedeemableAudios(
                    queryToSearch,
                    pageable,
                    true,
                    true,
                    false
            );
            items.addAll(mapAudiosToRedeemableItems(redeemableAudios, entityUser, pointsTracker));
            totalCount += redeemableAudios.size();
        }

        // Get rewards if requested
        if (itemType == null || "REWARD".equals(itemType)) {
            List<EntityReward> rewards = rewardRepository.findAvailableRewards(
                    queryToSearch,
                    true,
                    false,
                    pageable
            );
            items.addAll(mapRewardsToRedeemableItems(rewards, entityUser, pointsTracker));
            totalCount += rewards.size();
        }

        SearchResultDTO<RedeemableItemDTO> searchResult = new SearchResultDTO<>(items, totalCount, pageable.getPageSize());
        return new RedeemableItemsWithPointsDTO(searchResult, pointsTracker.getTotalPoints().intValue());
    }

    private List<RedeemableItemDTO> mapAudiosToRedeemableItems(
            List<EntityAudio> audios,
            EntityUser user,
            EntityUserPointsTracker pointsTracker) {
        
        String audioFilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getAudioFileFolderName();
        
        String thumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getAudioThumbnailFolderName();

        return audios.stream()
                .map(audio -> {
                    RedeemableItemDTO dto = new RedeemableItemDTO();
                    dto.setItemId(audio.getAudioId());
                    dto.setTitle(audio.getTitle());
                    dto.setDescription(audio.getDescription());
                    dto.setThumbnailImage(StringUtil.nonNullNonEmpty(audio.getThumbnailImage()) ?
                            thumbnailImagePath + audio.getThumbnailImage() : null);
                    dto.setPointsRequired(audio.getPoints());
                    dto.setActive(audio.isActive());
                    dto.setCreatedAt(audio.getCreatedAt());
                    dto.setUpdatedAt(audio.getUpdatedAt());
                    dto.setItemType("AUDIO");

                    // Audio specific fields
                    dto.setAudioPlaylistType(audio.getAudioPlaylistType());
                    dto.setAudioFile(StringUtil.nonNullNonEmpty(audio.getAudioFile()) ?
                            audioFilePath + audio.getAudioFile() : null);
                    dto.setAudioPlaylistId(audio.getAudioPlaylistId());
                    dto.setAudioPlaylistName(audio.getEntityAudioPlaylist().getAudioPlaylistName());

                    // Check redemption status
                    boolean isRedeemed = userRedeemedAudioRepository.isAudioRedeemedByUser(
                            user.getUserId(),
                            audio.getAudioId(),
                            true,
                            false
                    );
                    dto.setRedeemed(isRedeemed);
                    dto.setCanRedeem(!isRedeemed && pointsTracker.getTotalPoints() >= audio.getPoints());

                    return dto;
                })
                .collect(Collectors.toList());
    }

    private List<RedeemableItemDTO> mapRewardsToRedeemableItems(
            List<EntityReward> rewards,
            EntityUser user,
            EntityUserPointsTracker pointsTracker) {
        
        String thumbnailPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getRewardThumbnailFolderName();

        String rewardItemPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getRewardItemAssetsFolderName();

        return rewards.stream()
                .map(reward -> {
                    RedeemableItemDTO dto = new RedeemableItemDTO();
                    dto.setItemId(reward.getRewardId());
                    dto.setTitle(reward.getTitle());
                    dto.setDescription(reward.getDescription());
                    dto.setThumbnailImage(reward.getThumbnailImage() != null ?
                            thumbnailPath + reward.getThumbnailImage() : null);
                    dto.setPointsRequired(reward.getPointsRequired());
                    dto.setActive(reward.isActive());
                    dto.setCreatedAt(reward.getCreatedAt());
                    dto.setUpdatedAt(reward.getUpdatedAt());
                    dto.setItemType("REWARD");

                    // Reward specific fields
                    dto.setRewardType(reward.getRewardType());
                    if (!reward.getRewardType().equals(RewardType.TEN_PERCENT_DISCOUNT_MBW_STORE)) {
                        dto.setRewardMetadata(reward.getRewardMetadata() != null ? rewardItemPath + reward.getRewardMetadata() : null);
                    }else{
                        dto.setRewardMetadata(reward.getRewardMetadata());
                    }

                    // Check redemption status
                    boolean isRedeemed = userRedeemedRewardRepository.isRewardRedeemedByUser(
                            user.getUserId(),
                            reward.getRewardId(),
                            true,
                            false
                    );
                    dto.setRedeemed(isRedeemed);
                    dto.setCanRedeem(!isRedeemed && pointsTracker.getTotalPoints() >= reward.getPointsRequired());

                    // Add zodiac wallpapers if this is a zodiac wallpaper reward
                    if (reward.getRewardType() == RewardType.ZODIAC_WALLPAPER) {
                        List<EntityZodiacWallpaperBundle> wallpapers = zodiacWallpaperBundleRepository
                                .findByRewardIdAndIsActiveAndIsDeleted(reward.getRewardId(), true, false);
                        
                        if (!wallpapers.isEmpty()) {
                            dto.setZodiacWallpapers(wallpapers.stream()
                                    .map(wallpaper -> {
                                        Map<String, String> wallpaperInfo = new HashMap<>();
                                        wallpaperInfo.put("zodiacSign", wallpaper.getZodiacSign().name());
                                        wallpaperInfo.put("wallpaperUrl", rewardItemPath + wallpaper.getWallpaperUrl());
                                        return wallpaperInfo;
                                    })
                                    .collect(Collectors.toList()));
                        }
                    }

                    return dto;
                })
                .collect(Collectors.toList());
    }

    private EntityUser validateUser(Long userId) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found");
        }
        return entityUser;
    }

    private EntityUserPointsTracker validateUserPoints(Long userId) {
        return userPointsTrackerRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false)
                .orElseThrow(() -> new BusinessValidationException("user_points_not_found"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RedeemItemResDTO redeemItem(RedeemItemReqDTO redeemItemReqDTO) {
        EntityUser entityUser = validateUser(redeemItemReqDTO.getUserId());
        EntityUserPointsTracker pointsTracker = validateUserPoints(redeemItemReqDTO.getUserId());

        if ("AUDIO".equals(redeemItemReqDTO.getItemType())) {
            return redeemAudio(entityUser, pointsTracker, redeemItemReqDTO.getItemId());
        } else if ("REWARD".equals(redeemItemReqDTO.getItemType())) {
            return redeemReward(entityUser, pointsTracker, redeemItemReqDTO.getItemId());
        } else {
            throw new BusinessValidationException("invalid_item_type");
        }
    }

    private RedeemItemResDTO redeemAudio(
            EntityUser entityUser,
            EntityUserPointsTracker pointsTracker,
            Long audioId) {
        
        EntityAudio entityAudio = validateAudio(audioId);

        // Check if audio is already redeemed
        if (userRedeemedAudioRepository.isAudioRedeemedByUser(entityUser.getUserId(), audioId, true, false)) {
            throw new BusinessValidationException("audio_already_redeemed");
        }

        // Check if audio is redeemable
        if (!entityAudio.isRedeemable()) {
            throw new BusinessValidationException("audio_not_redeemable");
        }

        // Check if user has enough points
        if (pointsTracker.getTotalPoints() < entityAudio.getPoints()) {
            throw new BusinessValidationException("insufficient_points");
        }

        // Deduct points and save
        pointsTracker.setTotalPoints(pointsTracker.getTotalPoints() - entityAudio.getPoints());
        userPointsTrackerRepository.save(pointsTracker);

        // Create redeemed audio record
        EntityUserRedeemedAudio redeemedAudio = new EntityUserRedeemedAudio();
        redeemedAudio.setEntityUser(entityUser);
        redeemedAudio.setUserId(entityUser.getUserId());
        redeemedAudio.setEntityAudio(entityAudio);
        redeemedAudio.setAudioId(audioId);
        redeemedAudio.setPointsSpent(entityAudio.getPoints());
        userRedeemedAudioRepository.save(redeemedAudio);

        // Get file paths
        String audioFilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getAudioFileFolderName();
        
        String thumbnailPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getAudioThumbnailFolderName();

        // Create response with complete audio information
        RedeemItemResDTO response = new RedeemItemResDTO(
                entityAudio.getTitle(),
                pointsTracker.getTotalPoints().intValue(),
                "AUDIO",
                null,
                null,
                null
        );

        // Add audio-specific information
        response.setAudioId(entityAudio.getAudioId());
        response.setDescription(entityAudio.getDescription());
        response.setThumbnailImage(entityAudio.getThumbnailImage() != null ? thumbnailPath + entityAudio.getThumbnailImage() : null);
        response.setAudioPlaylistType(entityAudio.getAudioPlaylistType());
        response.setAudioFile(entityAudio.getAudioFile() != null ? audioFilePath + entityAudio.getAudioFile() : null);
        response.setAudioPlaylistId(entityAudio.getAudioPlaylistId());
        response.setAudioPlaylistName(entityAudio.getEntityAudioPlaylist().getAudioPlaylistName());
        response.setPointsSpent(entityAudio.getPoints());
        response.setCreatedAt(entityAudio.getCreatedAt());
        response.setUpdatedAt(entityAudio.getUpdatedAt());

        // Get zodiac signs if any
        if (entityAudio.getEntityAudioZodiacSigns() != null && !entityAudio.getEntityAudioZodiacSigns().isEmpty()) {
            response.setZodiacSigns(entityAudio.getEntityAudioZodiacSigns().stream()
                    .map(sign -> sign.getZodiacSign().name())
                    .collect(Collectors.toList()));
        }

        NotificationDTO audioRedeemedNotification = new NotificationDTO();
        audioRedeemedNotification.setTitle("Audio Redeemed Successfully! 🎵");
        audioRedeemedNotification.setMessage("Congratulations! You've successfully redeemed '" + entityAudio.getTitle() + "'. Enjoy your new audio content!");
        audioRedeemedNotification.setNotificationType(NotificationType.REDEEM_ITEM.toString());

        Map<String, String> notificationInfo = new HashMap<>();
        notificationInfo.put("title", audioRedeemedNotification.getTitle());
        notificationInfo.put("message", audioRedeemedNotification.getMessage());
        notificationInfo.put("notificationType", NotificationType.REDEEM_ITEM.name());
        audioRedeemedNotification.setData(notificationInfo);

        notificationService.sendNotificationToAppUser(entityUser, audioRedeemedNotification);

        return response;
    }

    private RedeemItemResDTO redeemReward(
            EntityUser entityUser,
            EntityUserPointsTracker pointsTracker,
            Long rewardId) {
        
        EntityReward reward = validateReward(rewardId);

        // Check if already redeemed
        if (userRedeemedRewardRepository.isRewardRedeemedByUser(entityUser.getUserId(), rewardId, true, false)) {
            throw new BusinessValidationException("reward_already_redeemed");
        }

        // Check if user has enough points
        if (pointsTracker.getTotalPoints() < reward.getPointsRequired()) {
            throw new BusinessValidationException("insufficient_points");
        }

        String couponCode = null;
        List<Map<String, String>> zodiacWallpapers = null;

        String rewardItemPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getRewardItemAssetsFolderName();

        // Handle different reward types
        if (reward.getRewardType() == RewardType.TEN_PERCENT_DISCOUNT_MBW_STORE) {
            couponCode = generateCouponCode();
        } else if (reward.getRewardType() == RewardType.ZODIAC_WALLPAPER) {
            // Get all wallpapers in the bundle
            List<EntityZodiacWallpaperBundle> wallpapers = zodiacWallpaperBundleRepository.findByRewardIdAndIsActiveAndIsDeleted(
                    rewardId, true, false);
            
            if (wallpapers.isEmpty()) {
                throw new BusinessValidationException("no_wallpapers_found");
            }

            // Convert wallpapers to response format with proper S3 paths
            zodiacWallpapers = wallpapers.stream()
                    .map(wallpaper -> {
                        Map<String, String> wallpaperInfo = new HashMap<>();
                        wallpaperInfo.put("zodiacSign", wallpaper.getZodiacSign().name());
                        wallpaperInfo.put("wallpaperUrl", rewardItemPath + wallpaper.getWallpaperUrl());
                        return wallpaperInfo;
                    })
                    .collect(Collectors.toList());
        } else if (reward.getRewardType() == RewardType.LINDA_EBOOK) {
            // For Linda's ebook, append the S3 path to the ebook URL
            if (reward.getRewardMetadata() != null) {
                reward.setRewardMetadata(rewardItemPath + reward.getRewardMetadata());
            }
        }

        // Create redemption record
        EntityUserRedeemedReward redeemedReward = new EntityUserRedeemedReward();
        redeemedReward.setUserId(entityUser.getUserId());
        redeemedReward.setRewardId(rewardId);
        redeemedReward.setPointsSpent(reward.getPointsRequired());
        redeemedReward.setRewardMetadata(couponCode);
        userRedeemedRewardRepository.save(redeemedReward);

        // Update user points
        pointsTracker.setTotalPoints(pointsTracker.getTotalPoints() - reward.getPointsRequired());
        userPointsTrackerRepository.save(pointsTracker);

        // Send notification to user about successful reward redemption
        NotificationDTO rewardRedeemedNotification = new NotificationDTO();
        rewardRedeemedNotification.setTitle("Reward Redeemed Successfully! 🎁");

        // Customize message based on reward type
        String message = switch (reward.getRewardType()) {
            case TEN_PERCENT_DISCOUNT_MBW_STORE ->
                    String.format("Congratulations! You've successfully redeemed '%s'. Your discount code is: %s",
                            reward.getTitle(), couponCode);
            case ZODIAC_WALLPAPER ->
                    String.format("Congratulations! You've successfully redeemed '%s'. Your zodiac wallpapers are ready to download!",
                            reward.getTitle());
            case LINDA_EBOOK ->
                    String.format("Congratulations! You've successfully redeemed '%s'. Your ebook is ready to read!",
                            reward.getTitle());
            default -> String.format("Congratulations! You've successfully redeemed '%s'. Enjoy your reward!",
                    reward.getTitle());
        };
        rewardRedeemedNotification.setMessage(message);
        rewardRedeemedNotification.setNotificationType(NotificationType.REDEEM_ITEM.toString());

        Map<String, String> notificationInfo = new HashMap<>();
        notificationInfo.put("title", rewardRedeemedNotification.getTitle());
        notificationInfo.put("message", rewardRedeemedNotification.getMessage());
        notificationInfo.put("notificationType", NotificationType.REDEEM_ITEM.name());
        rewardRedeemedNotification.setData(notificationInfo);

        notificationService.sendNotificationToAppUser(entityUser, rewardRedeemedNotification);

        // Get reward thumbnail path
        String thumbnailPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getRewardThumbnailFolderName();

        // Create complete response with all reward information
        RedeemItemResDTO response = new RedeemItemResDTO(
                reward.getTitle(),
                pointsTracker.getTotalPoints().intValue(),
                "REWARD",
                couponCode,
                reward.getRewardMetadata(),
                zodiacWallpapers
        );

        // Add additional reward information
        response.setRewardId(reward.getRewardId());
        response.setDescription(reward.getDescription());
        response.setThumbnailImage(reward.getThumbnailImage() != null ? thumbnailPath + reward.getThumbnailImage() : null);
        response.setPointsSpent(reward.getPointsRequired());
        response.setRewardType(reward.getRewardType());
        response.setCreatedAt(reward.getCreatedAt());
        response.setUpdatedAt(reward.getUpdatedAt());

        return response;
    }

    private EntityAudio validateAudio(Long audioId) {
        EntityAudio entityAudio = audioRepository.findByAudioIdAndIsActiveAndIsDeleted(audioId, true, false);
        if (Objects.isNull(entityAudio)) {
            throw new BusinessValidationException("audio_not_found");
        }
        return entityAudio;
    }

    private EntityReward validateReward(Long rewardId) {
        return rewardRepository.findByRewardIdAndIsActiveAndIsDeleted(rewardId, true, false)
                .orElseThrow(() -> new BusinessValidationException("reward_not_found"));
    }

    private String generateCouponCode() {
        String couponCode;
        int maxAttempts = 10; // Maximum attempts to generate a unique code
        int attempts = 0;

        do {
            StringBuilder code = new StringBuilder();
            for (int i = 0; i < 9; i++) {
                code.append(secureRandom.nextInt(10));
            }
            couponCode = code.toString();
            attempts++;

            // Break if we found a unique code or reached max attempts
            if (!userRedeemedRewardRepository.existsByCouponCode(couponCode) || attempts >= maxAttempts) {
                break;
            }
        } while (true);

        // If we couldn't generate a unique code after max attempts, throw an exception
        if (userRedeemedRewardRepository.existsByCouponCode(couponCode)) {
            throw new BusinessValidationException("failed_to_generate_unique_coupon");
        }

        return couponCode;
    }
} 