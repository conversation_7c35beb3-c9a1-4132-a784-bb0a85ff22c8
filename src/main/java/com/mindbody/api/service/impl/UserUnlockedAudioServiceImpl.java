package com.mindbody.api.service.impl;

import com.mindbody.api.dto.ExclusiveAudioStatusDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.EntityAudio;
import com.mindbody.api.model.EntityAudioZodiacSign;
import com.mindbody.api.model.EntityFavoriteAudio;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.model.EntityUserUnlockedAudio;
import com.mindbody.api.repository.AudioRepository;
import com.mindbody.api.repository.AudioZodiacSignRepository;
import com.mindbody.api.repository.FavoriteAudioRepository;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.repository.UserUnlockedAudioRepository;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.UserUnlockedAudioService;
import com.mindbody.api.config.GlobalConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Implementation of UserUnlockedAudioService
 */
@Service
@Slf4j
public class UserUnlockedAudioServiceImpl extends BaseService implements UserUnlockedAudioService {

    private final UserUnlockedAudioRepository userUnlockedAudioRepository;
    private final UserRepository userRepository;
    private final AudioRepository audioRepository;
    private final AudioZodiacSignRepository audioZodiacSignRepository;
    private final FavoriteAudioRepository favoriteAudioRepository;
    private final GlobalConfiguration globalConfiguration;

    /**
     * Constructor
     *
     * @param messageService              MessageService
     * @param userUnlockedAudioRepository UserUnlockedAudioRepository
     * @param userRepository              UserRepository
     * @param audioRepository             AudioRepository
     * @param audioZodiacSignRepository   AudioZodiacSignRepository
     * @param favoriteAudioRepository     FavoriteAudioRepository
     * @param globalConfiguration         GlobalConfiguration
     */
    public UserUnlockedAudioServiceImpl(MessageService messageService,
                                        UserUnlockedAudioRepository userUnlockedAudioRepository,
                                        UserRepository userRepository,
                                        AudioRepository audioRepository,
                                        AudioZodiacSignRepository audioZodiacSignRepository,
                                        FavoriteAudioRepository favoriteAudioRepository,
                                        GlobalConfiguration globalConfiguration) {
        super(messageService);
        this.userUnlockedAudioRepository = userUnlockedAudioRepository;
        this.userRepository = userRepository;
        this.audioRepository = audioRepository;
        this.audioZodiacSignRepository = audioZodiacSignRepository;
        this.favoriteAudioRepository = favoriteAudioRepository;
        this.globalConfiguration = globalConfiguration;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public EntityUserUnlockedAudio unlockAudio(Long userId, Long audioId) {
        // Validate user exists
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        // Validate audio exists
        EntityAudio entityAudio = audioRepository.findByAudioIdAndIsActiveAndIsDeleted(audioId, true, false);
        if (Objects.isNull(entityAudio)) {
            throw new BusinessValidationException("audio_not_found_error");
        }

        // Check if already unlocked
        Optional<EntityUserUnlockedAudio> existingUnlock = userUnlockedAudioRepository
                .findByUserIdAndAudioIdAndIsActiveAndIsDeleted(userId, audioId, true, false);

        if (existingUnlock.isPresent()) {
            EntityUserUnlockedAudio unlockedAudio = existingUnlock.get();
            if (!unlockedAudio.isUnlocked()) {
                unlockedAudio.setUnlocked(true);
                return userUnlockedAudioRepository.save(unlockedAudio);
            }
            return unlockedAudio;
        }

        // Create new unlocked audio record
        EntityUserUnlockedAudio userUnlockedAudio = new EntityUserUnlockedAudio();
        userUnlockedAudio.setEntityUser(entityUser);
        userUnlockedAudio.setUserId(userId);
        userUnlockedAudio.setEntityAudio(entityAudio);
        userUnlockedAudio.setAudioId(audioId);
        userUnlockedAudio.setUnlocked(true);
        userUnlockedAudio.setActive(true);
        userUnlockedAudio.setDeleted(false);

        return userUnlockedAudioRepository.save(userUnlockedAudio);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void lockAudio(Long userId, Long audioId) {
        Optional<EntityUserUnlockedAudio> existingUnlock = userUnlockedAudioRepository
                .findByUserIdAndAudioIdAndIsActiveAndIsDeleted(userId, audioId, true, false);

        if (existingUnlock.isPresent()) {
            EntityUserUnlockedAudio unlockedAudio = existingUnlock.get();
            unlockedAudio.setUnlocked(false);
            userUnlockedAudioRepository.save(unlockedAudio);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isAudioUnlocked(Long userId, Long audioId) {
        return userUnlockedAudioRepository.isAudioUnlockedByUser(userId, audioId, true, false);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<EntityUserUnlockedAudio> getAllUnlockedAudios(Long userId) {
        return userUnlockedAudioRepository.findAllByUserIdAndIsActiveAndIsDeleted(userId, true, false);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public long countUnlockedAudios(Long userId) {
        return userUnlockedAudioRepository.countByUserIdAndIsUnlockedAndIsActiveAndIsDeleted(
                userId, true, true, false);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SearchResultDTO<ExclusiveAudioStatusDTO> getExclusiveAudiosWithStatus(Long userId, String queryToSearch, Pageable pageable) {
        // Validate user exists
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        // Get all exclusive audios
        List<EntityAudio> exclusiveAudios = audioRepository.findExclusiveAudios(
                queryToSearch, pageable, true, true, false);

        long totalCount = audioRepository.countByIsExclusiveAndIsActiveAndIsDeleted(true, true, false);

        // Get paths for audio files and thumbnails
        String audioFilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getAudioFileFolderName();

        String thumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getAudioThumbnailFolderName();

        // Convert to DTOs with unlock status
        List<ExclusiveAudioStatusDTO> result = convertToExclusiveAudioStatusDTOs(userId, exclusiveAudios, audioFilePath, thumbnailImagePath);

        return new SearchResultDTO<>(result, totalCount, 10);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SearchResultDTO<ExclusiveAudioStatusDTO> getLockedExclusiveAudios(Long userId, String queryToSearch, Pageable pageable) {
        // Validate user exists
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        // Get all exclusive audios
        List<EntityAudio> exclusiveAudios = audioRepository.findExclusiveAudios(
                queryToSearch, pageable, true, true, false);

        // Get paths for audio files and thumbnails
        String audioFilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getAudioFileFolderName();

        String thumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getAudioThumbnailFolderName();

        // Convert to DTOs with unlock status
        List<ExclusiveAudioStatusDTO> allAudios = convertToExclusiveAudioStatusDTOs(userId, exclusiveAudios, audioFilePath, thumbnailImagePath);

        // Filter to only locked audios
        List<ExclusiveAudioStatusDTO> lockedAudios = allAudios.stream()
                .filter(audio -> !audio.isUnlocked())
                .collect(Collectors.toList());

        // Count total locked exclusive audios
        long totalCount = lockedAudios.size();

        return new SearchResultDTO<>(lockedAudios, totalCount, 10);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SearchResultDTO<ExclusiveAudioStatusDTO> getUnlockedExclusiveAudios(Long userId, String queryToSearch, Pageable pageable) {
        // Validate user exists
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        // Get all exclusive audios
        List<EntityAudio> exclusiveAudios = audioRepository.findExclusiveAudios(
                queryToSearch, pageable, true, true, false);

        // Get paths for audio files and thumbnails
        String audioFilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getAudioFileFolderName();

        String thumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getAudioThumbnailFolderName();

        // Convert to DTOs with unlock status
        List<ExclusiveAudioStatusDTO> allAudios = convertToExclusiveAudioStatusDTOs(userId, exclusiveAudios, audioFilePath, thumbnailImagePath);

        // Filter to only unlocked audios
        List<ExclusiveAudioStatusDTO> unlockedAudios = allAudios.stream()
                .filter(ExclusiveAudioStatusDTO::isUnlocked)
                .collect(Collectors.toList());

        // Count total unlocked exclusive audios
        long totalCount = unlockedAudios.size();

        return new SearchResultDTO<>(unlockedAudios, totalCount, 10);
    }

    /**
     * Helper method to convert EntityAudio list to ExclusiveAudioStatusDTO list with unlock status
     *
     * @param userId User ID
     * @param audios List of EntityAudio
     * @param audioFilePath Path to audio files
     * @param thumbnailImagePath Path to thumbnail images
     * @return List of ExclusiveAudioStatusDTO
     */
    private List<ExclusiveAudioStatusDTO> convertToExclusiveAudioStatusDTOs(Long userId, List<EntityAudio> audios,
                                                                          String audioFilePath, String thumbnailImagePath) {
        List<ExclusiveAudioStatusDTO> result = new ArrayList<>();

        // Get all audio IDs
        List<Long> audioIds = audios.stream()
                .map(EntityAudio::getAudioId)
                .collect(Collectors.toList());

        // Create a map of audio ID to unlock status
        Map<Long, Boolean> unlockStatusMap = new HashMap<>();
        for (Long audioId : audioIds) {
            unlockStatusMap.put(audioId, isAudioUnlocked(userId, audioId));
        }

        // Create a map of audio ID to favorite status
        Map<Long, Boolean> favoriteStatusMap = new HashMap<>();
        if (favoriteAudioRepository != null) {
            for (Long audioId : audioIds) {
                EntityFavoriteAudio favoriteAudio = favoriteAudioRepository.findByUserIdAndAudioIdAndIsActiveAndIsDeleted(
                        userId, audioId, true, false);
                favoriteStatusMap.put(audioId, favoriteAudio != null && favoriteAudio.isFavorite());
            }
        }

        // Get zodiac signs for all audios
        Map<Long, List<ZodiacSignType>> zodiacSignsMap = new HashMap<>();
        if (audioZodiacSignRepository != null) {
            for (Long audioId : audioIds) {
                List<EntityAudioZodiacSign> zodiacSigns = audioZodiacSignRepository.findByEntityAudio_AudioIdAndZodiacSignIn(
                        audioId, List.of(ZodiacSignType.values()));

                List<ZodiacSignType> signs = zodiacSigns.stream()
                        .map(EntityAudioZodiacSign::getZodiacSign)
                        .collect(Collectors.toList());

                zodiacSignsMap.put(audioId, signs);
            }
        }

        // Convert each audio to DTO
        for (EntityAudio audio : audios) {
            ExclusiveAudioStatusDTO dto = new ExclusiveAudioStatusDTO(
                    audio.getAudioId(),
                    audio.getTitle(),
                    audio.getDescription(),
                    audio.getAudioPlaylistType(),
                    thumbnailImagePath + audio.getThumbnailImage(),
                    audioFilePath + audio.getAudioFile(),
                    audio.getAudioPlaylistId(),
                    getAudioPlaylistName(audio),
                    audio.getCreatedAt(),
                    audio.getUpdatedAt(),
                    favoriteStatusMap.getOrDefault(audio.getAudioId(), false),
                    unlockStatusMap.getOrDefault(audio.getAudioId(), false),
                    audio.isDeleted(),
                    audio.isActive()
            );

            // Set zodiac signs if available
            if (zodiacSignsMap.containsKey(audio.getAudioId())) {
                dto.setZodiacSigns(zodiacSignsMap.get(audio.getAudioId()));
            }

            result.add(dto);
        }

        return result;
    }

    /**
     * Helper method to safely get the audio playlist name
     *
     * @param audio The EntityAudio
     * @return The audio playlist name or null if not available
     */
    private String getAudioPlaylistName(EntityAudio audio) {
        try {
            return (audio.getEntityAudioPlaylist() != null) ? audio.getEntityAudioPlaylist().getAudioPlaylistName() : null;
        } catch (Exception e) {
            // Handle LazyInitializationException or any other exception
            log.error("Error getting audio playlist name for audio ID {}: {}", audio.getAudioId(), e.getMessage());
            return null;
        }
    }
}
