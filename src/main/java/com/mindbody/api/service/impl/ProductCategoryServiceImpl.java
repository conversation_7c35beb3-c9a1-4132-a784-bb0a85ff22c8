package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.UploadedFileNameResponseDTO;
import com.mindbody.api.dto.cms.AddProductCategoryReqDTO;
import com.mindbody.api.dto.cms.EditProductCategoryReqDTO;
import com.mindbody.api.dto.cms.ProductCategoryResDTO;
import com.mindbody.api.enums.ImageType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.EntityProductCategory;
import com.mindbody.api.repository.ProductCategoryRepository;
import com.mindbody.api.service.AWSS3Service;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.ProductCategoryService;
import com.mindbody.api.util.StringUtil;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Service
public class ProductCategoryServiceImpl extends BaseService implements ProductCategoryService {

    private final ProductCategoryRepository productCategoryRepository;

    private final AWSS3Service awss3Service;

    private final GlobalConfiguration globalConfiguration;

    public ProductCategoryServiceImpl(MessageService messageService, ProductCategoryRepository productCategoryRepository, AWSS3Service awss3Service, GlobalConfiguration globalConfiguration) {
        super(messageService);
        this.productCategoryRepository = productCategoryRepository;
        this.awss3Service = awss3Service;
        this.globalConfiguration = globalConfiguration;
    }

    @Override
    public void addProductCategory(AddProductCategoryReqDTO addProductCategoryReqDTO) {
        if (productCategoryRepository.existsByProductCategoryNameAndIsDeleted(addProductCategoryReqDTO.getProductCategoryName().trim(), false)) {
            throw new BusinessValidationException("product_category_already_exists");
        }
        if (Objects.isNull(addProductCategoryReqDTO.getProductCategoryImage())) {
            throw new BusinessValidationException("product_category_image_required");
        }
        EntityProductCategory productCategory = new EntityProductCategory();
        productCategory.setProductCategoryName(addProductCategoryReqDTO.getProductCategoryName());

        UploadedFileNameResponseDTO uploadProductCategory = awss3Service.uploadFile(addProductCategoryReqDTO.getProductCategoryImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getProductCategoryFolderName(), ImageType.productCategoryImage);
        productCategory.setProductCategoryImage(uploadProductCategory.getImageName());

        productCategoryRepository.save(productCategory);
    }

    @Override
    public void editProductCategory(EditProductCategoryReqDTO editProductCategoryReqDTO) {
        if (Objects.nonNull(editProductCategoryReqDTO.getProductCategoryImage()) && StringUtil.nonNullNonEmpty(editProductCategoryReqDTO.getExistingProductCategoryImage())) {
            throw new BusinessValidationException("product_category_image_error");
        }
        if (Objects.isNull(editProductCategoryReqDTO.getProductCategoryImage()) && StringUtil.nullOrEmpty(editProductCategoryReqDTO.getExistingProductCategoryImage())) {
            throw new BusinessValidationException("product_category_image_required");
        }
        EntityProductCategory entityProductCategory = productCategoryRepository.findByProductCategoryIdAndIsDeleted(editProductCategoryReqDTO.getProductCategoryId(), false);
        if (Objects.isNull(entityProductCategory)) {
            throw new BusinessValidationException("product_category_not_found");
        }
        if (productCategoryRepository.existsByProductCategoryIdNotAndProductCategoryNameAndIsDeleted(editProductCategoryReqDTO.getProductCategoryId(), editProductCategoryReqDTO.getProductCategoryName().trim(), false)) {
            throw new BusinessValidationException("product_category_already_exists");
        }
        entityProductCategory.setProductCategoryName(editProductCategoryReqDTO.getProductCategoryName());
        if (StringUtil.nullOrEmpty(editProductCategoryReqDTO.getExistingProductCategoryImage())) {
            if (StringUtil.nonNullNonEmpty(entityProductCategory.getProductCategoryImage())) {
                awss3Service.deleteFile(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getProductCategoryFolderName() + entityProductCategory.getProductCategoryImage(), ImageType.productCategoryImage);
                entityProductCategory.setProductCategoryImage(null);
            }
            if (editProductCategoryReqDTO.getProductCategoryImage() != null && !editProductCategoryReqDTO.getProductCategoryImage().isEmpty()) {
                UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(editProductCategoryReqDTO.getProductCategoryImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getProductCategoryFolderName(), ImageType.productCategoryImage);
                entityProductCategory.setProductCategoryImage(uploadedFileName.getImageName());
            }
        }
        productCategoryRepository.save(entityProductCategory);
    }


    @Override
    public SearchResultDTO<ProductCategoryResDTO> listProductCategoryCms(CommonListDTO commonListDTO) {
        Sort sort = null;
        if (commonListDTO.getSortBy() != null && commonListDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(commonListDTO.getSortBy().getDirection(), commonListDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(commonListDTO.getPage().getPageId(), commonListDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());

        List<ProductCategoryResDTO> ProductCategoryResDTOList = new ArrayList<>();
        long pageCount = productCategoryRepository.countProductCategories(commonListDTO.getQueryToSearch(), false);

        if (pageCount > 0) {
            String productCategoryImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getProductCategoryFolderName();
            ProductCategoryResDTOList = productCategoryRepository.findAllProductCategories(commonListDTO.getQueryToSearch(), productCategoryImagePath, pageable, false);
        }
        return new SearchResultDTO<>(ProductCategoryResDTOList, pageCount, commonListDTO.getPage().getLimit());
    }


    @Override
    public void deleteProductCategory(Long productCategoryId) {
        EntityProductCategory entityProductCategory = productCategoryRepository.findByProductCategoryIdAndIsDeleted(productCategoryId, false);
        if (Objects.isNull(entityProductCategory)) {
            throw new BusinessValidationException("product_category_not_found");
        }
        entityProductCategory.setDeleted(true);
        entityProductCategory.setActive(false);
        productCategoryRepository.save(entityProductCategory);
    }


    @Override
    public boolean activeInactiveProductCategory(Long productCategoryId) {
        boolean isActive = true;
        EntityProductCategory entityProductCategory = productCategoryRepository.findByProductCategoryIdAndIsDeleted(productCategoryId, false);
        if (Objects.isNull(entityProductCategory)) {
            throw new BusinessValidationException("product_category_not_found");
        }
        if (entityProductCategory.isActive()) {
            isActive = false;
        }
        entityProductCategory.setActive(!entityProductCategory.isActive());
        productCategoryRepository.save(entityProductCategory);
        return isActive;
    }

    @Override
    public List<ProductCategoryResDTO> listProductCategoryForUserAndCms() {
        String productCategoryImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getProductCategoryFolderName();
        return productCategoryRepository.findAllProductCategoriesWithoutPagination(productCategoryImagePath, true, false);
    }

}
