package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.cms.EmailDetailDTO;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.service.EmailService;
import com.mindbody.api.util.Mail;
import com.mindbody.api.util.StringUtil;
import freemarker.template.Configuration;
import freemarker.template.TemplateException;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.IOException;
import java.util.Map;

@Service
public class EmailServiceImpl implements EmailService {

    @Autowired
    private GlobalConfiguration globalConfiguration;

    @Autowired
    private JavaMailSender javaMailSender;

    @Autowired
    private Configuration configuration;


    /**
     * This method will send email.
     *
     * @param details - reference of class having metadata of email
     */


    @Override
    @Async
    public void sendMimeMail(EmailDetailDTO details) throws MessagingException {
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mimeMessage);
        mimeMessage.setFrom(globalConfiguration.getMailConfig().getMailFrom());
        helper.setTo(details.getRecipient());
        helper.setText(details.getMsgBody(), true);
        helper.setSubject(details.getSubject());
        JavaMailSenderImpl javaMailSenderImpl = (JavaMailSenderImpl) javaMailSender;
        javaMailSenderImpl.getJavaMailProperties().put("mail.smtp.ssl.protocols", "TLSv1.2");
        javaMailSender.send(mimeMessage);

    }

    @Override
    public String sendMailWithAttachment(EmailDetailDTO details) {
        return null;
    }

    /**
     * This method is used to convert HTML code into String by using .ftl file
     *
     * @param mail having value of template and mail.
     * @return returns html code converted into string.
     * @throws IOException       need to throw.
     * @throws TemplateException need to throw.
     */
    public String parseHtml(Mail mail) throws IOException, TemplateException {
        String html = "";
        freemarker.template.Template t = null;
        configuration.setClassForTemplateLoading(this.getClass(),  "/templates/");
        if (StringUtil.nonNullNonEmpty(mail.getTemplate())) {
            t = configuration.getTemplate(mail.getTemplate());
        }
        if (t != null && mail.getModel() != null) {
            html = FreeMarkerTemplateUtils.processTemplateIntoString(t, mail.getModel());
        }
        return html;
    }

    @Override
    public void sendEmail(String templateName, String subject, String toEmail, Map<String, Object> map) {
        if (StringUtil.nullOrEmpty(toEmail)) {
            throw new BusinessValidationException("email_not_found_notification_error");
        }
        Mail mail = new Mail();
        mail.setModel(map);
        mail.setTemplate(templateName);
        String html = null;
        try {
            html = parseHtml(mail);
        } catch (IOException | TemplateException e) {
            throw new RuntimeException(e);
        }
        EmailDetailDTO details = new EmailDetailDTO(toEmail, html, subject);
        try {
            sendMimeMail(details);
        } catch (MessagingException e) {
            throw new RuntimeException(e);
        }
    }
}
