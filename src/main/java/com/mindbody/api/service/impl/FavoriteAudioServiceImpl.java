package com.mindbody.api.service.impl;

import com.mindbody.api.dto.FavoriteAudioReqDTO;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.EntityAudio;
import com.mindbody.api.model.EntityFavoriteAudio;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.repository.AudioRepository;
import com.mindbody.api.repository.FavoriteAudioRepository;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.FavoriteAudioService;
import com.mindbody.api.service.MessageService;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class FavoriteAudioServiceImpl extends BaseService implements FavoriteAudioService {

    private final UserRepository userRepository;

    private final AudioRepository audioRepository;

    private final FavoriteAudioRepository favoriteAudioRepository;

    public FavoriteAudioServiceImpl(MessageService messageService, UserRepository userRepository, AudioRepository audioRepository, FavoriteAudioRepository favoriteAudioRepository) {
        super(messageService);
        this.userRepository = userRepository;
        this.audioRepository = audioRepository;
        this.favoriteAudioRepository = favoriteAudioRepository;
    }

    @Override
    public boolean favoriteAudio(FavoriteAudioReqDTO favoriteAudioReqDTO) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(favoriteAudioReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        EntityAudio entityAudio = audioRepository.findByAudioIdAndIsActiveAndIsDeleted(favoriteAudioReqDTO.getAudioId(), true,false);
        if (Objects.isNull(entityAudio)) {
            throw new BusinessValidationException("audio_not_found");
        }
        boolean isFavorite = false;
        EntityFavoriteAudio entityFavoriteAudio = favoriteAudioRepository.findByUserIdAndAudioIdAndIsActiveAndIsDeleted(favoriteAudioReqDTO.getUserId(), favoriteAudioReqDTO.getAudioId(), true, false);

        if (Objects.nonNull(entityFavoriteAudio)) {
            if (entityFavoriteAudio.isFavorite()) {
                isFavorite = true;
            }
        } else {
            entityFavoriteAudio = new EntityFavoriteAudio();
        }
        entityFavoriteAudio.setEntityUser(entityUser);
        entityFavoriteAudio.setUserId(entityUser.getUserId());
        entityFavoriteAudio.setEntityAudio(entityAudio);
        entityFavoriteAudio.setAudioId(entityAudio.getAudioId());
        entityFavoriteAudio.setFavorite(!entityFavoriteAudio.isFavorite());
        favoriteAudioRepository.save(entityFavoriteAudio);
        return isFavorite;
    }
}
