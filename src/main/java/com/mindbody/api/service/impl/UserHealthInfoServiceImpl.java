package com.mindbody.api.service.impl;

import com.mindbody.api.dto.SubmitUserAchievementReqDTO;
import com.mindbody.api.dto.UserHealthReqDTO;
import com.mindbody.api.dto.UserHealthResDTO;
import com.mindbody.api.dto.UserIdDTO;
import com.mindbody.api.enums.AchievementActivityType;
import com.mindbody.api.enums.ActivityFactorType;
import com.mindbody.api.enums.GenderType;
import com.mindbody.api.enums.MeasurementUnitType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.UserHealthInfoMapper;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.model.EntityUserHealthInfo;
import com.mindbody.api.repository.UserHealthInfoRepository;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.UserAchievementService;
import com.mindbody.api.service.UserHealthInfoService;
import com.mindbody.api.util.Methods;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class UserHealthInfoServiceImpl extends BaseService implements UserHealthInfoService {

    private final UserHealthInfoMapper userHealthInfoMapper;

    private final UserHealthInfoRepository userHealthInfoRepository;

    private final UserRepository userRepository;

    private final UserAchievementService userAchievementService;

    public UserHealthInfoServiceImpl(MessageService messageService, UserHealthInfoMapper userHealthInfoMapper, UserHealthInfoRepository userHealthInfoRepository, UserRepository userRepository, UserAchievementService userAchievementService) {
        super(messageService);
        this.userHealthInfoMapper = userHealthInfoMapper;
        this.userHealthInfoRepository = userHealthInfoRepository;
        this.userRepository = userRepository;
        this.userAchievementService = userAchievementService;
    }

    @Override
    public UserHealthResDTO calculateUserHealthInfo(UserHealthReqDTO userHealthReqDTO) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(userHealthReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        // Convert measurements to metric units for calculations
        double weightInKg = convertToKg(userHealthReqDTO.getWeight(), userHealthReqDTO.getWeightUnit());
        double heightInCm = convertToCm(userHealthReqDTO.getHeight(), userHealthReqDTO.getHeightUnit());

        double bmi = calculateBMI(weightInKg, heightInCm);
        double bmr = calculateBMR(weightInKg, heightInCm, userHealthReqDTO.getAge(), userHealthReqDTO.getGender());
        double protein = calculateProteinIntake(weightInKg);
        double calories = calculateCalories(bmr, userHealthReqDTO.getActivityFactorType());
        double carbs = calculateCarbs(calories, protein);
        double fat = calculateFat(calories);

        EntityUserHealthInfo entityUserHealthInfo = userHealthInfoRepository.findByUserIdAndIsActiveAndIsDeleted(userHealthReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUserHealthInfo)) {
            entityUserHealthInfo = new EntityUserHealthInfo();
            entityUserHealthInfo.setUserId(entityUser.getUserId());
            entityUserHealthInfo.setEntityUser(entityUser);
        }
        updateUserHealthInfo(entityUserHealthInfo, userHealthReqDTO, weightInKg, heightInCm, bmi, bmr, protein, calories, carbs, fat);

        userHealthInfoRepository.save(entityUserHealthInfo);

        if (!userAchievementService.existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Connection_Profile_Complete, entityUser.getUserId())) {
            userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Connection_Profile_Complete, entityUser.getUserId()));
        }

        return userHealthInfoMapper.toDTO(entityUserHealthInfo);
    }

    private double convertToKg(double weight, MeasurementUnitType unit) {
        if (unit == MeasurementUnitType.KG) {
            return weight;
        } else if (unit == MeasurementUnitType.LBS) {
            return weight * 0.45359237; // 1 lb = 0.45359237 kg
        }
        throw new BusinessValidationException("invalid_weight_unit");
    }

    private double convertToCm(double height, MeasurementUnitType unit) {
        if (unit == MeasurementUnitType.CM) {
            return height;
        } else if (unit == MeasurementUnitType.FT) {
            return height * 30.48; // 1 ft = 30.48 cm
        }
        throw new BusinessValidationException("invalid_height_unit");
    }

    private void updateUserHealthInfo(EntityUserHealthInfo entityUserHealthInfo, UserHealthReqDTO userHealthReqDTO, 
                                    double weightInKg, double heightInCm,
                                    double bmi, double bmr, double protein, double calories, double carbs, double fat) {
        // Store original measurements and units
        entityUserHealthInfo.setWeight(userHealthReqDTO.getWeight());
        entityUserHealthInfo.setWeightUnit(userHealthReqDTO.getWeightUnit());
        entityUserHealthInfo.setHeight(userHealthReqDTO.getHeight());
        entityUserHealthInfo.setHeightUnit(userHealthReqDTO.getHeightUnit());
        
        // Store converted measurements in metric units
        entityUserHealthInfo.setConvertedWeight(weightInKg);
        entityUserHealthInfo.setConvertedHeight(heightInCm);
        
        entityUserHealthInfo.setAge(userHealthReqDTO.getAge());
        entityUserHealthInfo.setGender(userHealthReqDTO.getGender());
        entityUserHealthInfo.setActivityFactorType(userHealthReqDTO.getActivityFactorType());

        entityUserHealthInfo.setBmi(Methods.roundOff(bmi));
        entityUserHealthInfo.setBmr(Methods.roundOff(bmr));
        entityUserHealthInfo.setCalories(Methods.roundOff(calories));
        entityUserHealthInfo.setCarbs(Methods.roundOff(carbs));
        entityUserHealthInfo.setProtein(Methods.roundOff(protein));
        entityUserHealthInfo.setFat(Math.round(Methods.roundOff(fat)));
    }

    @Override
    public UserHealthResDTO viewUserHealth(UserIdDTO userIdDTO) {
        UserHealthResDTO userHealthResDTO = new UserHealthResDTO();
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(userIdDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        EntityUserHealthInfo entityUserHealthInfo = userHealthInfoRepository.findByUserIdAndIsActiveAndIsDeleted(userIdDTO.getUserId(), true, false);
        if (Objects.nonNull(entityUserHealthInfo)) {
            return userHealthInfoMapper.toDTO(entityUserHealthInfo);
        }
        return userHealthResDTO;
    }

    public double calculateBMI(double weightInKg, double heightInCm) {
        double heightInMeters = heightInCm / 100; // Convert height to meters
        return weightInKg / (heightInMeters * heightInMeters);
    }

    public double calculateProteinIntake(double weightInKg) {
        return weightInKg;
    }

    public double calculateBMR(double weightInKg, double heightInCm, int age, GenderType gender) {
        if (GenderType.MALE.equals(gender)) {
            return (10 * weightInKg) + (6.25 * heightInCm) - (5 * age) + 5;
        } else if (GenderType.FEMALE.equals(gender)) {
            return (10 * weightInKg) + (6.25 * heightInCm) - (5 * age) - 161;
        } else {
            return 0;
        }
    }

    public double calculateCalories(double bmr, ActivityFactorType activityFactorType) {
        double activityMultiplier = switch (activityFactorType) {
            case Sedentary -> 1.15;
            case Lightly_Active -> 1.25;
            case Moderately_Active -> 1.50;
            case Very_Active -> 1.65;
            case Extra_Active -> 1.85;
            default -> 1.15;
        };
        return bmr * activityMultiplier;
    }

    public double calculateCarbs(double calories, double proteinIntake) {
        return (calories - (proteinIntake * 4) - (calories * 0.30)) / 4;
    }

    public double calculateFat(double calories) {
        return (calories * 0.30) / 9;
    }
}
