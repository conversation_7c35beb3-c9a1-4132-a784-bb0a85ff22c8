package com.mindbody.api.service.impl;

import com.mindbody.api.dto.SubmitCategoryTimeSpentReqDTO;
import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.repository.UserPointsTrackerRepository;
import com.mindbody.api.service.UserPointsTrackerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UserPointsTrackerServiceImpl implements UserPointsTrackerService {


}
