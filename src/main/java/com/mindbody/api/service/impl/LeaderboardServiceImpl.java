package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.*;
import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.enums.ZodiacSignCategoryType;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.*;
import com.mindbody.api.repository.*;
import com.mindbody.api.service.LeaderboardService;
import com.mindbody.api.service.UserAchievementService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class LeaderboardServiceImpl implements LeaderboardService {

    private final UserInfoRepository userInfoRepository;
    private final UserStatsRepository userStatsRepository;
    private final UserPointsTrackerRepository pointsTrackerRepository;
    private final AchievementsRepository achievementsRepository;
    private final UserAchievementsRepository userAchievementsRepository;
    private final GlobalConfiguration globalConfiguration;
    private final UserAchievementService userAchievementService;
    private final UserRepository userRepository;
    private final PinnedUserMappingRepository pinnedUserMappingRepository;

    protected static final Logger logger = LoggerFactory.getLogger(LeaderboardServiceImpl.class);


    @Override
    public Map<String, Object> generateLeaderboard(GenerateLeaderboardReqDTO generateLeaderboardReqDTO) {
        String borderImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBorderFolderName();
        String badgeImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBadgeFolderName();

        // Fetch stats for "alltime" timeframe to calculate total stats
        List<EntityUserStats> allTimeStats = userStatsRepository.findAll();

        // Aggregate "alltime" stats by user
        Map<Long, EntityUserStats> allTimeAggregatedStats = allTimeStats.stream()
                .collect(Collectors.toMap(
                        EntityUserStats::getUserId,
                        stat -> stat,
                        (stat1, stat2) -> {
                            stat1.setMindCategoryTimeSpent(
                                    (stat1.getMindCategoryTimeSpent() != null ? stat1.getMindCategoryTimeSpent() : 0L) +
                                            (stat2.getMindCategoryTimeSpent() != null ? stat2.getMindCategoryTimeSpent() : 0L)
                            );
                            stat1.setBodyCategoryTimeSpent(
                                    (stat1.getBodyCategoryTimeSpent() != null ? stat1.getBodyCategoryTimeSpent() : 0L) +
                                            (stat2.getBodyCategoryTimeSpent() != null ? stat2.getBodyCategoryTimeSpent() : 0L)
                            );
                            stat1.setWxpEarned(
                                    (stat1.getWxpEarned() != null ? stat1.getWxpEarned() : 0L) +
                                            (stat2.getWxpEarned() != null ? stat2.getWxpEarned() : 0L)
                            );
                            return stat1;
                        }
                ));

        // Fetch stats based on the requested timeframe
        List<EntityUserStats> stats;
        EntityUserInfo currentUserInfo = userInfoRepository.findByUserIdAndIsDeleted(generateLeaderboardReqDTO.getCurrentUserId(), false);

        // Fetch users based on category
        List<EntityUserInfo> users = new ArrayList<>();
        if ("local".equalsIgnoreCase(generateLeaderboardReqDTO.getCategory())) {
            //Convert
            double radius = 10.0; // Start with a 10 km radius
            while (users.size() < 100 && radius <= 100) { // Expand radius progressively
                users = userInfoRepository.findUsersWithinRadius(
                        generateLeaderboardReqDTO.getLatitude(),
                        generateLeaderboardReqDTO.getLongitude(),
                        radius
                );
                radius *= 2; // Double the radius
            }
        } else if ("worldwide".equalsIgnoreCase(generateLeaderboardReqDTO.getCategory())) {
            users = userInfoRepository.findAll();
        } else {
            throw new BusinessValidationException("invalid_category");
        }

        switch (generateLeaderboardReqDTO.getTimeframe().toLowerCase()) {
            case "alltime":
                stats = allTimeStats;
                break;
            case "weekly", "daily":
                stats = userStatsRepository.findByDateBetween(generateLeaderboardReqDTO.getStartDateTime(), generateLeaderboardReqDTO.getEndDateTime());
                break;
            default:
                throw new BusinessValidationException("invalid_timeframe");
        }

        // Aggregate stats by user for the requested timeframe
        Map<Long, EntityUserStats> aggregatedStats = stats.stream()
                .collect(Collectors.toMap(
                        EntityUserStats::getUserId,
                        stat -> stat,
                        (stat1, stat2) -> {
                            stat1.setMindCategoryTimeSpent(
                                    (stat1.getMindCategoryTimeSpent() != null ? stat1.getMindCategoryTimeSpent() : 0L) +
                                            (stat2.getMindCategoryTimeSpent() != null ? stat2.getMindCategoryTimeSpent() : 0L)
                            );
                            stat1.setBodyCategoryTimeSpent(
                                    (stat1.getBodyCategoryTimeSpent() != null ? stat1.getBodyCategoryTimeSpent() : 0L) +
                                            (stat2.getBodyCategoryTimeSpent() != null ? stat2.getBodyCategoryTimeSpent() : 0L)
                            );
                            stat1.setWxpEarned(
                                    (stat1.getWxpEarned() != null ? stat1.getWxpEarned() : 0L) +
                                            (stat2.getWxpEarned() != null ? stat2.getWxpEarned() : 0L)
                            );
                            return stat1;
                        }
                ));


        // Filter aggregated stats to include only users in the selected category
        List<EntityUserInfo> finalUsers = users;
        aggregatedStats = aggregatedStats.entrySet().stream()
                .filter(entry -> finalUsers.stream().anyMatch(user -> user.getUserId().equals(entry.getKey())))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        Map<Long, EntityUser> userMap = userRepository.findAllById(aggregatedStats.keySet()).stream()
                .collect(Collectors.toMap(EntityUser::getUserId, user -> user));

        // Fetch additional data for users
        Map<Long, EntityUserPointsTracker> pointsTrackerMap = pointsTrackerRepository.findAllByUserIdIn(aggregatedStats.keySet()).stream()
                .collect(Collectors.toMap(EntityUserPointsTracker::getUserId, tracker -> tracker));

        // Fetch achievements for users
        Map<Long, List<EntityUserAchievements>> userAchievementsMap = userAchievementsRepository.findAllByUserIdIn(aggregatedStats.keySet()).stream()
                .collect(Collectors.groupingBy(EntityUserAchievements::getUserId));

        // Calculate level rank worldwide for "alltime" timeframe
        List<Long> worldwideLevelRanks = allTimeAggregatedStats.entrySet().stream()
                .sorted(Comparator.comparingLong((Map.Entry<Long, EntityUserStats> entry) -> entry.getValue().getWxpEarned()).reversed())
                .map(Map.Entry::getKey)
                .toList();

        // Map users to DTOs
        List<LeaderboardUserDTO> leaderboard = aggregatedStats.entrySet().stream().map(entry -> {
            Long userId = entry.getKey();
            EntityUserStats statsForUser = entry.getValue();
            EntityUserStats allTimeStatsForUser = allTimeAggregatedStats.get(userId);
            EntityUserInfo userInfo = finalUsers.stream().filter(user -> user.getUserId().equals(userId)).findFirst().orElse(null);
            EntityUserPointsTracker pointsTracker = pointsTrackerMap.get(userId);

            if (userInfo == null || pointsTracker == null || allTimeStatsForUser == null) {
                return null; // Skip if any required data is missing
            }

            // Fetch achievements for the user
            List<EntityUserAchievements> userAchievements = userAchievementsMap.getOrDefault(userId, Collections.emptyList());
            Integer totalAchievements = userAchievements.size();

            // Determine the most achieved category
            AchievementCategoryType mostAchievedCategory = userAchievements.stream()
                    .collect(Collectors.groupingBy(EntityUserAchievements::getAchievementCategoryType, Collectors.counting()))
                    .entrySet().stream()
                    .max(Comparator.comparingLong(Map.Entry::getValue))
                    .map(Map.Entry::getKey)
                    .orElse(AchievementCategoryType.Mind);

            // Determine the level based on wxpEarned
            Map<String, Long> levelMap = Objects.nonNull(statsForUser.getWxpEarned()) ? userAchievementService.getCurrentAndNextLevelMap(statsForUser.getWxpEarned()) : null;
            Long currentLevel = Objects.nonNull(levelMap) ? levelMap.getOrDefault(Constant.CURRENT_LEVEL, 1L) : 1L;

            Optional<EntityUserAchievements> mostRecentAchievementOptional = userAchievements.stream()
                    .max(Comparator.comparing(EntityUserAchievements::getCreatedAt));
            String recentAchievementTitle = mostRecentAchievementOptional.map(a -> a.getAchievementTitleId().getTitle()).orElse(null);
            String recentAchievementBadge = mostRecentAchievementOptional.map(a -> a.getEntityAchievements().getBadgeImage()).orElse(null);

            // Calculate level rank worldwide
            Long levelRankWorldWide = (long) (worldwideLevelRanks.indexOf(userId) + 1);

            // Provide default values if any field is null
            String title = userInfo.getTitleAchievementId() != null
                    ? userInfo.getTitleAchievementId().getEntityAchievementTitle().getTitle()
                    : null;
            String borderImage = userInfo.getBorderAchievementId() != null
                    ? userInfo.getBorderAchievementId().getBorderImage()
                    : null;
            String badgeImage = userInfo.getMedalAchievementId() != null
                    ? userInfo.getMedalAchievementId().getBadgeImage()
                    : null;

            return LeaderboardUserDTO.builder()
                    .userId(userId)
                    .name(userInfo.getName() != null ? userInfo.getName() : null)
                    .googleAccountName(userInfo.getGoogleAccountName() != null ? userInfo.getGoogleAccountName() : null)
                    .facebookAccountName(userInfo.getFacebookAccountName() != null ? userInfo.getFacebookAccountName() : null)
                    .appleAccountName(userInfo.getAppleAccountName() != null ? userInfo.getAppleAccountName() : null)
                    .zodiacSign(userInfo.getZodiacSign() != null ? userInfo.getZodiacSign() : null)
                    .mindCategoryTimeSpent(statsForUser.getMindCategoryTimeSpent() != null ? statsForUser.getMindCategoryTimeSpent() : 0L)
                    .bodyCategoryTimeSpent(statsForUser.getBodyCategoryTimeSpent() != null ? statsForUser.getBodyCategoryTimeSpent() : 0L)
                    .wxpEarned(statsForUser.getWxpEarned() != null ? statsForUser.getWxpEarned() : 0L)
                    .currentLevel(currentLevel)
                    .title(StringUtil.nonNullNonEmpty(title) ? title : null)
                    .borderImage(StringUtil.nonNullNonEmpty(borderImage) ? borderImagePath + borderImage : null)
                    .badgeImage(StringUtil.nonNullNonEmpty(badgeImage) ? badgeImagePath + badgeImage : null)
                    .totalAchievements(totalAchievements)
                    .recentAchievementTitle(StringUtil.nonNullNonEmpty(recentAchievementTitle) ? recentAchievementTitle : null)
                    .recentAchievementBadge(StringUtil.nonNullNonEmpty(recentAchievementBadge) ? globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBadgeFolderName() + recentAchievementBadge : null)
                    .mostAchievedCategory(mostAchievedCategory)
                    .totalMindCategoryTimeSpent(allTimeStatsForUser.getMindCategoryTimeSpent() != null ? allTimeStatsForUser.getMindCategoryTimeSpent() : 0L)
                    .totalBodyCategoryTimeSpent(allTimeStatsForUser.getBodyCategoryTimeSpent() != null ? allTimeStatsForUser.getBodyCategoryTimeSpent() : 0L)
                    .totalWxpEarned(allTimeStatsForUser.getWxpEarned() != null ? allTimeStatsForUser.getWxpEarned() : 0L)
                    .levelRankWorldWide(levelRankWorldWide)
                    .countryCode(userMap.getOrDefault(userId, null).getCountryCode())
                    .zodiacSignCategory(userInfo.getZodiacSign() != null ? getZodiacSignCategory(userInfo.getZodiacSign()) : null)
                    .userType(userMap.getOrDefault(userId, null).getUserType())
                    .build();
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // Sort leaderboard based on category
        switch (generateLeaderboardReqDTO.getLeaderboardType().toLowerCase()) {
            case "mind":
                leaderboard.sort(Comparator.comparingLong(LeaderboardUserDTO::getMindCategoryTimeSpent).reversed());
                break;
            case "body":
                leaderboard.sort(Comparator.comparingLong(LeaderboardUserDTO::getBodyCategoryTimeSpent).reversed());
                break;
            case "warrior":
                leaderboard.sort(Comparator.comparingLong(LeaderboardUserDTO::getWxpEarned).reversed());
                break;
            case "level":
                leaderboard.sort(Comparator.comparingLong(LeaderboardUserDTO::getCurrentLevel).reversed());
                break;
            case "astrology":
                leaderboard = leaderboard.stream()
                        .collect(Collectors.groupingBy(LeaderboardUserDTO::getZodiacSign))
                        .entrySet().stream()
                        .map(entry -> {
                            ZodiacSignType zodiacSign = entry.getKey();
                            long totalWxpByZodiac = entry.getValue().stream()
                                    .mapToLong(LeaderboardUserDTO::getWxpEarned)
                                    .sum();
                            return LeaderboardUserDTO.builder()
                                    .zodiacSign(zodiacSign)
                                    .wxpEarned(totalWxpByZodiac) // Total WXP for the zodiac sign
                                    .zodiacSignCategory(getZodiacSignCategory(zodiacSign))
                                    .build();
                        })
                        .sorted(Comparator.comparingLong(LeaderboardUserDTO::getWxpEarned).reversed()) // Sort by total WXP
                        .collect(Collectors.toList());

                // Assign ranks to zodiac signs
                for (int i = 0; i < leaderboard.size(); i++) {
                    leaderboard.get(i).setRank((long) (i + 1));
                }
                break;
            default:
                throw new BusinessValidationException("invalid_leaderboard_type");
        }

        // Assign ranks
        for (int i = 0; i < leaderboard.size(); i++) {
            leaderboard.get(i).setRank((long) (i + 1));
        }

        // Fetch current user's details
        LeaderboardUserDTO currentUserDetails = null;

        if (currentUserInfo != null) {
            EntityUserStats currentUserStats = aggregatedStats.get(generateLeaderboardReqDTO.getCurrentUserId());
            EntityUserStats allTimeCurrentUserStats = allTimeAggregatedStats.get(generateLeaderboardReqDTO.getCurrentUserId());
            EntityUserPointsTracker currentUserPoints = pointsTrackerMap.get(generateLeaderboardReqDTO.getCurrentUserId());

            if (currentUserStats != null && currentUserPoints != null && allTimeCurrentUserStats != null) {
                List<EntityUserAchievements> currentUserAchievements = userAchievementsMap.getOrDefault(generateLeaderboardReqDTO.getCurrentUserId(), Collections.emptyList());
                EntityUser currentUser = userRepository.findByUserId(generateLeaderboardReqDTO.getCurrentUserId());
                if (Objects.isNull(currentUser)) {
                    throw new BusinessValidationException("current_user_not_found");
                }
                Integer totalAchievements = currentUserAchievements.size();

                // Determine the most achieved category
                AchievementCategoryType mostAchievedCategory = currentUserAchievements.stream()
                        .collect(Collectors.groupingBy(EntityUserAchievements::getAchievementCategoryType, Collectors.counting()))
                        .entrySet().stream()
                        .max(Comparator.comparingLong(Map.Entry::getValue))
                        .map(Map.Entry::getKey)
                        .orElse(AchievementCategoryType.Mind);

                // Determine the level based on wxpEarned
                Map<String, Long> levelMap = Objects.nonNull(currentUserStats.getWxpEarned()) ? userAchievementService.getCurrentAndNextLevelMap(currentUserStats.getWxpEarned()) : null;
                Long currentLevel = Objects.nonNull(levelMap) ? levelMap.getOrDefault(Constant.CURRENT_LEVEL, 1L) : 1L;

                Optional<EntityUserAchievements> mostRecentAchievementOptional = currentUserAchievements.stream()
                        .max(Comparator.comparing(EntityUserAchievements::getCreatedAt));
                String recentAchievementTitle = mostRecentAchievementOptional.map(a -> a.getAchievementTitleId().getTitle()).orElse(null);
                String recentAchievementBadge = mostRecentAchievementOptional.map(a -> a.getEntityAchievements().getBadgeImage()).orElse(null);

                // Calculate level rank worldwide
                Long levelRankWorldWide = (long) (worldwideLevelRanks.indexOf(generateLeaderboardReqDTO.getCurrentUserId()) + 1);

                currentUserDetails = LeaderboardUserDTO.builder()
                        .userId(generateLeaderboardReqDTO.getCurrentUserId())
                        .name(currentUserInfo.getName() != null ? currentUserInfo.getName() : null)
                        .googleAccountName(currentUserInfo.getGoogleAccountName() != null ? currentUserInfo.getGoogleAccountName() : null)
                        .facebookAccountName(currentUserInfo.getFacebookAccountName() != null ? currentUserInfo.getFacebookAccountName() : null)
                        .appleAccountName(currentUserInfo.getAppleAccountName() != null ? currentUserInfo.getAppleAccountName() : null)
                        .zodiacSign(currentUserInfo.getZodiacSign() != null ? currentUserInfo.getZodiacSign() : null)
                        .mindCategoryTimeSpent(currentUserStats.getMindCategoryTimeSpent() != null ? currentUserStats.getMindCategoryTimeSpent() : 0L)
                        .bodyCategoryTimeSpent(currentUserStats.getBodyCategoryTimeSpent() != null ? currentUserStats.getBodyCategoryTimeSpent() : 0L)
                        .wxpEarned(currentUserStats.getWxpEarned() != null ? currentUserStats.getWxpEarned() : 0L)
                        .currentLevel(currentLevel)
                        .title(currentUserInfo.getTitleAchievementId() != null
                                ? currentUserInfo.getTitleAchievementId().getEntityAchievementTitle().getTitle()
                                : null)
                        .borderImage(currentUserInfo.getBorderAchievementId() != null
                                ? borderImagePath + currentUserInfo.getBorderAchievementId().getBorderImage()
                                : null)
                        .badgeImage(currentUserInfo.getMedalAchievementId() != null
                                ? badgeImagePath + currentUserInfo.getMedalAchievementId().getBadgeImage()
                                : null)
                        .totalAchievements(totalAchievements)
                        .recentAchievementTitle(recentAchievementTitle)
                        .recentAchievementBadge(Objects.nonNull(recentAchievementBadge)? badgeImagePath + recentAchievementBadge : null)
                        .mostAchievedCategory(mostAchievedCategory)
                        .totalMindCategoryTimeSpent(allTimeCurrentUserStats.getMindCategoryTimeSpent() != null ? allTimeCurrentUserStats.getMindCategoryTimeSpent() : 0L)
                        .totalBodyCategoryTimeSpent(allTimeCurrentUserStats.getBodyCategoryTimeSpent() != null ? allTimeCurrentUserStats.getBodyCategoryTimeSpent() : 0L)
                        .totalWxpEarned(allTimeCurrentUserStats.getWxpEarned() != null ? allTimeCurrentUserStats.getWxpEarned() : 0L)
                        .levelRankWorldWide(levelRankWorldWide)
                        .countryCode(currentUser.getCountryCode() != null ? currentUser.getCountryCode() : null)
                        .zodiacSignCategory(currentUserInfo.getZodiacSign() != null ? getZodiacSignCategory(currentUserInfo.getZodiacSign()) : null)
                        .userType(currentUser.getUserType())
                        .build();

                if (!generateLeaderboardReqDTO.getLeaderboardType().toLowerCase().equalsIgnoreCase("astrology")) {
                    currentUserDetails.setRank(leaderboard.stream()
                            .filter(user -> user.getUserId().equals(generateLeaderboardReqDTO.getCurrentUserId()))
                            .map(LeaderboardUserDTO::getRank)
                            .findFirst()
                            .orElse(null));
                }
            }
        }

        // Prepare response
        Map<String, Object> response = new HashMap<>();
        response.put("leaderboard", leaderboard.stream().limit(100).collect(Collectors.toList()));
        response.put("currentUser", currentUserDetails);

        return response;
    }

    /**
     * Service method to pin a user on the leaderboard.
     *
     * @param request DTO containing pinnedUserId and forUserId.
     */
    @Override
    public void pinLeaderboardUser(PinUserRequestDTO request) {

        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(request.getPinnedUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("pinned_user_not_found_error");
        }


        // Check if the pinned user already exists for the given user
        Optional<EntityPinnedUserMapping> existingPinnedUser = pinnedUserMappingRepository.findByPinnedUserIdAndForUserId(request.getPinnedUserId(), request.getForUserId());
        if (existingPinnedUser.isPresent()) {
            throw new BusinessValidationException("user_already_pinned");
        }

        // Save the pinned user
        EntityPinnedUserMapping entityPinnedUserMapping = new EntityPinnedUserMapping();
        entityPinnedUserMapping.setPinnedUserId(request.getPinnedUserId());
        entityPinnedUserMapping.setForUserId(request.getForUserId());
        pinnedUserMappingRepository.save(entityPinnedUserMapping);
    }

    @Override
    public MeSectionResponseDTO getLeaderboardProfile(Long currentUserId, String userType, Long userId) {

        String badgeImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBadgeFolderName();

        if (userId == null) {
            throw new BusinessValidationException("user_id_required_error");
        }

        // Fetch current user info
        EntityUserInfo currentUserInfo = userInfoRepository.findByUserIdAndIsDeleted(userId, false);
        if (currentUserInfo == null) {
            throw new BusinessValidationException("user_not_found_error");
        }

        EntityUser currentUser = userRepository.findByUserIdAndIsDeleted(userId, false);
        if (Objects.isNull(currentUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        // Fetch current user stats
        boolean isCurrentUserStatsExists = userStatsRepository.existsByUserIdAndIsDeleted(userId, false);

        // Fetch all-time stats for the current user
        AllTimeUserStatsReqDTO allTimeCurrentUserStats = userStatsRepository.findAllTimeStatsByUserId(userId);

        // Fetch achievements for the current user
        List<EntityUserAchievements> currentUserAchievements = userAchievementsRepository.findAllByUserIdAndIsDeleted(userId, false);

        // Determine the most achieved category
        AchievementCategoryType mostAchievedCategory = currentUserAchievements.stream()
                .collect(Collectors.groupingBy(EntityUserAchievements::getAchievementCategoryType, Collectors.counting()))
                .entrySet().stream()
                .max(Comparator.comparingLong(Map.Entry::getValue))
                .map(Map.Entry::getKey)
                .orElse(AchievementCategoryType.Mind);

        // Determine the level based on wxpEarned
        Map<String, Long> levelMap = isCurrentUserStatsExists ? userAchievementService.getCurrentAndNextLevelMap(allTimeCurrentUserStats.getWxpEarned()) : null;
        Long currentLevel = levelMap != null ? levelMap.getOrDefault(Constant.CURRENT_LEVEL, 1L) : 1;

        // Fetch the most recent achievement
        Optional<EntityUserAchievements> mostRecentAchievementOptional = currentUserAchievements.stream()
                .max(Comparator.comparing(EntityUserAchievements::getCreatedAt));
        String recentAchievementTitle = mostRecentAchievementOptional.map(a -> a.getAchievementTitleId().getTitle()).orElse(null);
        String recentAchievementBadge = mostRecentAchievementOptional.map(a -> StringUtil.nonNullNonEmpty(a.getEntityAchievements().getBadgeImage()) ?a.getEntityAchievements().getBadgeImage() : null).orElse(null);

        // Fetch pinned users
        List<EntityPinnedUserMapping> pinnedUsers = pinnedUserMappingRepository.findAllByForUserId(userId);
        Set<Long> pinnedUserIds = pinnedUsers.stream().map(EntityPinnedUserMapping::getPinnedUserId).collect(Collectors.toSet());
        List<EntityUserInfo> pinnedUserInfos = userInfoRepository.findByUserIdInAndIsActiveAndIsDeleted(pinnedUserIds.toArray(new Long[]{}), true, false).orElseThrow(() -> new BusinessValidationException("pinned_user_not_found"));

        // Fetch points tracker for pinned users
        Map<Long, EntityUserPointsTracker> pinnedUserPointsMap = pointsTrackerRepository.findAllByUserIdIn(pinnedUserIds).stream()
                .collect(Collectors.toMap(EntityUserPointsTracker::getUserId, tracker -> tracker));

        List<PinnedUserDTO> pinnedUserDTOs = null;

        if (userType.equals("currentUser")) {

            // Map pinned users to DTOs
            pinnedUserDTOs = pinnedUserInfos.stream().map(userInfo -> {
                EntityUserPointsTracker pointsTracker = pinnedUserPointsMap.get(userInfo.getUserId());
                Long pinnedUserLevel = pointsTracker != null ? pointsTracker.getCurrentLevel() : 1L;

                return PinnedUserDTO.builder()
                        .userId(userInfo.getUserId())
                        .name(userInfo.getName())
                        .zodiacSign(userInfo.getZodiacSign())
                        .currentLevel(pinnedUserLevel)
                        .googleAccountName(userInfo.getGoogleAccountName())
                        .facebookAccountName(userInfo.getFacebookAccountName())
                        .appleAccountName(userInfo.getAppleAccountName())
                        .title(userInfo.getTitleAchievementId() != null
                                ? userInfo.getTitleAchievementId().getEntityAchievementTitle().getTitle()
                                : null)
                        .badgeImage(StringUtil.nonNullNonEmpty(Objects.nonNull(userInfo.getMedalAchievementId())?userInfo.getMedalAchievementId().getBadgeImage():null) ? globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBadgeFolderName() + userInfo.getMedalAchievementId().getBadgeImage() : null)
                        .build();
            }).collect(Collectors.toList());
        }

        // Check if the userId is pinned by currentUserId
        boolean isPinned = pinnedUserMappingRepository.findByPinnedUserIdAndForUserId(userId, currentUserId).isPresent();

        // Build the response
        return MeSectionResponseDTO.builder()
                .userId(userId)
                .name(currentUserInfo.getName())
                .zodiacSign(currentUserInfo.getZodiacSign())
                .currentLevel(currentLevel)
                .title(currentUserInfo.getTitleAchievementId() != null
                        ? currentUserInfo.getTitleAchievementId().getEntityAchievementTitle().getTitle()
                        : null)
                .borderImage(currentUserInfo.getBorderAchievementId() != null
                        ? globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                        globalConfiguration.getAmazonS3().getUploadFolderName() +
                        globalConfiguration.getAmazonS3().getAchievementBorderFolderName() +
                        currentUserInfo.getBorderAchievementId().getBorderImage()
                        : null)
                .badgeImage(Objects.nonNull(currentUserInfo.getMedalAchievementId())?StringUtil.nonNullNonEmpty(currentUserInfo.getMedalAchievementId().getBadgeImage()) ? badgeImagePath + currentUserInfo.getMedalAchievementId().getBadgeImage() : null:null)
                .mostAchievedCategory(mostAchievedCategory)
                .totalAchievements(currentUserAchievements.size())
                .recentAchievementTitle(recentAchievementTitle)
                .recentAchievementBadge(StringUtil.nonNullNonEmpty(recentAchievementBadge) ? globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBadgeFolderName() + recentAchievementBadge : null)
                .totalMindCategoryTimeSpent(Objects.nonNull(allTimeCurrentUserStats) ? allTimeCurrentUserStats.getMindCategoryTimeSpent() : 0)
                .totalBodyCategoryTimeSpent(Objects.nonNull(allTimeCurrentUserStats) ? allTimeCurrentUserStats.getBodyCategoryTimeSpent() : 0)
                .totalWxpEarned(Objects.nonNull(allTimeCurrentUserStats) ? allTimeCurrentUserStats.getWxpEarned() : 0)
                .levelRankWorldWide(calculateLevelRankWorldWide(userId))
                .googleAccountName(currentUserInfo.getGoogleAccountName())
                .facebookAccountName(currentUserInfo.getFacebookAccountName())
                .appleAccountName(currentUserInfo.getAppleAccountName())
                .countryCode(currentUser.getCountryCode())
                .pinnedUsers(pinnedUserDTOs)
                .isPinned(isPinned)
                .userType(currentUser.getUserType())
                .build();
    }

    /**
     * Helper method to calculate the worldwide level rank for the current user.
     *
     * @param currentUserId The ID of the current user.
     * @return The worldwide level rank.
     */
    private Long calculateLevelRankWorldWide(Long currentUserId) {
        // Fetch all EntityUserStats records
        List<EntityUserStats> userStats = userStatsRepository.findAllByUserIdAndIsActiveAndIsDeleted(currentUserId, true, false);

        if (userStats.isEmpty()) {
            logger.warn("No user stats found. Returning default rank as 0.");
            return 0L; // Return 0 if no stats are available
        }

        // Aggregate wxpEarned for each user and handle possible nulls
        Map<Long, Long> userWxpMap = userStats.stream()
                // Filter out null elements or invalid records
                .filter(Objects::nonNull)
                .filter(stats -> stats.getUserId() != null && stats.getWxpEarned() != null)
                .collect(Collectors.groupingBy(
                        EntityUserStats::getUserId,
                        Collectors.summingLong(EntityUserStats::getWxpEarned)
                ));

        if (userWxpMap.isEmpty()) {
            logger.warn("No valid user stats found for aggregation. Returning default rank as 0.");
            return 0L;
        }

        // Sort users by their total WxpEarned in descending order
        List<Long> worldwideLevelRanks = userWxpMap.entrySet().stream()
                .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
                .map(Map.Entry::getKey)
                .toList();

        // Check if the current userId exists in the rank calculation
        if (!worldwideLevelRanks.contains(currentUserId)) {
            logger.warn("Current user with ID {} not found in the worldwide leaderboard ranking.", currentUserId);
            return (long) worldwideLevelRanks.size() + 1; // Return the rank as outside the list
        }

        // Calculate the rank of the current user
        return (long) (worldwideLevelRanks.indexOf(currentUserId) + 1);
    }

    @Override
    public void unpinUser(PinUserRequestDTO pinUserRequestDTO) {

        // Check if the pinned user exists for the given user
        Optional<EntityPinnedUserMapping> pinnedUser = pinnedUserMappingRepository.findByPinnedUserIdAndForUserId(pinUserRequestDTO.getPinnedUserId(), pinUserRequestDTO.getForUserId());
        if (pinnedUser.isEmpty()) {
            throw new BusinessValidationException("pinned_user_not_found");
        }

        // Delete the pinned user record
        pinnedUserMappingRepository.delete(pinnedUser.get());
    }

    ZodiacSignCategoryType getZodiacSignCategory(ZodiacSignType zodiacSignType){
        return switch (zodiacSignType) {
            case Aries, Leo, Sagittarius -> ZodiacSignCategoryType.FIRE;
            case Taurus, Virgo, Capricorn -> ZodiacSignCategoryType.EARTH;
            case Gemini, Libra, Aquarius -> ZodiacSignCategoryType.AIR;
            case Cancer, Scorpio, Pisces -> ZodiacSignCategoryType.WATER;
            default -> throw new BusinessValidationException("invalid_zodiac_sign_type");
        };

    }


}