package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.*;
import com.mindbody.api.dto.cms.*;
import com.mindbody.api.dto.notification.*;
import com.mindbody.api.enums.*;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.exception.ForbiddenException;
import com.mindbody.api.mapper.UserAstrologyInfoMapper;
import com.mindbody.api.model.*;
import com.mindbody.api.repository.*;
import com.mindbody.api.security.CustomUserDetails;
import com.mindbody.api.security.ExecutionContextUtil;
import com.mindbody.api.security.JwtTokenUtil;
import com.mindbody.api.service.*;
import com.mindbody.api.util.*;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserServiceImpl extends BaseService implements UserService {

    private final UserRepository userRepository;

    private final UserInfoRepository userInfoRepository;

    private final JwtTokenUtil jwtTokenUtil;

    private final AccessTokenRepository accessTokenRepository;

    private final PasswordEncoder passwordEncoder;

    private final GlobalConfiguration globalConfiguration;

    private final OTPService otpService;

    private final EmailVerificationTokenRepository emailVerificationTokenRepository;

    private final AuthenticationManager authenticationManager;

    private final FcmTokenRepository fcmTokenRepository;

    private final UserAnswerRepository userAnswerRepository;

    private final DailyTipsRepository dailyTipsRepository;

    private final MagazineRepository magazineRepository;

    private final UserStreakRepository userStreakRepository;

    private final WorkoutPlaylistRepository workoutPlaylistRepository;


    private final AudioRepository audioRepository;

    private final WorkoutPlanRepository workoutPlanRepository;

    private final AstrologyService astrologyService;


    private final WorkoutPlanExerciseRepository workoutPlanExerciseRepository;

    private final AstrologyThirdPartyService astrologyThirdPartyService;

    private final UserAstrologyInfoMapper userAstrologyInfoMapper;

    private final UserAstrologyInfoRepository userAstrologyInfoRepository;

    private final AchievementsRepository achievementsRepository;

    private final UserPointsTrackerRepository userPointsTrackerRepository;

    private final UserAchievementService userAchievementService;

    private final NotificationRepository notificationRepository;

    private final NotificationService notificationService;

    private final UserTopicSubscriptionsRepository userTopicSubscriptionsRepository;

    private final UserDeviceService userDeviceService;

    private final UserReferralRepository userReferralRepository;

    public UserServiceImpl(MessageService messageService, UserRepository userRepository, UserInfoRepository userInfoRepository, JwtTokenUtil jwtTokenUtil, AccessTokenRepository accessTokenRepository, PasswordEncoder passwordEncoder, GlobalConfiguration globalConfiguration, @Lazy OTPService otpService, EmailVerificationTokenRepository emailVerificationTokenRepository, AuthenticationManager authenticationManager, FcmTokenRepository fcmTokenRepository, UserAnswerRepository userAnswerRepository, DailyTipsRepository dailyTipsRepository, MagazineRepository magazineRepository, UserStreakRepository userStreakRepository, WorkoutPlaylistRepository workoutPlaylistRepository, AudioRepository audioRepository, WorkoutPlanRepository workoutPlanRepository, AstrologyService astrologyService, WorkoutPlanExerciseRepository workoutPlanExerciseRepository, AstrologyThirdPartyService astrologyThirdPartyService, UserAstrologyInfoMapper userAstrologyInfoMapper, UserAstrologyInfoRepository userAstrologyInfoRepository, AchievementsRepository achievementsRepository, UserPointsTrackerRepository userPointsTrackerRepository, UserAchievementService userAchievementService, NotificationRepository notificationRepository, NotificationService notificationService, UserTopicSubscriptionsRepository userTopicSubscriptionsRepository, UserDeviceService userDeviceService, UserReferralRepository userReferralRepository) {
        super(messageService);
        this.userRepository = userRepository;
        this.userInfoRepository = userInfoRepository;
        this.jwtTokenUtil = jwtTokenUtil;
        this.accessTokenRepository = accessTokenRepository;
        this.passwordEncoder = passwordEncoder;
        this.globalConfiguration = globalConfiguration;
        this.otpService = otpService;
        this.emailVerificationTokenRepository = emailVerificationTokenRepository;
        this.authenticationManager = authenticationManager;
        this.fcmTokenRepository = fcmTokenRepository;
        this.userAnswerRepository = userAnswerRepository;
        this.dailyTipsRepository = dailyTipsRepository;
        this.magazineRepository = magazineRepository;
        this.userStreakRepository = userStreakRepository;
        this.workoutPlaylistRepository = workoutPlaylistRepository;
        this.audioRepository = audioRepository;
        this.workoutPlanRepository = workoutPlanRepository;
        this.astrologyService = astrologyService;
        this.workoutPlanExerciseRepository = workoutPlanExerciseRepository;
        this.astrologyThirdPartyService = astrologyThirdPartyService;
        this.userAstrologyInfoMapper = userAstrologyInfoMapper;
        this.userAstrologyInfoRepository = userAstrologyInfoRepository;
        this.achievementsRepository = achievementsRepository;
        this.userPointsTrackerRepository = userPointsTrackerRepository;
        this.userAchievementService = userAchievementService;
        this.notificationRepository = notificationRepository;
        this.notificationService = notificationService;
        this.userTopicSubscriptionsRepository = userTopicSubscriptionsRepository;
        this.userDeviceService = userDeviceService;
        this.userReferralRepository = userReferralRepository;
    }

    @Override
    public UserSignupResDTO userLogin(UserLoginDTO userLoginDTO) {
        validateEmailAndPhoneNumber(userLoginDTO.getEmail(), userLoginDTO.getCountryCode(), userLoginDTO.getPhoneNumber());

        EntityUser entityUser1 = userRepository.findByEmailAndIsDeleted(userLoginDTO.getEmail().toLowerCase(), false);
        if (Objects.nonNull(entityUser1)) {
            // Restrict login if the account is inactive
            if (!entityUser1.isActive()) {
                throw new ForbiddenException("user_account_deactivated");
            }
            if (!entityUser1.getRegisterType().equals(RegisterType.NORMAL) && StringUtil.nullOrEmpty(entityUser1.getPassword())) {
                throw new BusinessValidationException("this_user_previously_login_as_social_media_error", entityUser1.getRegisterType().toString());
            }
        } else {
            List<EntityUser> deletedAccountList = userRepository.findAllByAllEmailAndIsDeleted(userLoginDTO.getEmail().toLowerCase(), true);
            if (!deletedAccountList.isEmpty()) {
                throw new BusinessValidationException("user_account_deleted");
            }
        }

        Authentication authentication;
        /** login with email */
        ExecutionContextUtil.getContext().setRoleType(RoleType.USER);
        if (userLoginDTO.getEmail() != null) {
            userLoginDTO.setEmail(userLoginDTO.getEmail().toLowerCase());
            ExecutionContextUtil.getContext().setEmailProvided(true);
            authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(userLoginDTO.getEmail(), userLoginDTO.getPassword()));
            SecurityContextHolder.getContext().setAuthentication(authentication);
        }
        /** login with phoneNumber */
        else {
            ExecutionContextUtil.getContext().setEmailProvided(false);
            String phoneNumber = userLoginDTO.getCountryCode().concat(":").concat(userLoginDTO.getPhoneNumber());
            authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(phoneNumber, userLoginDTO.getPassword()));
            SecurityContextHolder.getContext().setAuthentication(authentication);
        }

        CustomUserDetails customUserDetails = (CustomUserDetails) authentication.getPrincipal();

        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(customUserDetails.getId(), true, false);
        if (entityUser.getAccountStatus().equals(AccountStatus.PENDING)) {
            throw new BusinessValidationException("user_account_not_verified");
        }

        /** Generate the jwt token based on the email or country code and phoneNumber */
        String jwt = jwtTokenUtil.generateJwtForUser(userLoginDTO.getEmail(), userLoginDTO.getCountryCode(), userLoginDTO.getPhoneNumber(), RoleType.USER, RegisterType.NORMAL.name());
        EntityAccessToken entityAccessToken = new EntityAccessToken(entityUser.getUserId(), RoleType.USER, EncryptUtil.encryptKey(jwt), null);
        accessTokenRepository.save(entityAccessToken);

        /** when fcmToken is already present with deviceUniqueId then update that fcmToken
         * and if not present then create new fcmToken against the deviceUniqueId
         */
        saveFcmTokenDetail(entityUser, userLoginDTO.getDeviceUniqueId(), userLoginDTO.getFcmToken(), userLoginDTO.getDeviceType());

        UserSignupResDTO userAllInformation = getUserAllInformation(customUserDetails.getId());
        userAllInformation.setLoginType(RegisterType.NORMAL.name());
        userAllInformation.setAccessToken(jwt);
        return userAllInformation;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserSignupResDTO signUpUser(UserSignupReqDTO userSignupReqDTO) {
        validateEmailAndPhoneNumber(userSignupReqDTO.getEmail(), userSignupReqDTO.getCountryCode(), userSignupReqDTO.getPhoneNumber());

        // Get the current IP address
        String ipAddress = userDeviceService.getCurrentIpAddress();
        log.info("User signup from IP: {}", ipAddress);

        EntityUser entityUser = userRepository.findByUserIdAndIsDeleted(userSignupReqDTO.getUserId(), false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        /** before we proceed to the sign user should hava complete the profile */
        if (!entityUser.isProfileCompleted()) {
            throw new BusinessValidationException("step_1_complete_error");
        }

        /** here are we check that if user is already exists or not */
        if (userSignupReqDTO.getEmail() != null) {
            userSignupReqDTO.setEmail(userSignupReqDTO.getEmail().toLowerCase());
            if (entityUser.getEmail() != null && !userSignupReqDTO.getEmail().equals(entityUser.getEmail())) {
                throw new BusinessValidationException("user_id_already_has_different_email");
            }
            if (userRepository.existsByAllEmailAndIsDeleted(userSignupReqDTO.getEmail(), false, entityUser.getUserId())) {
                throw new BusinessValidationException("account_already_exist_with_email");
            }
            entityUser.setEmail(userSignupReqDTO.getEmail());
            entityUser.setEmailRegistered(true);
        } else {
            if ((entityUser.getCountryCode() != null && entityUser.getPhoneNumber() != null) && (!userSignupReqDTO.getPhoneNumber().equals(entityUser.getPhoneNumber()) && !userSignupReqDTO.getCountryCode().equals(entityUser.getCountryCode()))) {
                throw new BusinessValidationException("user_id_already_has_different_phone_number");
            }
            if (userRepository.existsByCountryCodeAndPhoneNumberAndIsDeleted(userSignupReqDTO.getCountryCode(), userSignupReqDTO.getPhoneNumber(), false)) {
                throw new BusinessValidationException("account_already_exist_with_phone_number");
            }
            entityUser.setCountryCode(userSignupReqDTO.getCountryCode());
            entityUser.setPhoneNumber(userSignupReqDTO.getPhoneNumber());
            entityUser.setEmailRegistered(false);
        }

        entityUser.setUserType(UserType.NORMAL);
        entityUser.setRoleType(RoleType.USER);
        entityUser.setAccountStatus(AccountStatus.PENDING);
        entityUser.setRegisterType(RegisterType.NORMAL);
        entityUser.setPassword(passwordEncoder.encode(userSignupReqDTO.getPassword()));
        entityUser = userRepository.save(entityUser);

        saveUserInfo(entityUser, userSignupReqDTO.getName());

        /** Delete the jwt token the anonymous user */
        accessTokenRepository.deleteSecretTokenByRoleAndUserId(entityUser.getUserId(), entityUser.getRoleType());

        /** Generate the jwt token based on the email or country code and phoneNumber */
        String jwt = jwtTokenUtil.generateJwtForUser(userSignupReqDTO.getEmail(), userSignupReqDTO.getCountryCode(), userSignupReqDTO.getPhoneNumber(), RoleType.USER, RegisterType.NORMAL.name());
        EntityAccessToken entityAccessToken = new EntityAccessToken(entityUser.getUserId(), RoleType.USER, EncryptUtil.encryptKey(jwt), null);
        accessTokenRepository.save(entityAccessToken);

        UserSignupResDTO userSignupResDTO = getUserAllInformation(entityUser.getUserId());
        userSignupResDTO.setLoginType(ExecutionContextUtil.getContext().getLoginType());
        userSignupResDTO.setAccessToken(jwt);

        /** send the verification otp to the user */
        SendOTPReqDTO sendOTPReqDTO = new SendOTPReqDTO(userSignupReqDTO.getEmail(), userSignupReqDTO.getCountryCode(), userSignupReqDTO.getPhoneNumber(), OtpModuleName.ACCOUNT_VERIFICATION);
        otpService.sendVerificationOTP(sendOTPReqDTO);
        return userSignupResDTO;
    }

    @Override
    @Transactional
    public Object userSocialMediaLogin(SocialMediaLoginDTO socialMediaLoginDTO) {
        socialMediaLoginDTO.setEmail(socialMediaLoginDTO.getEmail().toLowerCase());

        EntityUser entityUser = null;
        if (socialMediaLoginDTO.getUserId() != null) {
            entityUser = userRepository.findByUserIdAndIsDeleted(socialMediaLoginDTO.getUserId(), false);
            if (Objects.isNull(entityUser)) {
                throw new BusinessValidationException("step_1_complete_error");
            }
        }

        // If no userId is provided, find the user by email or socialMediaId
        if (entityUser == null) {
            entityUser = userRepository.findByAllEmailAndIsDeleted(socialMediaLoginDTO.getEmail(), false);
            if (Objects.isNull(entityUser)) {
                throw new BusinessValidationException("user_not_found_error");
            }
            // If the user exists but hasn't completed step one, throw an error
            if(socialMediaLoginDTO.getRegisterType().equals(RegisterType.GOOGLE) && (StringUtil.nullOrEmpty(entityUser.getGoogleSocialMediaId()) || !entityUser.getGoogleSocialMediaId().equals(socialMediaLoginDTO.getSocialMediaId()))) {
                    throw new BusinessValidationException("step_1_complete_error");
            }else if(socialMediaLoginDTO.getRegisterType().equals(RegisterType.FACEBOOK) && (StringUtil.nullOrEmpty(entityUser.getFacebookSocialMediaId()) || !entityUser.getFacebookSocialMediaId().equals(socialMediaLoginDTO.getSocialMediaId()))) {
                throw new BusinessValidationException("step_1_complete_error");
            }else if(socialMediaLoginDTO.getRegisterType().equals(RegisterType.APPLE) && (StringUtil.nullOrEmpty(entityUser.getAppleSocialMediaId()) || !entityUser.getAppleSocialMediaId().equals(socialMediaLoginDTO.getSocialMediaId()))) {
                throw new BusinessValidationException("step_1_complete_error");
            }


        }
        /** this is old user so login */
        if (socialMediaLoginDTO.getRegisterType().equals(RegisterType.GOOGLE) && (StringUtil.nonNullNonEmpty(entityUser.getGoogleEmailId()) && entityUser.getGoogleEmailId().equals(socialMediaLoginDTO.getEmail()))) {
            if (StringUtil.nullOrEmpty(entityUser.getGoogleSocialMediaId())) {
                /**
                 * when user is already exist, and it is normal user, so they don't have a socialMediaId,
                 * so we are adding the socialMedia adding here so, we can login with that user in Google/Apple account also
                 */
                entityUser.setUserType(UserType.NORMAL);
//                entityUser.setRegisterType(socialMediaLoginDTO.getRegisterType());
                entityUser.setAccountStatus(AccountStatus.VERIFIED);
                entityUser.setGoogleSocialMediaId(socialMediaLoginDTO.getSocialMediaId());
                entityUser.setEmailRegistered(true);
                entityUser = userRepository.save(entityUser);
            } else {
                if (!entityUser.getGoogleSocialMediaId().equals(socialMediaLoginDTO.getSocialMediaId())) {
                    throw new BusinessValidationException("user_not_found_error");
                }
            }
        }else if (socialMediaLoginDTO.getRegisterType().equals(RegisterType.APPLE) && (StringUtil.nonNullNonEmpty(entityUser.getAppleEmailId()) && entityUser.getAppleEmailId().equals(socialMediaLoginDTO.getEmail()))) {
            if (StringUtil.nullOrEmpty(entityUser.getAppleSocialMediaId())) {
                /**
                 * when user is already exist, and it is normal user, so they don't have a socialMediaId,
                 * so we are adding the socialMedia adding here so, we can login with that user in Google/Apple account also
                 */
                entityUser.setUserType(UserType.NORMAL);
//                entityUser.setRegisterType(socialMediaLoginDTO.getRegisterType());
                entityUser.setAccountStatus(AccountStatus.VERIFIED);
                entityUser.setAppleSocialMediaId(socialMediaLoginDTO.getSocialMediaId());
                entityUser.setEmailRegistered(true);
                entityUser = userRepository.save(entityUser);
            } else {
                if (!entityUser.getAppleSocialMediaId().equals(socialMediaLoginDTO.getSocialMediaId())) {
                    throw new BusinessValidationException("user_not_found_error");
                }
            }
        }else if (socialMediaLoginDTO.getRegisterType().equals(RegisterType.FACEBOOK) && (StringUtil.nonNullNonEmpty(entityUser.getFacebookEmailId()) && entityUser.getFacebookEmailId().equals(socialMediaLoginDTO.getEmail()))) {
            if (StringUtil.nullOrEmpty(entityUser.getFacebookSocialMediaId())) {
                /**
                 * when user is already exist, and it is normal user, so they don't have a socialMediaId,
                 * so we are adding the socialMedia adding here so, we can login with that user in Google/Apple account also
                 */
                entityUser.setUserType(UserType.NORMAL);
//                entityUser.setRegisterType(socialMediaLoginDTO.getRegisterType());
                entityUser.setAccountStatus(AccountStatus.VERIFIED);
                entityUser.setAppleSocialMediaId(socialMediaLoginDTO.getSocialMediaId());
                entityUser.setEmailRegistered(true);
                entityUser = userRepository.save(entityUser);
            } else {
                if (!entityUser.getFacebookSocialMediaId().equals(socialMediaLoginDTO.getSocialMediaId())) {
                    throw new BusinessValidationException("user_not_found_error");
                }
            }
        }
        /** sign up process */
        else {
            boolean isUserExist = userRepository.existsByAllEmailAndIsDeleted(socialMediaLoginDTO.getEmail(), false, entityUser.getUserId());
            if (isUserExist) {
                throw new BusinessValidationException("email_already_associate_with_another_user");
            }
            entityUser.setUserType(UserType.NORMAL);
            entityUser.setRegisterType(socialMediaLoginDTO.getRegisterType());
            entityUser.setAccountStatus(AccountStatus.VERIFIED);
            if(socialMediaLoginDTO.getRegisterType().equals(RegisterType.FACEBOOK)) {
                entityUser.setFacebookSocialMediaId(socialMediaLoginDTO.getSocialMediaId());
                entityUser.setFacebookEmailId(socialMediaLoginDTO.getEmail());
            }else if(socialMediaLoginDTO.getRegisterType().equals(RegisterType.GOOGLE)) {
                entityUser.setGoogleSocialMediaId(socialMediaLoginDTO.getSocialMediaId());
                entityUser.setGoogleEmailId(socialMediaLoginDTO.getEmail());
            }else if(socialMediaLoginDTO.getRegisterType().equals(RegisterType.APPLE)) {
                entityUser.setAppleSocialMediaId(socialMediaLoginDTO.getSocialMediaId());
                entityUser.setAppleEmailId(socialMediaLoginDTO.getEmail());
            }
//            entityUser.setEmail(socialMediaLoginDTO.getEmail());
            entityUser.setEmailRegistered(true);
            entityUser = userRepository.save(entityUser);
            /** Send Welcome Notification To User **/
            NotificationDTO notificationDTO = notificationService.setWelcomeNotificationData();
            notificationService.sendNotificationToAppUser(entityUser, notificationDTO);
        }

        saveUserInfo(entityUser, socialMediaLoginDTO.getName());

        /** when fcmToken is already present with deviceUniqueId then update that fcmToken
         * and if not present then create new fcmToken against the deviceUniqueId
         */
        saveFcmTokenDetail(entityUser, socialMediaLoginDTO.getDeviceUniqueId(), socialMediaLoginDTO.getFcmToken(), socialMediaLoginDTO.getDeviceType());

        /** Delete the jwt token the anonymous user */
        accessTokenRepository.deleteSecretTokenByRoleAndUserId(entityUser.getUserId(), entityUser.getRoleType());

        /** Generate the jwt token based on the email or country code and phoneNumber */
        String jwt = jwtTokenUtil.generateJwtForUser(socialMediaLoginDTO.getEmail(), null, null, RoleType.USER, socialMediaLoginDTO.getRegisterType().name());
        EntityAccessToken entityAccessToken = new EntityAccessToken(entityUser.getUserId(), RoleType.USER, EncryptUtil.encryptKey(jwt), null);
        accessTokenRepository.save(entityAccessToken);

        UserSignupResDTO userSignupResDTO = getUserAllInformation(entityUser.getUserId());
        userSignupResDTO.setLoginType(socialMediaLoginDTO.getRegisterType().name());
        userSignupResDTO.setAccessToken(jwt);

        return userSignupResDTO;
    }

    private EntityUserInfo saveUserInfo(EntityUser entityUser, String name) {
        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(entityUser.getUserId(), true, false);
        if (Objects.isNull(entityUserInfo)) {
            throw new BusinessValidationException("user_info_not_found_error");
        }
        if(StringUtil.nonNullNonEmpty(entityUser.getFacebookEmailId())) {
            entityUserInfo.setFacebookAccountName(name);
        }else if(StringUtil.nonNullNonEmpty(entityUser.getGoogleEmailId())) {
            entityUserInfo.setGoogleAccountName(name);
        }else if(StringUtil.nonNullNonEmpty(entityUser.getAppleEmailId())) {
            entityUserInfo.setAppleAccountName(name);
        }else if(StringUtil.nonNullNonEmpty(entityUser.getEmail())) {
            entityUserInfo.setName(name);
        }
        entityUserInfo.setEntityUser(entityUser);
        entityUserInfo.setUserId(entityUser.getUserId());
        entityUserInfo = userInfoRepository.save(entityUserInfo);
        
        // Get all existing referral codes
        List<String> existingCodes = userReferralRepository.findAllReferralCodes();
        entityUser.setReferralCode(Methods.generateUniqueReferralCode(existingCodes));
        
        userRepository.save(entityUser);
        return entityUserInfo;
    }

    public void saveFcmTokenDetail(EntityUser entityUser, String deviceUniqueId, String fcmToken, DeviceType deviceType) {
        EntityFcmToken entityFcmToken = fcmTokenRepository.findByDeviceUniqueIdAndUserId(deviceUniqueId, entityUser.getUserId());
        if (Objects.nonNull(entityFcmToken)) {
            entityFcmToken.setFcmToken(fcmToken);
            entityFcmToken.setUpdatedAt(LocalDateTime.now());
            fcmTokenRepository.save(entityFcmToken);
        } else {
            entityFcmToken = new EntityFcmToken(deviceType, deviceUniqueId, fcmToken, entityUser, entityUser.getUserId());
            fcmTokenRepository.save(entityFcmToken);
        }
    }


    @Override
    public UserSignupResDTO getUserAllInformation(Long userId) {
        String profilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getUserProfileFolderName();
        String borderImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBorderFolderName();
        String badgeImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAchievementBadgeFolderName();

        UserSignupResDTO userSignupResDTO = userRepository.findAllUserInformation(userId, profilePath, borderImagePath, badgeImagePath);
        Optional<EntityUserPointsTracker> optionalUserPointsTracker = userPointsTrackerRepository.findByUserIdAndIsDeleted(userId, false);
        EntityUserPointsTracker userPointsTracker;
        if (optionalUserPointsTracker.isPresent()) {
            userPointsTracker = optionalUserPointsTracker.get();
            userSignupResDTO.setCurrentLevel(userPointsTracker.getCurrentLevel());
            userSignupResDTO.setCurrentPoint(userPointsTracker.getTotalPoints());
            userSignupResDTO.setCurrentWxp(userPointsTracker.getTotalWxp());
            Map<String, Long> nextLevelDetails = userAchievementService.getCurrentAndNextLevelMap(userPointsTracker.getTotalWxp());
            userSignupResDTO.setNextLevel(nextLevelDetails.get(Constant.NEXT_LEVEL));
            userSignupResDTO.setNextLevelWxp(nextLevelDetails.get(Constant.NEXT_LEVEL_WXP_POINT));
        }

        return userSignupResDTO;
    }

    @Override
    public ViewUserDetailsResDTO getUserAllInformationForCMS(Long userId) {
        String profilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getUserProfileFolderName();
        return userRepository.findAllUserInformationForCMS(userId, profilePath);
    }

    @Override
    public void resetPassword(ResetPasswordReqDTO resetPasswordReqDTO) {
        Optional<EmailVerificationToken> emailVerification = emailVerificationTokenRepository.findByVerificationTokenAndIsActiveAndIsDeleted(resetPasswordReqDTO.getVerificationToken(), true, false);
        if (emailVerification.isEmpty()) {
            throw new BusinessValidationException("error_reset_password_link_expired");
        }
        EntityUser user = userRepository.findById(emailVerification.get().getUserId()).orElseThrow(() -> new BusinessValidationException("user_not_found_error"));
        if (!emailVerification.get().getExpirationTime().after(new Date())) {
            throw new BusinessValidationException("error_verification_link_expired");
        }
        if (passwordEncoder.matches(resetPasswordReqDTO.getNewPassword(), user.getPassword())) {
            throw new BusinessValidationException("error_new_password_same_as_old_password");
        }
        user.setPassword(passwordEncoder.encode(resetPasswordReqDTO.getNewPassword()));
        userRepository.save(user);

        /** once password change successfully inactive the token */
        emailVerification.get().setActive(false);
        emailVerification.get().setDeleted(true);
        emailVerificationTokenRepository.save(emailVerification.get());
    }

    @Override
    @Transactional
    public void userLogout(String accessToken, DeviceUniqueIdDTO deviceUniqueIdDto) {
        if (StringUtil.nullOrEmpty(accessToken))
            throw new BusinessValidationException("access_token_not_found_error");
        Long userId = ExecutionContextUtil.getContext().getUserId();
        if (userId == null)
            throw new BusinessValidationException("user_id_required");
        accessTokenRepository.deleteByAccessTokenAndUserId(EncryptUtil.encryptKey(accessToken), userId);
        if (StringUtil.nonNullNonEmpty(deviceUniqueIdDto.getDeviceUniqueId())) {
            fcmTokenRepository.deleteByDeviceUniqueIdAndUserId(deviceUniqueIdDto.getDeviceUniqueId(), userId);
        }
        List<String> topicNames = deviceUniqueIdDto.getTopicNames();
        if (topicNames != null && !topicNames.isEmpty()) {
            /** Delete topics subscribed by user **/
            userTopicSubscriptionsRepository.deleteByUserIdAndTopicNameIn(userId, topicNames);
        }

    }

    @Override
    public SearchResultDTO<UserManagementResDTO> listUserManagement(CommonListDTO commonListDTO) {
        Sort sort = null;
        if (commonListDTO.getSortBy() != null && commonListDTO.getSortBy().getProperty() != null) {

            String sortProperty = commonListDTO.getSortBy().getProperty();
            Sort.Direction direction = commonListDTO.getSortBy().getDirection();

            // Example: If sorting by 'name' field in 'user_info' table
            if (sortProperty.equalsIgnoreCase("name")) {
                sort = Sort.by(direction, "ui.name"); // Use 'ui.name' for sorting by name
            } else if (sortProperty.equalsIgnoreCase("placeOfBirth")) {
                sort = Sort.by(direction, "ui.placeOfBirth"); // Use 'ui.placeOfBirth' for sorting by placeOfBirth
            } else if (sortProperty.equalsIgnoreCase("dateOfBirth")) {
                sort = Sort.by(direction, "ui.dateOfBirth"); // Use 'ui.dateOfBirth' for sorting by dateOfBirth
            } else {
                sort = Sort.by(direction, sortProperty); // Use other properties as specified
            }
        }
        Pageable pageable = PageRequest.of(commonListDTO.getPage().getPageId(),
                commonListDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());
        List<UserManagementResDTO> userList = new ArrayList<>();
        long pageCount;
        UserType userTypeFilter = null;
        boolean checkIsUserType = false;
        if (!StringUtil.nullOrEmpty(commonListDTO.getFilters().getSearchFilter())) {
            checkIsUserType = true;
            userTypeFilter = UserType.valueOf(commonListDTO.getFilters().getSearchFilter());
        }
        pageCount = userRepository.countUsers(commonListDTO.getQueryToSearch(), checkIsUserType, userTypeFilter);
        if (pageCount > 0) {
            String profilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getUserProfileFolderName();
            Page<UserManagementResDTO> usersPage = userRepository.listUsers(
                    checkIsUserType,
                    commonListDTO.getQueryToSearch(),
                    profilePath,
                    userTypeFilter,
                    pageable
            );
            userList = usersPage.getContent();
        }
        return new SearchResultDTO<>(userList, pageCount, commonListDTO.getPage().getLimit());
    }

    @Override
    public SearchResultDTO<FilterUsersForNotificationResDTO> filterUsersBasedOnNotificationFilters(FilterUsersForNotificationReqDTO filterUsersForNotificationReqDTO) {
        LocalDate currentDate = LocalDate.now(ZoneOffset.UTC);
        Pageable pageable = PageRequest.of(
                filterUsersForNotificationReqDTO.getPage().getPageId(),
                filterUsersForNotificationReqDTO.getPage().getLimit()
        );

        GenderType genderTypeFilter = null;
        boolean checkIsGenderTypeFilter = false;
        ZodiacSignType zodiacSignTypeFilter = null;
        boolean checkIsZodiacSignTypeFilter = false;
        if (!StringUtil.nullOrEmpty(filterUsersForNotificationReqDTO.getFilters().getGenderFilter())) {
            checkIsGenderTypeFilter = true;
            genderTypeFilter = GenderType.valueOf(filterUsersForNotificationReqDTO.getFilters().getGenderFilter());
        }
        if (!StringUtil.nullOrEmpty(filterUsersForNotificationReqDTO.getFilters().getZodiacSignFilter())) {
            checkIsZodiacSignTypeFilter = true;
            zodiacSignTypeFilter = ZodiacSignType.valueOf(filterUsersForNotificationReqDTO.getFilters().getZodiacSignFilter());
        }
        List<FilterUsersForNotificationResDTO> usersList;
        if (checkIsGenderTypeFilter && filterUsersForNotificationReqDTO.getFilters().getZodiacSignFilter() == null && filterUsersForNotificationReqDTO.getFilters().getStartAge() == null && filterUsersForNotificationReqDTO.getFilters().getEndAge() == null) {
            usersList = userRepository.listUsersBasedOnGenderFilter(filterUsersForNotificationReqDTO.getQueryToSearch(), genderTypeFilter.name(), currentDate);
        } else if (genderTypeFilter == null && zodiacSignTypeFilter == null && filterUsersForNotificationReqDTO.getFilters().getStartAge() != null && filterUsersForNotificationReqDTO.getFilters().getEndAge() != null) {
            usersList = userRepository.listUsersBasedOnAgeFilter(filterUsersForNotificationReqDTO.getQueryToSearch(), currentDate, filterUsersForNotificationReqDTO.getFilters().getStartAge(), filterUsersForNotificationReqDTO.getFilters().getEndAge());
        } else if (checkIsZodiacSignTypeFilter && filterUsersForNotificationReqDTO.getFilters().getGenderFilter() == null && filterUsersForNotificationReqDTO.getFilters().getStartAge() == null && filterUsersForNotificationReqDTO.getFilters().getEndAge() == null) {
            usersList = userRepository.listUsersBasedOnZodiacSignFilter(true, filterUsersForNotificationReqDTO.getQueryToSearch(), zodiacSignTypeFilter.name(), currentDate);
        } else if (checkIsGenderTypeFilter && filterUsersForNotificationReqDTO.getFilters().getStartAge() != null && filterUsersForNotificationReqDTO.getFilters().getEndAge() != null && filterUsersForNotificationReqDTO.getFilters().getZodiacSignFilter() == null) {
            usersList = userRepository.listUsersBasedOnGenderAndAgeFilters(true, filterUsersForNotificationReqDTO.getQueryToSearch(), genderTypeFilter.name(),
                    currentDate, filterUsersForNotificationReqDTO.getFilters().getStartAge(), filterUsersForNotificationReqDTO.getFilters().getEndAge());
        } else if (checkIsGenderTypeFilter && checkIsZodiacSignTypeFilter && filterUsersForNotificationReqDTO.getFilters().getStartAge() == null && filterUsersForNotificationReqDTO.getFilters().getEndAge() == null) {
            usersList = userRepository.listUsersBasedOnGenderAndZodiacSignFilters(true, filterUsersForNotificationReqDTO.getQueryToSearch(), genderTypeFilter.name(), currentDate, true, zodiacSignTypeFilter.name());
        } else if (checkIsZodiacSignTypeFilter && filterUsersForNotificationReqDTO.getFilters().getStartAge() != null && filterUsersForNotificationReqDTO.getFilters().getEndAge() != null && filterUsersForNotificationReqDTO.getFilters().getGenderFilter() == null) {
            usersList = userRepository.listUsersBasedOnZodiacSignAndAgeFilters(true, filterUsersForNotificationReqDTO.getQueryToSearch(), zodiacSignTypeFilter.name(), currentDate, filterUsersForNotificationReqDTO.getFilters().getStartAge(), filterUsersForNotificationReqDTO.getFilters().getEndAge());
        } else if (checkIsGenderTypeFilter && checkIsZodiacSignTypeFilter && filterUsersForNotificationReqDTO.getFilters().getStartAge() != null && filterUsersForNotificationReqDTO.getFilters().getEndAge() != null) {
            usersList = userRepository.listUsersBasedOnGenderAndAgeAndZodiacSignFilters(true, true, filterUsersForNotificationReqDTO.getQueryToSearch(), genderTypeFilter.name(), zodiacSignTypeFilter.name(), currentDate, filterUsersForNotificationReqDTO.getFilters().getStartAge(), filterUsersForNotificationReqDTO.getFilters().getEndAge());
        } else {
            usersList = userRepository.listAllUsersForNotifications(filterUsersForNotificationReqDTO.getQueryToSearch(), currentDate);
        }
        // Total records count
        long totalRecords = usersList.size();

        // Convert to a new list for pagination
        List<FilterUsersForNotificationResDTO> paginatedResult = new ArrayList<>(usersList);

        Comparator<FilterUsersForNotificationResDTO> comparator;
        if (filterUsersForNotificationReqDTO.getSortBy() != null && filterUsersForNotificationReqDTO.getSortBy().getProperty() != null) {
            String sortProperty = filterUsersForNotificationReqDTO.getSortBy().getProperty();
            Sort.Direction direction = filterUsersForNotificationReqDTO.getSortBy().getDirection();
            comparator = switch (sortProperty.toLowerCase()) {
                case "name" ->
                        Comparator.comparing(FilterUsersForNotificationResDTO::getName, String.CASE_INSENSITIVE_ORDER);
                case "email" ->
                        Comparator.comparing(FilterUsersForNotificationResDTO::getEmail, String.CASE_INSENSITIVE_ORDER);
                default -> Comparator.comparing(FilterUsersForNotificationResDTO::getCreatedAt);
            };

            if (direction == Sort.Direction.DESC) {
                comparator = comparator.reversed();
            }

            paginatedResult.sort(comparator);
        }

        // Apply pagination manually
        int start = Math.min((int) pageable.getOffset(), paginatedResult.size());
        int end = Math.min(start + pageable.getPageSize(), paginatedResult.size());
        List<FilterUsersForNotificationResDTO> paginatedList = paginatedResult.subList(start, end);
        return new SearchResultDTO<>(paginatedList, totalRecords, filterUsersForNotificationReqDTO.getPage().getLimit());
    }

    @Override
    public FilterUserIdsResDTO filterUserIdsBasedOnNotificationFilters(FilterUserIdsReqDTO filterUserIdsReqDTO) {
        FilterUserIdsResDTO filterUserIdsResDTO = new FilterUserIdsResDTO();
        List<Long> userIds;
        LocalDate currentDate = LocalDate.now(ZoneOffset.UTC);
        GenderType genderTypeFilter = null;
        boolean checkIsGenderTypeFilter = false;
        ZodiacSignType zodiacSignTypeFilter = null;
        boolean checkIsZodiacSignTypeFilter = false;
        if (!StringUtil.nullOrEmpty(filterUserIdsReqDTO.getFilters().getGenderFilter())) {
            checkIsGenderTypeFilter = true;
            genderTypeFilter = GenderType.valueOf(filterUserIdsReqDTO.getFilters().getGenderFilter());
        }
        if (!StringUtil.nullOrEmpty(filterUserIdsReqDTO.getFilters().getZodiacSignFilter())) {
            checkIsZodiacSignTypeFilter = true;
            zodiacSignTypeFilter = ZodiacSignType.valueOf(filterUserIdsReqDTO.getFilters().getZodiacSignFilter());
        }
        List<FilterUsersForNotificationResDTO> usersList;
        if (checkIsGenderTypeFilter && filterUserIdsReqDTO.getFilters().getZodiacSignFilter() == null && filterUserIdsReqDTO.getFilters().getStartAge() == null && filterUserIdsReqDTO.getFilters().getEndAge() == null) {
            usersList = userRepository.listUsersBasedOnGenderFilter(filterUserIdsReqDTO.getQueryToSearch(), genderTypeFilter.name(), currentDate);
            userIds = usersList.stream().distinct().map(FilterUsersForNotificationResDTO::getUserId).toList();
        } else if (genderTypeFilter == null && zodiacSignTypeFilter == null && filterUserIdsReqDTO.getFilters().getStartAge() != null && filterUserIdsReqDTO.getFilters().getEndAge() != null) {
            usersList = userRepository.listUsersBasedOnAgeFilter(filterUserIdsReqDTO.getQueryToSearch(), currentDate, filterUserIdsReqDTO.getFilters().getStartAge(), filterUserIdsReqDTO.getFilters().getEndAge());
            userIds = usersList.stream().distinct().map(FilterUsersForNotificationResDTO::getUserId).toList();
        } else if (checkIsZodiacSignTypeFilter && filterUserIdsReqDTO.getFilters().getGenderFilter() == null && filterUserIdsReqDTO.getFilters().getStartAge() == null && filterUserIdsReqDTO.getFilters().getEndAge() == null) {
            usersList = userRepository.listUsersBasedOnZodiacSignFilter(true, filterUserIdsReqDTO.getQueryToSearch(), zodiacSignTypeFilter.name(), currentDate);
            userIds = usersList.stream().distinct().map(FilterUsersForNotificationResDTO::getUserId).toList();
        } else if (checkIsGenderTypeFilter && filterUserIdsReqDTO.getFilters().getStartAge() != null && filterUserIdsReqDTO.getFilters().getEndAge() != null && filterUserIdsReqDTO.getFilters().getZodiacSignFilter() == null) {
            usersList = userRepository.listUsersBasedOnGenderAndAgeFilters(true, filterUserIdsReqDTO.getQueryToSearch(), genderTypeFilter.name(),
                    currentDate, filterUserIdsReqDTO.getFilters().getStartAge(), filterUserIdsReqDTO.getFilters().getEndAge());
            userIds = usersList.stream().distinct().map(FilterUsersForNotificationResDTO::getUserId).toList();
        } else if (checkIsGenderTypeFilter && checkIsZodiacSignTypeFilter && filterUserIdsReqDTO.getFilters().getStartAge() == null && filterUserIdsReqDTO.getFilters().getEndAge() == null) {
            usersList = userRepository.listUsersBasedOnGenderAndZodiacSignFilters(true, filterUserIdsReqDTO.getQueryToSearch(), genderTypeFilter.name(), currentDate, true, zodiacSignTypeFilter.name());
            userIds = usersList.stream().distinct().map(FilterUsersForNotificationResDTO::getUserId).toList();
        } else if (checkIsZodiacSignTypeFilter && filterUserIdsReqDTO.getFilters().getStartAge() != null && filterUserIdsReqDTO.getFilters().getEndAge() != null && filterUserIdsReqDTO.getFilters().getGenderFilter() == null) {
            usersList = userRepository.listUsersBasedOnZodiacSignAndAgeFilters(true, filterUserIdsReqDTO.getQueryToSearch(), zodiacSignTypeFilter.name(), currentDate, filterUserIdsReqDTO.getFilters().getStartAge(), filterUserIdsReqDTO.getFilters().getEndAge());
            userIds = usersList.stream().distinct().map(FilterUsersForNotificationResDTO::getUserId).toList();
        } else if (checkIsGenderTypeFilter && checkIsZodiacSignTypeFilter && filterUserIdsReqDTO.getFilters().getStartAge() != null && filterUserIdsReqDTO.getFilters().getEndAge() != null) {
            usersList = userRepository.listUsersBasedOnGenderAndAgeAndZodiacSignFilters(true, true, filterUserIdsReqDTO.getQueryToSearch(), genderTypeFilter.name(), zodiacSignTypeFilter.name(), currentDate, filterUserIdsReqDTO.getFilters().getStartAge(), filterUserIdsReqDTO.getFilters().getEndAge());
            userIds = usersList.stream().distinct().map(FilterUsersForNotificationResDTO::getUserId).toList();
        } else {
            usersList = userRepository.listAllUsersForNotifications(filterUserIdsReqDTO.getQueryToSearch(), currentDate);
            userIds = usersList.stream().distinct().map(FilterUsersForNotificationResDTO::getUserId).toList();
        }
        filterUserIdsResDTO.setUserIds(userIds);
        return filterUserIdsResDTO;
    }


    @Override
    @Transactional
    public String activeInactiveUserByAdmin(Long userId) {
        String activeInactiveMsg = "admin_user_activate";
        EntityUser entityUser = userRepository.findByUserIdAndIsDeleted(userId, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        if (entityUser.isActive()) {
            accessTokenRepository.deleteByUserId(userId);
            activeInactiveMsg = "admin_user_inactivate";

            List<String> topicNames = userTopicSubscriptionsRepository
                    .findTopicNamesByUserId(entityUser.getUserId());

            if (topicNames != null && !topicNames.isEmpty()) {
                /** Delete topics subscribed by user **/
                userTopicSubscriptionsRepository.deleteByUserIdAndTopicNameIn(userId, topicNames);
            }

        }
        entityUser.setActive(!entityUser.isActive());
        userRepository.save(entityUser);
        return activeInactiveMsg;
    }

    @Transactional
    @Override
    public ViewUserDetailsResDTO viewUserDetails(Long userId) {
        EntityUser entityUser = userRepository.findByUserId(userId);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        ViewUserDetailsResDTO viewUserDetailsResDTO = getUserAllInformationForCMS(userId);
        // Fetch user question answers

        if (viewUserDetailsResDTO != null) {
            List<EntityUserAnswer> userAnswerList = userAnswerRepository.findAllByUserId(userId);
            List<UserQuestionAnswerDetails> questionAnswerDetails = mapUserAnswers(userAnswerList);
            viewUserDetailsResDTO.setUserQuestionAnswerDetails(questionAnswerDetails);
            EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(entityUser.getUserId(), true, false);
            if (Objects.isNull(entityUserInfo)) {
                throw new BusinessValidationException("user_info_not_found_error");
            }
            List<UserAstrologyDetailResDTO> userAstrologyInfoList = generateUserAstrologyDetails(entityUserInfo);
            viewUserDetailsResDTO.setUserAstrologyDetailResDTOList(userAstrologyInfoList);
            viewUserDetailsResDTO.setUserCurrentConnectionList(listCurrentConnection(userId));
        }
        return viewUserDetailsResDTO;
    }

    @Override
    public void editUserDetails(EditUserDetailsReqDTO editUserDetailsReqDTO) {
        EntityUser entityUser = userRepository.findByUserId(editUserDetailsReqDTO.getUserId());
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsDeleted(entityUser.getUserId(), false);
        if (Objects.isNull(entityUserInfo)) {
            throw new BusinessValidationException("user_info_not_found_error");
        }
        entityUserInfo.setName(editUserDetailsReqDTO.getName());
        userInfoRepository.save(entityUserInfo);
        if (!StringUtil.nullOrEmpty(editUserDetailsReqDTO.getEmail())) {
            entityUser.setEmail(editUserDetailsReqDTO.getEmail());
        }
        if (!StringUtil.nullOrEmpty(editUserDetailsReqDTO.getCountryCode()) && !StringUtil.nullOrEmpty(editUserDetailsReqDTO.getPhoneNumber())) {
            entityUser.setCountryCode(editUserDetailsReqDTO.getCountryCode());
            entityUser.setPhoneNumber(editUserDetailsReqDTO.getPhoneNumber());
        }
        userRepository.save(entityUser);
    }


    private List<UserAstrologyDetailResDTO> generateUserAstrologyDetails(EntityUserInfo entityUserInfo) {
        LocalDateTime date = Methods.convertUtcToUserTimeZone(entityUserInfo.getDateOfBirth(), entityUserInfo.getTimezone());
        double latitude = entityUserInfo.getLatitude();
        double longitude = entityUserInfo.getLongitude();

        // Get time zone with DST if needed
        int timeZoneWithDst = getTimeZoneWithDstIfNeeded(null, latitude, longitude, entityUserInfo.getDateOfBirth());
        List<EntityUserAstrologyInfo> existingAstrologyInfoList = userAstrologyInfoRepository.findByUserIdAndIsActiveAndIsDeleted(entityUserInfo.getUserId(), true, false);
        List<UserAstrologyDetailResDTO> planetDetails = astrologyThirdPartyService.getUserPlanetDetails(new UserAstrologyReqDTO(
                        date.getDayOfMonth(), date.getMonthValue(), date.getYear(),
                        date.getHour(), date.getMinute(), latitude, longitude,
                        timeZoneWithDst, FieldConstant.Astrology.PLACIDUS))
                .stream()
                .filter(dto -> !"Ascendant".equals(dto.getName()))
                .toList();

        if (existingAstrologyInfoList != null && !existingAstrologyInfoList.isEmpty()) {
            Map<String, EntityUserAstrologyInfo> astrologyInfoMap = existingAstrologyInfoList.stream().collect(Collectors.toMap(EntityUserAstrologyInfo::getName, info -> info));
            for (UserAstrologyDetailResDTO userAstrologyDetailResDTO : planetDetails) {
                String planetName = userAstrologyDetailResDTO.getName();
                EntityUserAstrologyInfo userAstrologyInfo = astrologyInfoMap.get(planetName);
                if (StringUtil.nullOrEmpty(userAstrologyInfo.getReport()) && !userAstrologyInfo.getName().equals("Ascendant")) {
                    UserPlanetSignReportResDTO userPlanetSignReportResDTO = astrologyThirdPartyService.getUserPlanetSignReport(new UserAstrologyReqDTO(
                            date.getDayOfMonth(), date.getMonthValue(), date.getYear(),
                            date.getHour(), date.getMinute(), latitude, longitude,
                            timeZoneWithDst, FieldConstant.Astrology.PLACIDUS), planetName);
                    userAstrologyInfo.setReport(userPlanetSignReportResDTO.getReport());
                }
            }
        }
        // Convert the list to DTOs
        return (existingAstrologyInfoList != null && !existingAstrologyInfoList.isEmpty())
                ? existingAstrologyInfoList.stream().map(userAstrologyInfoMapper::toDTO)
                .filter(dto -> !"Ascendant".equals(dto.getName()))
                .toList()
                : new ArrayList<>();
    }

    private Integer getTimeZoneWithDstIfNeeded(Integer timeZoneWithDst, double latitude, double longitude, LocalDateTime date) {
        if (timeZoneWithDst == null) {
            String formattedDate = date.format(DateTimeFormatter.ofPattern(FieldConstant.DATE_PATTERN));
            TimezoneWithDstResDTO timezoneWithDstResDTO = astrologyThirdPartyService.getTimeZoneWithDst(new TimeZoneWithDstReqDTO(latitude, longitude, formattedDate));
            return timezoneWithDstResDTO.getTimezone();
        } else {
            return timeZoneWithDst;
        }
    }

    // Helper method to map user answers to UserQuestionAnswerDetails DTO
    private List<UserQuestionAnswerDetails> mapUserAnswers(List<EntityUserAnswer> userAnswerList) {
        // Group answers by question name
        Map<EntityQuestion, List<String>> questionToAnswersMap = userAnswerList.stream()
                .collect(Collectors.groupingBy(
                        EntityUserAnswer::getEntityQuestion,  // Group by EntityQuestion
                        Collectors.mapping(
                                answer -> answer.getEntityQuestionOption().getOptionName(),
                                Collectors.toList()
                        )
                ));

        // Convert the map to a list of UserQuestionAnswerDetails, sorted by question orderNo
        return questionToAnswersMap.entrySet().stream()
                .sorted(Comparator.comparing(entry -> entry.getKey().getOrderNo()))  // Sort by orderNo
                .map(entry -> new UserQuestionAnswerDetails(
                        entry.getKey().getQuestionName(),  // Get question name from EntityQuestion
                        entry.getValue()                   // List of option names
                ))
                .collect(Collectors.toList());
    }

    private static void validateEmailAndPhoneNumber(String email, String countryCode, String phoneNumber) {
        if ((StringUtil.nonNullNonEmpty(email) && (StringUtil.nonNullNonEmpty(countryCode) || StringUtil.nonNullNonEmpty(phoneNumber))) || (StringUtil.nullOrEmpty(email) && (StringUtil.nullOrEmpty(countryCode) || StringUtil.nullOrEmpty(phoneNumber)))) {
            throw new BusinessValidationException("either_provide_email_or_phone_number");
        }
    }

    @Override
    public HomeScreenResDTO homeScreenList(HomeScreenReqDTO homeScreenReqDTO) {

        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(homeScreenReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(homeScreenReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUserInfo)) {
            throw new BusinessValidationException("user_info_not_found_error");
        }
        /** daily tips */
        List<DailyTipsResSTO> dailyTipsList = dailyTipsRepository.findDailyTipsByZodiacSignWiseForHomeScreen(ZodiacSignType.valueOf(homeScreenReqDTO.getZodiacSign()), 3, true, false);

        /** magazine list */
        String magazineImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getMagazineFolderName();
        String magazineThumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getMagazineThumbnailFolderName();
        List<MagazineDetailResDTO> magazineList = magazineRepository.findMagazineForHomeScreen(6, magazineImagePath, magazineThumbnailImagePath, true, false);

        /** workoutPlaylist list */
        String workoutPlaylistImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutPlaylistFolderName();
        List<UserWorkoutPlaylistResDTO> workoutPlaylist = workoutPlaylistRepository.findWorkoutPlaylistsForHomeScreen(6, workoutPlaylistImagePath, true, false);

        LocalDateTime date = Methods.convertUtcToUserTimeZone(entityUserInfo.getDateOfBirth(), entityUserInfo.getTimezone());
        double latitude = entityUserInfo.getLatitude();
        double longitude = entityUserInfo.getLongitude();

        List<EntityUserAstrologyInfo> existingAstrologyInfoList = userAstrologyInfoRepository.findByUserIdAndIsActiveAndIsDeleted(homeScreenReqDTO.getUserId(), true, false);

        UserAstrologyDetailResDTO userAstrologyDetailResDTO = null;
        if (existingAstrologyInfoList != null && !existingAstrologyInfoList.isEmpty()) {
            EntityUserAstrologyInfo astrologyInfo = existingAstrologyInfoList.get(0);
            if (astrologyInfo.getReport() == null) {
                String formattedDate = entityUserInfo.getDateOfBirth().format(DateTimeFormatter.ofPattern(FieldConstant.DATE_PATTERN));
                TimezoneWithDstResDTO timezoneWithDstResDTO = astrologyThirdPartyService.getTimeZoneWithDst(new TimeZoneWithDstReqDTO(latitude, longitude, formattedDate));

                UserPlanetSignReportResDTO userPlanetSignReportResDTO = astrologyThirdPartyService.getUserPlanetSignReport(new UserAstrologyReqDTO(
                        date.getDayOfMonth(), date.getMonthValue(), date.getYear(),
                        date.getHour(), date.getMinute(), latitude, longitude,
                        timezoneWithDstResDTO.getTimezone(), FieldConstant.Astrology.PLACIDUS), astrologyInfo.getName());
                astrologyInfo.setReport(userPlanetSignReportResDTO.getReport());
                userAstrologyInfoRepository.save(astrologyInfo);
            }
            userAstrologyDetailResDTO = userAstrologyInfoMapper.toDTO(astrologyInfo);
        }

        HomeScreenResDTO homeScreenResDTO = new HomeScreenResDTO();
        homeScreenResDTO.setDailyTipsResSTOList(dailyTipsList);
        homeScreenResDTO.setMagazineDetailResDTOList(magazineList);
        homeScreenResDTO.setWorkoutPlaylistResDTOList(workoutPlaylist);
        homeScreenResDTO.setUserAstrologyDetailResDTO(userAstrologyDetailResDTO);
        homeScreenResDTO.setAchievementActivityType(userAchievementService.findAchievementActivityTypeByUserIdAndIsClaimedAndIsDeleted(homeScreenReqDTO.getUserId(), false, false));
        /** Set Unread Notification Count **/
        homeScreenResDTO.setUnreadNotificationCount(notificationRepository.countUnreadNotificationOfUser(homeScreenReqDTO.getUserId()));
        return homeScreenResDTO;
    }

    @Override
    public ViewMyProfileResDTO viewMyProfile() {
        Long userId = ExecutionContextUtil.getContext().getUserId();
        EntityUser entityUser = userRepository.findByUserId(userId);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        // Retrieve user information for response
        UserSignupResDTO userAllInformation = getUserAllInformation(entityUser.getUserId());
        userAllInformation.setLoginType(ExecutionContextUtil.getContext().getLoginType());

        Integer streakCount = 0;
        Optional<EntityUserStreak> optionalStreak = userStreakRepository.findByUserId(userId);
        if (optionalStreak.isPresent()) {
            streakCount = optionalStreak.get().getStreakCount();
        }

        // Return both the token and user information in the response DTO
        return new ViewMyProfileResDTO(
                userAllInformation.getUserId(),
                userAllInformation.getEmail(),
                userAllInformation.getCountryCode(),
                userAllInformation.getPhoneNumber(),
                userAllInformation.getRoleType(),
                userAllInformation.getUserType(),
                entityUser.getAccountStatus(), // Updated to fetch from entityUser
                userAllInformation.getCreatedAt(),
                userAllInformation.getUpdatedAt(),
                userAllInformation.isDeleted(),
                userAllInformation.isActive(),
                userAllInformation.getName(),
                userAllInformation.getDateOfBirth(),
                userAllInformation.getPlaceOfBirth(),
                userAllInformation.getProfileImage(),
                userAllInformation.getGenderType(),
                userAllInformation.getZodiacSign(),
                userAllInformation.getTimezone(),
                streakCount,
                entityUser.isProfileCompleted(),
                entityUser.getRegisterType(),
                entityUser.isEmailRegistered(),
                userAllInformation.getBorderImage(),
                userAllInformation.getBadgeImage(),
                userAllInformation.getTitle(),
                userAllInformation.getCurrentLevel(),
                userAllInformation.getCurrentPoint(),
                userAllInformation.getCurrentWxp(),
                userAllInformation.getNextLevel(),
                userAllInformation.getNextLevelWxp(),
                userAllInformation.getReferralCode(),
                userAllInformation.getLoginType(),
                userAllInformation.getGoogleAccountName(),
                userAllInformation.getAppleAccountName(),
                userAllInformation.getFacebookAccountName(),
                userAllInformation.isReferralCodeEntered(),
                userAllInformation.getThemeMode()
        );
    }

    @Override
    public EditProfileResDTO editProfile(EditProfileReqDTO editProfileReqDTO) {
        EntityUser entityUser = userRepository.findByUserId(editProfileReqDTO.getUserId());
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(entityUser.getUserId(), true, false);
        if (Objects.isNull(entityUserInfo)) {
            throw new BusinessValidationException("user_info_not_found_error");
        }

        String wheelChartUrl;
        if (!entityUserInfo.getDateOfBirth().equals(editProfileReqDTO.getDateOfBirth()) || !entityUserInfo.getPlaceOfBirth().equals(editProfileReqDTO.getPlaceOfBirth())) {
            wheelChartUrl = astrologyService.updateUserAstrologyDetails(entityUser.getUserId(), editProfileReqDTO.getLatitude(), editProfileReqDTO.getLongitude(), editProfileReqDTO.getDateOfBirth(), entityUserInfo.getTimezone());
            entityUserInfo.setWheelChartUrl(wheelChartUrl);
        }

        /** Editable fields for both ANONYMOUS and NORMAL user **/
        entityUserInfo.setPlaceOfBirth(editProfileReqDTO.getPlaceOfBirth());
        entityUserInfo.setDateOfBirth(editProfileReqDTO.getDateOfBirth());
        entityUserInfo.setGenderType(editProfileReqDTO.getGenderType());
        entityUserInfo.setZodiacSign(Methods.getZodiacSign(editProfileReqDTO.getDateOfBirth(), entityUserInfo.getTimezone()));

        if (entityUser.getUserType().equals(UserType.NORMAL)) {
            entityUserInfo.setName(editProfileReqDTO.getName());

            /** Set profile image if does not exist else delete existing image and upload new profile image **/

//            if (editProfileReqDTO.getProfileImage() != null && !editProfileReqDTO.getProfileImage().isEmpty()) {
//                if (!StringUtil.nullOrEmpty(entityUserInfo.getProfileImage())) {
//                    awss3Service.deleteFile(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getUserProfileFolderName() + entityUserInfo.getProfileImage(), ImageType.profileImage);
//                }
//                UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(editProfileReqDTO.getProfileImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getUserProfileFolderName(), ImageType.profileImage);
//                entityUserInfo.setProfileImage(uploadedFileName.getImageName());
//            }

            /** Editable fields for NORMAL user **/
            if (!StringUtil.nullOrEmpty(entityUser.getEmail())) {
                entityUser.setCountryCode(editProfileReqDTO.getCountryCode());
                entityUser.setPhoneNumber(editProfileReqDTO.getPhoneNumber());
            } else {
                entityUser.setEmail(editProfileReqDTO.getEmail());
            }
        }
        EntityUserInfo savedUserInfo = userInfoRepository.save(entityUserInfo);
        userRepository.save(entityUser);
        return buildEditProfileResponse(savedUserInfo);
    }

    @Override
    public SearchResultDTO<FavoriteListResDTO> favoriteList(FavoriteListReqDTO favoriteListReqDTO) {
        FavoriteModuleType favoriteModuleType = FavoriteModuleType.valueOf(favoriteListReqDTO.getFavoriteModuleType());
        Sort sort = null;
        if (favoriteListReqDTO.getSortBy() != null && favoriteListReqDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(favoriteListReqDTO.getSortBy().getDirection(), favoriteListReqDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(favoriteListReqDTO.getPage().getPageId(), favoriteListReqDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());
        if (FavoriteModuleType.MEDITATION.equals(favoriteModuleType)) {
            return getFavoriteAudios(favoriteListReqDTO, pageable);
        } else if (FavoriteModuleType.WORKOUTS.equals(favoriteModuleType)) {
            return getFavoriteWorkoutPlans(favoriteListReqDTO, pageable);
        } else {
            return null;
        }
    }

    private SearchResultDTO<FavoriteListResDTO> getFavoriteAudios(FavoriteListReqDTO favoriteListReqDTO, Pageable pageable) {
        List<FavoriteListResDTO> favoriteListResDTO = new ArrayList<>();
        long pageCount = audioRepository.countFavoriteAudio(favoriteListReqDTO.getUserId(), true, true, false);
        if (pageCount > 0) {
            String audioFilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioFileFolderName();
            String thumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioThumbnailFolderName();
            List<UserAudioListResDTO> audioResDTOList = audioRepository.findAllFavoriteAudio(favoriteListReqDTO.getUserId(), pageable, audioFilePath, thumbnailImagePath, true, true, false);

//            List<Long> audioIds = audioResDTOList.stream().map(UserAudioListResDTO::getAudioId).collect(Collectors.toList());
//            List<AudioZodiacSignDTO> zodiacSigns = audioZodiacSignRepository.findZodiacSignsByAudioIds(audioIds);
//            Map<Long, List<ZodiacSignType>> zodiacMap = zodiacSigns.stream().collect(Collectors.groupingBy(AudioZodiacSignDTO::getAudioId, Collectors.mapping(AudioZodiacSignDTO::getZodiacSign, Collectors.toList())));
//
//            audioResDTOList.forEach(audio -> audio.setZodiacSigns(zodiacMap.getOrDefault(audio.getAudioId(), new ArrayList<>())));
            favoriteListResDTO.add(new FavoriteListResDTO(audioResDTOList, null));
        }
        return new SearchResultDTO<>(favoriteListResDTO, pageCount, favoriteListReqDTO.getPage().getLimit());
    }

    private SearchResultDTO<FavoriteListResDTO> getFavoriteWorkoutPlans(FavoriteListReqDTO favoriteListReqDTO, Pageable pageable) {
        List<FavoriteListResDTO> favoriteListResDTO = new ArrayList<>();
        long pageCount = workoutPlanRepository.countFavoriteWorkoutPlan(favoriteListReqDTO.getUserId(), true, true, false);
        if (pageCount > 0) {
            String videoPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutVideoFolderName();
            String thumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutVideoThumbnailFolderName();
            List<WorkoutPlanlistDetailResDTO> workoutPlans = workoutPlanRepository.findFavoriteWorkoutPlans(favoriteListReqDTO.getUserId(), pageable, videoPath, thumbnailImagePath, true, true, false);

//            List<Long> workoutIds = workoutPlans.stream().map(WorkoutPlanlistDetailResDTO::getWorkoutPlanId).collect(Collectors.toList());
//            List<WorkoutPlanZodiacSignDTO> zodiacSigns = workoutPlanZodiacRepository.findZodiacSignsByAudioIds(workoutIds);
//            Map<Long, List<ZodiacSignType>> zodiacMap = zodiacSigns.stream().collect(Collectors.groupingBy(WorkoutPlanZodiacSignDTO::getWorkoutPlanId, Collectors.mapping(WorkoutPlanZodiacSignDTO::getZodiacSign, Collectors.toList())));

            workoutPlans.forEach(workoutPlan -> {
                String exerciseVideoPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseVideoFolderName();
                String exerciseThumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseThumbnailFolderName();
                List<ExerciseDetailResDTO> exerciseList = workoutPlanExerciseRepository.findExerciseDetailsByWorkoutPlanIdAndType(workoutPlan.getWorkoutPlanId(), WorkoutPlanType.MULTI_EXERCISE_PLAN, exerciseVideoPath, exerciseThumbnailImagePath);
                workoutPlan.setExerciseList(exerciseList.isEmpty() ? new ArrayList<>() : exerciseList);
            });
            favoriteListResDTO.add(new FavoriteListResDTO(null, workoutPlans));
        }
        return new SearchResultDTO<>(favoriteListResDTO, pageCount, favoriteListReqDTO.getPage().getLimit());
    }


    private EditProfileResDTO buildEditProfileResponse(EntityUserInfo savedUserInfo) {
        UserSignupResDTO userSignupResDTO = getUserAllInformation(savedUserInfo.getUserId());
        userSignupResDTO.setLoginType(ExecutionContextUtil.getContext().getLoginType());
        String accessToken = null;
        String secretToken = null;
        if (userSignupResDTO.getUserType().equals(UserType.NORMAL)) {
            // Fetch the latest access token for the user and role
            Optional<EntityAccessToken> latestAccessToken = accessTokenRepository
                    .findFirstByUserIdAndRoleTypeOrderByCreatedAtDesc(savedUserInfo.getUserId(), RoleType.USER);
            if (latestAccessToken.isPresent() && !StringUtil.nullOrEmpty(latestAccessToken.get().getAccessToken())) {
                accessToken = EncryptUtil.decryptKey(latestAccessToken.get().getAccessToken());
            }
        } else {
            // Fetch the latest secret token for the user and role
            Optional<EntityAccessToken> latestSecretToken = accessTokenRepository
                    .findFirstByUserIdAndRoleTypeOrderByCreatedAtDesc(savedUserInfo.getUserId(), RoleType.USER);
            if (latestSecretToken.isPresent() && !StringUtil.nullOrEmpty(latestSecretToken.get().getSecretToken())) {
                secretToken = latestSecretToken.get().getSecretToken();
            }
        }
        return new EditProfileResDTO(
                userSignupResDTO.getUserId(),
                userSignupResDTO.getEmail(),
                userSignupResDTO.getCountryCode(),
                userSignupResDTO.getPhoneNumber(),
                userSignupResDTO.getRoleType(),
                userSignupResDTO.getUserType(),
                userSignupResDTO.getAccountStatus(),
                userSignupResDTO.getCreatedAt(),
                userSignupResDTO.getUpdatedAt(),
                userSignupResDTO.isDeleted(),
                userSignupResDTO.isActive(),
                userSignupResDTO.getName(),
                userSignupResDTO.getDateOfBirth(),
                userSignupResDTO.getPlaceOfBirth(),
                userSignupResDTO.getProfileImage(),
                userSignupResDTO.getGenderType(),
                userSignupResDTO.getZodiacSign(),
                userSignupResDTO.getTimezone(),
                accessToken,
                secretToken,
                userSignupResDTO.isProfileCompleted(),
                userSignupResDTO.getRegisterType(),
                userSignupResDTO.isEmailRegistered(),
                userSignupResDTO.getReferralCode(),
                userSignupResDTO.getLoginType(),
                userSignupResDTO.getGoogleAccountName(),
                userSignupResDTO.getAppleAccountName(),
                userSignupResDTO.getFacebookAccountName(),
                userSignupResDTO.isReferralCodeEntered(),
                userSignupResDTO.getThemeMode()
        );
    }

    @Override
    public void updateUserTitleByAchievementId(UserIdAchievementIdDTO userIdAchievementIdDTO) {
        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(userIdAchievementIdDTO.getUserId(), true, false);
        if (Objects.isNull(entityUserInfo)) {
            throw new BusinessValidationException("user_info_not_found_error");
        }
        EntityAchievements achievements = achievementsRepository.findByAchievementsIdAndIsDeleted(userIdAchievementIdDTO.getAchievementId(), false);
        entityUserInfo.setTitleAchievementId(achievements);
        userInfoRepository.save(entityUserInfo);
    }

    @Override
    public void updateUserBorderByAchievementId(UserIdAchievementIdDTO userIdAchievementIdDTO) {
        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(userIdAchievementIdDTO.getUserId(), true, false);
        if (Objects.isNull(entityUserInfo)) {
            throw new BusinessValidationException("user_info_not_found_error");
        }
        EntityAchievements achievements = achievementsRepository.findByAchievementsIdAndIsDeleted(userIdAchievementIdDTO.getAchievementId(), false);
        entityUserInfo.setBorderAchievementId(achievements);
        userInfoRepository.save(entityUserInfo);
    }

    @Override
    public void updateUserMedalByAchievementId(UserIdAchievementIdDTO userIdAchievementIdDTO) {
        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(userIdAchievementIdDTO.getUserId(), true, false);
        if (Objects.isNull(entityUserInfo)) {
            throw new BusinessValidationException("user_info_not_found_error");
        }
        EntityAchievements achievements = achievementsRepository.findByAchievementsIdAndIsDeleted(userIdAchievementIdDTO.getAchievementId(), false);
        entityUserInfo.setMedalAchievementId(achievements);
        userInfoRepository.save(entityUserInfo);
        if (!userAchievementService.existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Account_Change_Profile_Badge, userIdAchievementIdDTO.getUserId())) {
            userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Account_Change_Profile_Badge, userIdAchievementIdDTO.getUserId()));

        }


    }

    @Override
    public void deleteUserAccountByAdmin(Long userId) {
        EntityUser entityUser = userRepository.findByUserId(userId);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        entityUser.setDeleted(true);
        userRepository.save(entityUser);

        /** soft delete user from EntityUserInfo table also **/
        EntityUserInfo entityUserInfo = userInfoRepository.findByUserId(entityUser.getUserId());
        entityUserInfo.setDeleted(true);
        userInfoRepository.save(entityUserInfo);
    }

    @Override
    @Transactional
    public void deleteUserAccount(DeactivateUserAccountReqDTO deactivateUserAccountReqDTO) {
        Long userId = ExecutionContextUtil.getContext().getUserId();
        EntityUser entityUser = userRepository.findByUserId(userId);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        // Mark the user's devices as belonging to a deleted account before deleting the user
        userDeviceService.markUserDevicesAsDeletedAccount(userId);

        entityUser.setDeleted(true);
        userRepository.save(entityUser);

        /** soft delete user from EntityUserInfo table also **/
        EntityUserInfo entityUserInfo = userInfoRepository.findByUserId(entityUser.getUserId());
        entityUserInfo.setDeleted(true);
        userInfoRepository.save(entityUserInfo);

        List<String> topicNames = deactivateUserAccountReqDTO.getTopicNames();
        if (topicNames != null && !topicNames.isEmpty()) {
            /** Delete topics subscribed by user **/
            userTopicSubscriptionsRepository.deleteByUserIdAndTopicNameIn(userId, topicNames);
        }
    }

    @Transactional
    @Override
    public void deactivateUserAccount(DeactivateUserAccountReqDTO deactivateUserAccountReqDTO) {
        Long userId = ExecutionContextUtil.getContext().getUserId();
        EntityUser entityUser = userRepository.findByUserIdAndIsDeleted(userId, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        if (entityUser.isActive()) {
            accessTokenRepository.deleteByUserId(userId);
        }
        entityUser.setActive(!entityUser.isActive());
        userRepository.save(entityUser);

        List<String> topicNames = deactivateUserAccountReqDTO.getTopicNames();
        if (topicNames != null && !topicNames.isEmpty()) {
            /** Delete topics subscribed by user **/
            userTopicSubscriptionsRepository.deleteByUserIdAndTopicNameIn(userId, topicNames);
        }
    }

//    @Override
//    public void updateReactivationRequestPending(boolean isPending, Long userId) {
//        EntityUser entityUser = userRepository.findByUserIdAndIsDeleted(userId,false);
//        if (Objects.isNull(entityUser)) {
//            throw new BusinessValidationException("user_not_found_error");
//        }
//        entityUser.setReactivationRequestPending(isPending);
//        userRepository.save(entityUser);
//    }

//    @Override
//    public void updateRequestStatus(RequestActionReqDTO requestActionReqDTO){
//        EntityUser entityUser = userRepository.findByUserIdAndIsDeleted(requestActionReqDTO.getUserId(),false);
//        if (Objects.isNull(entityUser)) {
//            throw new BusinessValidationException("user_not_found_error");
//        }
//
//        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(entityUser.getUserId(), true, false);
//        if (Objects.isNull(entityUserInfo)) {
//            throw new BusinessValidationException("user_info_not_found_error");
//        }
//
//        Map<String, Object> map = new HashMap<>();
//        map.put("name", entityUserInfo.getName());
//        map.put("year", Integer.valueOf(LocalDate.now().getYear()).toString());
//        if(requestActionReqDTO.getRequestAction().equals(RequestActions.APPROVE.toString())){
//            emailService.sendEmail(FieldConstant.EmailTemplateName.APPROVE_USER_ACCOUNT_ACTIVATION_REQUEST,
//                    "MindBodyWarrior - User Account Activation Request",
//                    entityUser.getEmail(),
//                    map);
//            entityUser.setActive(true);
//
//        }else if (requestActionReqDTO.getRequestAction().equals(RequestActions.REJECT.toString())){
//            emailService.sendEmail(FieldConstant.EmailTemplateName.REJECT_USER_ACCOUNT_ACTIVATION_REQUEST,
//                    "MindBodyWarrior - User Account Activation Request",
//                    entityUser.getEmail(),
//                    map);
//            entityUser.setActive(false);
//        }
//        userRepository.save(entityUser);
//        updateReactivationRequestPending(false, requestActionReqDTO.getUserId());
//    }

//    @Override
//    public SearchResultDTO<UserRequestManagementResDTO> listUserRequestManagement(CommonListDTO commonListDTO) {
//        Sort sort = null;
//        if (commonListDTO.getSortBy() != null && commonListDTO.getSortBy().getProperty() != null) {
//
//            String sortProperty = commonListDTO.getSortBy().getProperty();
//            Sort.Direction direction = commonListDTO.getSortBy().getDirection();
//
//            // Example: If sorting by 'name' field in 'user_info' table
//            if (sortProperty.equalsIgnoreCase("name")) {
//                sort = Sort.by(direction, "ui.name"); // Use 'ui.name' for sorting by name
//            } else {
//                sort = Sort.by(direction, sortProperty); // Use other properties as specified
//            }
//        }
//        Pageable pageable = PageRequest.of(commonListDTO.getPage().getPageId(), commonListDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());
//        List<UserRequestManagementResDTO> userList = new ArrayList<>();
//        long pageCount = 0L;
//        UserType userTypeFilter = null;
//        boolean checkIsUserType = false;
//        if (!StringUtil.nullOrEmpty(commonListDTO.getFilters().getSearchFilter())) {
//            checkIsUserType = true;
//            userTypeFilter = UserType.valueOf(commonListDTO.getFilters().getSearchFilter());
//        }
//        pageCount = userRepository.countUsersForRequest(commonListDTO.getQueryToSearch(), checkIsUserType, userTypeFilter);
//        if (pageCount > 0) {
//            Page<UserRequestManagementResDTO> usersPage = userRepository.listUsersRequestManagement(
//                    pageable,
//                    checkIsUserType,
//                    commonListDTO.getQueryToSearch(),
//                    userTypeFilter
//                    );
//            userList = usersPage.getContent();
//        }
//        return new SearchResultDTO<>(userList, pageCount, commonListDTO.getPage().getLimit());
//    }

    @Override
    @Transactional
    public String reActivateUserByEmailId(ForgotPasswordReqDTO forgotPasswordReqDTO) {
        String activeInactiveMsg = "user_activate";
        EntityUser entityUser = userRepository.findByAllEmailAndIsDeleted(forgotPasswordReqDTO.getEmail(), false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        entityUser.setActive(true);
        userRepository.save(entityUser);
        return activeInactiveMsg;
    }

    @Override
    public EntityUser findByUserIdAndIsActiveAndIsDeleted(Long userId, boolean isActive, boolean isDeleted) {
        return userRepository.findByUserIdAndIsActiveAndIsDeleted(userId, isActive, isDeleted);
    }

    @Override
    public List<UserCurrentConnectionListDTO> listCurrentConnection(Long userId){
        Optional<UserCurrentConnectionDTO> userCurrentConnectionDTOOptional = userInfoRepository.listCurrentConnection(userId);
        List<UserCurrentConnectionListDTO> userCurrentConnectionList = new ArrayList<>();
        if(userCurrentConnectionDTOOptional.isPresent()){
            UserCurrentConnectionDTO userCurrentConnectionDTO = userCurrentConnectionDTOOptional.get();
            if(Objects.nonNull(userCurrentConnectionDTO.getFacebookEmailId())){
                userCurrentConnectionList.add(new UserCurrentConnectionListDTO("Facebook",userCurrentConnectionDTO.getFacebookEmailId(),userCurrentConnectionDTO.getFacebookMediaId(),userCurrentConnectionDTO.getFacebookHandleName()));
            }
            if (Objects.nonNull(userCurrentConnectionDTO.getGoogleEmailId())){
                userCurrentConnectionList.add(new UserCurrentConnectionListDTO("Google",userCurrentConnectionDTO.getGoogleEmailId(),userCurrentConnectionDTO.getGoogleMediaId(),userCurrentConnectionDTO.getGoogleHandleName()));
            }
            if (Objects.nonNull(userCurrentConnectionDTO.getAppleEmailId())){
                userCurrentConnectionList.add(new UserCurrentConnectionListDTO("Apple",userCurrentConnectionDTO.getAppleEmailId(),userCurrentConnectionDTO.getAppleMediaId(),userCurrentConnectionDTO.getAppleHandleName()));
            }
        }
        return userCurrentConnectionList;
    }

    @Override
    public void addCurrentConnection(AddCurrentConnectionReqDTO addCurrentConnectionReqDTO){

        boolean isUserExist = userRepository.existsByAllEmailAndIsDeleted(addCurrentConnectionReqDTO.getEmailId(), false, addCurrentConnectionReqDTO.getUserId());
        if (isUserExist) {
            throw new BusinessValidationException("email_already_associate_with_another_user");
        }

        EntityUser entityUser = findByUserIdAndIsActiveAndIsDeleted(addCurrentConnectionReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)){
            throw new BusinessValidationException("user_not_found_error");
        }
        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(entityUser.getUserId(), true, false);
        if (Objects.isNull(entityUserInfo)){
            throw new BusinessValidationException("user_info_not_found_error");
        }
        if(addCurrentConnectionReqDTO.getRegisterType().equals(RegisterType.FACEBOOK.name())){
            entityUser.setFacebookEmailId(addCurrentConnectionReqDTO.getEmailId());
            entityUser.setFacebookSocialMediaId(addCurrentConnectionReqDTO.getSocialMediaId());
            entityUserInfo.setFacebookAccountName(addCurrentConnectionReqDTO.getAccountName());
        }
        else if(addCurrentConnectionReqDTO.getRegisterType().equals(RegisterType.GOOGLE.name())){
            entityUser.setGoogleEmailId(addCurrentConnectionReqDTO.getEmailId());
            entityUser.setGoogleSocialMediaId(addCurrentConnectionReqDTO.getSocialMediaId());
            entityUserInfo.setGoogleAccountName(addCurrentConnectionReqDTO.getAccountName());
        }
        else if(addCurrentConnectionReqDTO.getRegisterType().equals(RegisterType.APPLE.name())){
            entityUser.setAppleEmailId(addCurrentConnectionReqDTO.getEmailId());
            entityUser.setAppleSocialMediaId(addCurrentConnectionReqDTO.getSocialMediaId());
            entityUserInfo.setAppleAccountName(addCurrentConnectionReqDTO.getAccountName());
        }
        userRepository.save(entityUser);
        userInfoRepository.save(entityUserInfo);
    }

    @Override
    public void removeCurrentConnection(RemoveCurrentConnectionReqDTO removeCurrentConnectionReqDTO){
        EntityUser entityUser = findByUserIdAndIsActiveAndIsDeleted(removeCurrentConnectionReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)){
            throw new BusinessValidationException("user_not_found_error");
        }
        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(entityUser.getUserId(), true, false);
        if (Objects.isNull(entityUserInfo)){
            throw new BusinessValidationException("user_info_not_found_error");
        }
        if(removeCurrentConnectionReqDTO.getRegisterType().equals(RegisterType.FACEBOOK.name())){
            entityUser.setFacebookEmailId(null);
            entityUser.setFacebookSocialMediaId(null);
            entityUserInfo.setFacebookAccountName(null);
        }
        else if(removeCurrentConnectionReqDTO.getRegisterType().equals(RegisterType.GOOGLE.name())){
            entityUser.setGoogleEmailId(null);
            entityUser.setGoogleSocialMediaId(null);
            entityUserInfo.setGoogleAccountName(null);
        }
        else if(removeCurrentConnectionReqDTO.getRegisterType().equals(RegisterType.APPLE.name())){
            entityUser.setAppleEmailId(null);
            entityUser.setAppleSocialMediaId(null);
            entityUserInfo.setAppleAccountName(null);
        }
        userRepository.save(entityUser);
        userInfoRepository.save(entityUserInfo);
    }

    @Override
    public UpdateThemeModeResDTO updateThemeMode(UpdateThemeModeReqDTO updateThemeModeReqDTO) {
        EntityUser entityUser = findByUserIdAndIsActiveAndIsDeleted(updateThemeModeReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(entityUser.getUserId(), true, false);
        if (Objects.isNull(entityUserInfo)) {
            throw new BusinessValidationException("user_info_not_found_error");
        }
        
        String themeMode = updateThemeModeReqDTO.getThemeMode().toUpperCase();
        if (!themeMode.equals("LIGHT") && !themeMode.equals("DARK") && !themeMode.equals("SYSTEM_DEFAULT")) {
            throw new BusinessValidationException("invalid_theme_mode");
        }
        
        entityUserInfo.setThemeMode(themeMode);
        userInfoRepository.save(entityUserInfo);
        return new UpdateThemeModeResDTO(themeMode);
    }
}
