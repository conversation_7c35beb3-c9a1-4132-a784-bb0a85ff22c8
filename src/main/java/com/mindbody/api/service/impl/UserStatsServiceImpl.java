package com.mindbody.api.service.impl;

import com.mindbody.api.dto.AllTimeUserStatsReqDTO;
import com.mindbody.api.dto.UserStatsTimeSpentReqDTO;
import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.model.EntityUserInfo;
import com.mindbody.api.model.EntityUserStats;
import com.mindbody.api.repository.UserInfoRepository;
import com.mindbody.api.repository.UserStatsRepository;
import com.mindbody.api.service.AchievementEventService;
import com.mindbody.api.service.UserStatsService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

@Service
@RequiredArgsConstructor
public class UserStatsServiceImpl implements UserStatsService {

    private static final Logger logger = LoggerFactory.getLogger(UserStatsServiceImpl.class);

    private final UserStatsRepository userStatsRepository;
    private final UserInfoRepository userInfoRepository;
    private final AchievementEventService achievementEventService;

    @Override
    public void saveCategoryStatsForUser(UserStatsTimeSpentReqDTO userStatsTimeSpentReqDTO) {

        EntityUserInfo entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(userStatsTimeSpentReqDTO.getUserId(), true, false);

        // Assuming you have the user's timezone ID (e.g., "America/New_York") and userId
        String userTimezone = entityUserInfo.getTimezone();

        // Get the current date in the user's timezone
        ZoneId userZoneId = ZoneId.of(userTimezone);
        ZonedDateTime nowInUserTimeZone = ZonedDateTime.now(userZoneId);

        // Get the start of today in the user's local timezone (midnight)
        ZonedDateTime startOfDayLocal = nowInUserTimeZone.toLocalDate().atStartOfDay(userZoneId);

        // Convert the start of today to UTC (because the records are stored in UTC)
        ZonedDateTime startOfDayUtc = startOfDayLocal.withZoneSameInstant(ZoneId.of("UTC"));

        // Get the end of today in the user's local timezone (midnight of the next day)
        ZonedDateTime endOfDayLocal = startOfDayLocal.plusDays(1);

        // Convert the end of today to UTC
        ZonedDateTime endOfDayUtc = endOfDayLocal.withZoneSameInstant(ZoneId.of("UTC"));
        if(checkIfRecordsExistForToday(userStatsTimeSpentReqDTO.getUserId(),startOfDayUtc.toLocalDateTime(),endOfDayUtc.toLocalDateTime())==1){

            EntityUserStats entityUserStats = userStatsRepository.findByDateBetweenByUserId(startOfDayUtc.toLocalDateTime(),endOfDayUtc.toLocalDateTime(),userStatsTimeSpentReqDTO.getUserId());
            if(userStatsTimeSpentReqDTO.getAchievementCategoryType().equalsIgnoreCase(AchievementCategoryType.Mind.name())){
                entityUserStats.setMindCategoryTimeSpent(entityUserStats.getMindCategoryTimeSpent() + userStatsTimeSpentReqDTO.getMindTimeSpent());
            }else if(userStatsTimeSpentReqDTO.getAchievementCategoryType().equalsIgnoreCase(AchievementCategoryType.Body.name())){
                entityUserStats.setBodyCategoryTimeSpent(entityUserStats.getBodyCategoryTimeSpent() + userStatsTimeSpentReqDTO.getBodyTimeSpent());
            }else if(userStatsTimeSpentReqDTO.getAchievementCategoryType().equalsIgnoreCase(AchievementCategoryType.Warrior.name())){
                entityUserStats.setWxpEarned(entityUserStats.getWxpEarned() + userStatsTimeSpentReqDTO.getWxpEarned());
            }
            userStatsRepository.save(entityUserStats);

            // Check for leaderboard achievements after updating stats
            checkLeaderboardAchievements(userStatsTimeSpentReqDTO.getUserId(), userStatsTimeSpentReqDTO.getAchievementCategoryType());

        }else{
            EntityUserStats entityUserStats = new EntityUserStats();
            entityUserStats.setUserId(userStatsTimeSpentReqDTO.getUserId());
            if(userStatsTimeSpentReqDTO.getAchievementCategoryType().equals(AchievementCategoryType.Mind.name())){
                entityUserStats.setMindCategoryTimeSpent(userStatsTimeSpentReqDTO.getMindTimeSpent());
                entityUserStats.setBodyCategoryTimeSpent(0L);
                entityUserStats.setWxpEarned(0L);
            }else if(userStatsTimeSpentReqDTO.getAchievementCategoryType().equals(AchievementCategoryType.Body.name())){
                entityUserStats.setMindCategoryTimeSpent(0L);
                entityUserStats.setBodyCategoryTimeSpent(userStatsTimeSpentReqDTO.getBodyTimeSpent());
                entityUserStats.setWxpEarned(0L);
            }else if(userStatsTimeSpentReqDTO.getAchievementCategoryType().equals(AchievementCategoryType.Warrior.name())){
                entityUserStats.setMindCategoryTimeSpent(0L);
                entityUserStats.setBodyCategoryTimeSpent(0L);
                entityUserStats.setWxpEarned(userStatsTimeSpentReqDTO.getWxpEarned());
            }
            entityUserStats.setDate(LocalDateTime.now());
            userStatsRepository.save(entityUserStats);

            // Check for leaderboard achievements after creating new stats
            checkLeaderboardAchievements(userStatsTimeSpentReqDTO.getUserId(), userStatsTimeSpentReqDTO.getAchievementCategoryType());
        }
    }


    @Override
    public long checkIfRecordsExistForToday(Long userId, LocalDateTime startOfDayUtc, LocalDateTime endOfDayUtc){
        return userStatsRepository.checkIfRecordsExistForToday(userId, startOfDayUtc, endOfDayUtc);
    }

    /**
     * Checks if the user qualifies for any leaderboard achievements based on their updated stats.
     * This method is called after a user's stats are updated.
     *
     * @param userId The ID of the user.
     * @param achievementCategoryType The category type that was updated.
     */
    private void checkLeaderboardAchievements(Long userId, String achievementCategoryType) {
        logger.info("Checking leaderboard achievements for user: {}, category: {}", userId, achievementCategoryType);

        try {
            // Get the user's all-time stats
            AllTimeUserStatsReqDTO allTimeStats = userStatsRepository.findAllTimeStatsByUserId(userId);
            if (allTimeStats == null) {
                logger.warn("No all-time stats found for user: {}", userId);
                return;
            }

            // Check for mind category leaderboard achievements
            if (AchievementCategoryType.Mind.name().equalsIgnoreCase(achievementCategoryType)) {
                checkMindLeaderboardAchievements(userId, allTimeStats.getMindCategoryTimeSpent());
            }

            // Check for body category leaderboard achievements
            else if (AchievementCategoryType.Body.name().equalsIgnoreCase(achievementCategoryType)) {
                checkBodyLeaderboardAchievements(userId, allTimeStats.getBodyCategoryTimeSpent());
            }

            // Check for warrior category leaderboard achievements
            else if (AchievementCategoryType.Warrior.name().equalsIgnoreCase(achievementCategoryType)) {
                checkWarriorLeaderboardAchievements(userId, allTimeStats.getWxpEarned());
            }

        } catch (Exception e) {
            logger.error("Error checking leaderboard achievements for user: {}", userId, e);
        }
    }

    /**
     * Checks if the user qualifies for mind leaderboard achievements.
     *
     * @param userId The ID of the user.
     * @param mindTimeSpent The user's total mind category time spent.
     */
    private void checkMindLeaderboardAchievements(Long userId, long mindTimeSpent) {
        // Skip if the user has no mind time spent
        if (mindTimeSpent <= 0) {
            return;
        }

        // Count users with higher mind time spent
        Long usersWithHigherMindTime = userStatsRepository.countUsersWithHigherMindTimeSpent(userId);
        if (usersWithHigherMindTime == null) {
            usersWithHigherMindTime = 0L;
        }

        // Calculate the user's rank (1-based)
        long rank = usersWithHigherMindTime + 1;
        logger.info("User {} has rank {} on mind leaderboard", userId, rank);

        // Check for leaderboard achievements
        submitLeaderboardAchievements(userId, rank);
    }

    /**
     * Checks if the user qualifies for body leaderboard achievements.
     *
     * @param userId The ID of the user.
     * @param bodyTimeSpent The user's total body category time spent.
     */
    private void checkBodyLeaderboardAchievements(Long userId, long bodyTimeSpent) {
        // Skip if the user has no body time spent
        if (bodyTimeSpent <= 0) {
            return;
        }

        // Count users with higher body time spent
        Long usersWithHigherBodyTime = userStatsRepository.countUsersWithHigherBodyTimeSpent(userId);
        if (usersWithHigherBodyTime == null) {
            usersWithHigherBodyTime = 0L;
        }

        // Calculate the user's rank (1-based)
        long rank = usersWithHigherBodyTime + 1;
        logger.info("User {} has rank {} on body leaderboard", userId, rank);

        // Check for leaderboard achievements
        submitLeaderboardAchievements(userId, rank);
    }

    /**
     * Checks if the user qualifies for warrior leaderboard achievements.
     *
     * @param userId The ID of the user.
     * @param wxpEarned The user's total WXP earned.
     */
    private void checkWarriorLeaderboardAchievements(Long userId, long wxpEarned) {
        // Skip if the user has no WXP earned
        if (wxpEarned <= 0) {
            return;
        }

        // Count users with higher WXP earned
        Long usersWithHigherWxp = userStatsRepository.countUsersWithHigherWxpEarned(userId);
        if (usersWithHigherWxp == null) {
            usersWithHigherWxp = 0L;
        }

        // Calculate the user's rank (1-based)
        long rank = usersWithHigherWxp + 1;
        logger.info("User {} has rank {} on warrior leaderboard", userId, rank);

        // Check for leaderboard achievements
        submitLeaderboardAchievements(userId, rank);
    }

    /**
     * Submits leaderboard achievements based on the user's rank.
     *
     * @param userId The ID of the user.
     * @param rank The user's rank on the leaderboard.
     */
    private void submitLeaderboardAchievements(Long userId, long rank) {
        // Delegate to the achievement event service to process leaderboard achievements
        achievementEventService.processLeaderboardAchievement(userId, rank);
    }
}
