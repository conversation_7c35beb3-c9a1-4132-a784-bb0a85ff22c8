package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.RedeemableAudioStatusDTO;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.EntityAudio;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.model.EntityUserPointsTracker;
import com.mindbody.api.model.EntityUserRedeemedAudio;
import com.mindbody.api.repository.AudioRepository;
import com.mindbody.api.repository.UserPointsTrackerRepository;
import com.mindbody.api.repository.UserRedeemedAudioRepository;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.UserRedeemableAudioService;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class UserRedeemableAudioServiceImpl extends BaseService implements UserRedeemableAudioService {

    private final UserRepository userRepository;
    private final AudioRepository audioRepository;
    private final UserRedeemedAudioRepository userRedeemedAudioRepository;
    private final UserPointsTrackerRepository userPointsTrackerRepository;
    private GlobalConfiguration globalConfiguration;

    public UserRedeemableAudioServiceImpl(
            MessageService messageService,
            UserRepository userRepository,
            AudioRepository audioRepository,
            UserRedeemedAudioRepository userRedeemedAudioRepository,
            UserPointsTrackerRepository userPointsTrackerRepository,
            GlobalConfiguration globalConfiguration) {
        super(messageService);
        this.userRepository = userRepository;
        this.audioRepository = audioRepository;
        this.userRedeemedAudioRepository = userRedeemedAudioRepository;
        this.userPointsTrackerRepository = userPointsTrackerRepository;
        this.globalConfiguration = globalConfiguration;
    }

    @Override
    public List<RedeemableAudioStatusDTO> getRedeemableAudiosWithStatus(Long userId, String queryToSearch, Pageable pageable) {
        EntityUser entityUser = validateUser(userId);
        EntityUserPointsTracker pointsTracker = userPointsTrackerRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false)
                .orElseThrow(() -> new BusinessValidationException("user_points_not_found"));

        List<EntityAudio> redeemableAudios = audioRepository.findRedeemableAudios(
                queryToSearch,
                pageable,
                true,
                true,
                false
        );

        return mapToRedeemableAudioStatusDTOs(redeemableAudios, entityUser, pointsTracker);
    }

    @Override
    public List<RedeemableAudioStatusDTO> getLockedRedeemableAudios(Long userId, String queryToSearch, Pageable pageable) {
        EntityUser entityUser = validateUser(userId);
        EntityUserPointsTracker pointsTracker = userPointsTrackerRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false)
                .orElseThrow(() -> new BusinessValidationException("user_points_not_found"));

        List<EntityAudio> redeemableAudios = audioRepository.findRedeemableAudios(
                queryToSearch,
                pageable,
                true,
                true,
                false
        );

        List<RedeemableAudioStatusDTO> allAudios = mapToRedeemableAudioStatusDTOs(redeemableAudios, entityUser, pointsTracker);
        return allAudios.stream()
                .filter(audio -> !audio.isRedeemed())
                .toList();
    }

    @Override
    public List<RedeemableAudioStatusDTO> getRedeemedAudios(Long userId, String queryToSearch, Pageable pageable) {
        EntityUser entityUser = validateUser(userId);
        EntityUserPointsTracker pointsTracker = userPointsTrackerRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false)
                .orElseThrow(() -> new BusinessValidationException("user_points_not_found"));

        List<EntityAudio> redeemableAudios = audioRepository.findRedeemableAudios(
                queryToSearch,
                pageable,
                true,
                true,
                false
        );

        List<RedeemableAudioStatusDTO> allAudios = mapToRedeemableAudioStatusDTOs(redeemableAudios, entityUser, pointsTracker);
        return allAudios.stream()
                .filter(RedeemableAudioStatusDTO::isRedeemed)
                .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void redeemAudio(Long userId, Long audioId) {
        EntityUser entityUser = validateUser(userId);
        EntityAudio entityAudio = validateAudio(audioId);

        // Check if audio is already redeemed
        if (userRedeemedAudioRepository.isAudioRedeemedByUser(userId, audioId, true, false)) {
            throw new BusinessValidationException("audio_already_redeemed");
        }

        // Check if audio is redeemable
        if (!entityAudio.isRedeemable()) {
            throw new BusinessValidationException("audio_not_redeemable");
        }

        // Get user's points
        EntityUserPointsTracker pointsTracker = userPointsTrackerRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false)
                .orElseThrow(() -> new BusinessValidationException("user_points_not_found"));

        // Check if user has enough points
        if (pointsTracker.getTotalPoints() < entityAudio.getPoints()) {
            throw new BusinessValidationException("insufficient_points");
        }

        // Deduct points and save
        pointsTracker.setTotalPoints(pointsTracker.getTotalPoints() - entityAudio.getPoints());
        userPointsTrackerRepository.save(pointsTracker);

        // Create redeemed audio record
        EntityUserRedeemedAudio redeemedAudio = new EntityUserRedeemedAudio();
        redeemedAudio.setEntityUser(entityUser);
        redeemedAudio.setUserId(userId);
        redeemedAudio.setEntityAudio(entityAudio);
        redeemedAudio.setAudioId(audioId);
        redeemedAudio.setPointsSpent(entityAudio.getPoints());
        userRedeemedAudioRepository.save(redeemedAudio);
    }

    private EntityUser validateUser(Long userId) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found");
        }
        return entityUser;
    }

    private EntityAudio validateAudio(Long audioId) {
        EntityAudio entityAudio = audioRepository.findByAudioIdAndIsActiveAndIsDeleted(audioId, true, false);
        if (Objects.isNull(entityAudio)) {
            throw new BusinessValidationException("audio_not_found");
        }
        return entityAudio;
    }

    private List<RedeemableAudioStatusDTO> mapToRedeemableAudioStatusDTOs(
            List<EntityAudio> audios,
            EntityUser user,
            EntityUserPointsTracker pointsTracker) {
        List<RedeemableAudioStatusDTO> dtos = new ArrayList<>();
        String audioFilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioFileFolderName();
        String thumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioThumbnailFolderName();
        
        for (EntityAudio audio : audios) {
            if (!audio.isRedeemable()) {
                continue;
            }

            RedeemableAudioStatusDTO dto = new RedeemableAudioStatusDTO();
            dto.setAudioId(audio.getAudioId());
            dto.setTitle(audio.getTitle());
            dto.setDescription(audio.getDescription());
            dto.setAudioPlaylistType(audio.getAudioPlaylistType());
            dto.setThumbnailImage(StringUtil.nonNullNonEmpty(audio.getThumbnailImage()) ? thumbnailImagePath + audio.getThumbnailImage() : null);
            dto.setAudioFile(StringUtil.nonNullNonEmpty(audio.getAudioFile()) ? audioFilePath + audio.getAudioFile() : null);
            dto.setAudioPlaylistId(audio.getAudioPlaylistId());
            dto.setAudioPlaylistName(audio.getEntityAudioPlaylist().getAudioPlaylistName());
            dto.setCreatedAt(audio.getCreatedAt());
            dto.setUpdatedAt(audio.getUpdatedAt());
            dto.setDeleted(audio.isDeleted());
            dto.setActive(audio.isActive());
            dto.setRedeemable(audio.isRedeemable());
            dto.setPoints(audio.getPoints());

            // Check if user has already redeemed this audio
            boolean isRedeemed = userRedeemedAudioRepository.isAudioRedeemedByUser(
                    user.getUserId(),
                    audio.getAudioId(),
                    true,
                    false
            );
            dto.setRedeemed(isRedeemed);

            // Check if user has enough points to redeem
            boolean canRedeem = !isRedeemed && pointsTracker.getTotalPoints() >= audio.getPoints();
            dto.setCanRedeem(canRedeem);

            dtos.add(dto);
        }

        return dtos;
    }
} 