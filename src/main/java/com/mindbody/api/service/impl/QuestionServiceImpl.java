package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.*;
import com.mindbody.api.dto.cms.AddOptionReqDTO;
import com.mindbody.api.enums.ImageType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.QuestionOptionMapper;
import com.mindbody.api.model.EntityQuestion;
import com.mindbody.api.model.EntityQuestionOption;
import com.mindbody.api.model.EntityUserAnswer;
import com.mindbody.api.repository.QuestionOptionRepository;
import com.mindbody.api.repository.QuestionRepository;
import com.mindbody.api.repository.UserAnswerRepository;
import com.mindbody.api.service.AWSS3Service;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.QuestionService;
import com.mindbody.api.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class QuestionServiceImpl extends BaseService implements QuestionService {

    private final QuestionRepository questionRepository;

    private final QuestionOptionRepository questionOptionRepository;

    private final QuestionOptionMapper questionOptionMapper;

    private final AWSS3Service awss3Service;

    private final GlobalConfiguration globalConfiguration;

    private final UserAnswerRepository userAnswerRepository;

    private static final String SVG = "svg";


    public QuestionServiceImpl(MessageService messageService, QuestionRepository questionRepository, QuestionOptionRepository questionOptionRepository, QuestionOptionMapper questionOptionMapper, AWSS3Service awss3Service, GlobalConfiguration globalConfiguration, UserAnswerRepository userAnswerRepository) {
        super(messageService);
        this.questionRepository = questionRepository;
        this.questionOptionRepository = questionOptionRepository;
        this.questionOptionMapper = questionOptionMapper;
        this.awss3Service = awss3Service;
        this.globalConfiguration = globalConfiguration;
        this.userAnswerRepository = userAnswerRepository;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public QuestionOptionResDTO addQuestion(AddQuestionReqDTO addQuestionReqDTO) {
        Optional<EntityQuestion> entityQuestion = questionRepository.findByQuestionNameAndIsActiveAndIsDeleted(addQuestionReqDTO.getQuestionName(), true, false);
        if (entityQuestion.isPresent()) {
            throw new BusinessValidationException("question_already_exists");
        }

        EntityQuestion question = questionOptionMapper.toModel(addQuestionReqDTO);

        // Fetch the latest question based on orderNo and set the new orderNo
        Optional<EntityQuestion> lastQuestion = questionRepository.findTopByIsActiveAndIsDeletedOrderByOrderNoDesc(true,false);
        if (lastQuestion.isPresent()) {
            // Set the new orderNo to be the last orderNo + 1
            question.setOrderNo(lastQuestion.get().getOrderNo() + 1);
        } else {
            // If no previous question exists, start with orderNo 1
            question.setOrderNo(1);
        }
        question = questionRepository.save(question);

        List<EntityQuestionOption> entityQuestionOptionList = new ArrayList<>();
        if (addQuestionReqDTO.getOptionList() != null && !addQuestionReqDTO.getOptionList().isEmpty()) {
            List<AddOptionReqDTO> optionList = addQuestionReqDTO.getOptionList();

            for (AddOptionReqDTO option : optionList) {

                EntityQuestionOption entityQuestionOption = new EntityQuestionOption();
                entityQuestionOption.setEntityQuestion(question);
                entityQuestionOption.setQuestionId(question.getQuestionId());
                entityQuestionOption.setOptionName(option.getOptionName());

                if (option.getOptionImage() != null && !option.getOptionImage().isEmpty()) {
                    String extension = awss3Service.getFileExtension(option.getOptionImage().getOriginalFilename());
//                    if (!extension.equalsIgnoreCase(SVG)) {
//                        throw new BusinessValidationException("image_extension_not_supported");
//                    }
                    UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(option.getOptionImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getQuestionOptionFolderName(), ImageType.questionOptionImage);
                    entityQuestionOption.setOptionImage(uploadedFileName.getImageName());
                }
                entityQuestionOptionList.add(entityQuestionOption);
            }
            entityQuestionOptionList = questionOptionRepository.saveAll(entityQuestionOptionList);
        }
        QuestionOptionResDTO questionOptionResDTO = questionOptionMapper.toDTOFromEntityQuestion(question);

        List<OptionResDTO> optionResDTOList = new ArrayList<>();
        if (entityQuestionOptionList != null && !entityQuestionOptionList.isEmpty()) {
            for (EntityQuestionOption entityQuestionOption : entityQuestionOptionList) {
                OptionResDTO optionResDTO = questionOptionMapper.toDTOFromEntityQuestionOption(entityQuestionOption);
                if (StringUtil.nonNullNonEmpty(entityQuestionOption.getOptionImage())) {
                    optionResDTO.setOptionImage(globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getQuestionOptionFolderName() + optionResDTO.getOptionImage());
                }
                optionResDTOList.add(optionResDTO);
            }
        }
        questionOptionResDTO.setOptionList(optionResDTOList);
        return questionOptionResDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public QuestionOptionResDTO editQuestion(EditQuestionReqDTO editQuestionReqDTO) {
        EntityQuestion entityQuestion = questionRepository.findByQuestionId(editQuestionReqDTO.getQuestionId());
        if (Objects.isNull(entityQuestion)) {
            throw new BusinessValidationException("question_not_found");
        }

        Optional<EntityQuestion> optionalEntityQuestion = questionRepository.findByQuestionNameAndQuestionIdNotAndIsActiveAndIsDeleted(editQuestionReqDTO.getQuestionName(), editQuestionReqDTO.getQuestionId(), true, false);
        if (optionalEntityQuestion.isPresent()) {
            throw new BusinessValidationException("question_already_exists");
        }

        entityQuestion.setQuestionName(editQuestionReqDTO.getQuestionName());
        entityQuestion.setQuestionTitle(editQuestionReqDTO.getQuestionTitle());
        entityQuestion = questionRepository.save(entityQuestion);

        List<String> deleteImageList = new ArrayList<>();
        List<EntityQuestionOption> entityQuestionOptionList = new ArrayList<>();

        /**
         * delete old options
         */
        //TODO need to add one check if we are delete the option
        if (editQuestionReqDTO.getDeleteOptionList() != null && !editQuestionReqDTO.getDeleteOptionList().isEmpty()) {
            for (DeleteOptionReqDTO deleteOptionReqDTO : editQuestionReqDTO.getDeleteOptionList()) {
                Optional<EntityQuestionOption> entityQuestionOption = questionOptionRepository.findByOptionIdAndIsActiveAndIsDeleted(deleteOptionReqDTO.getOptionId(), true, false);
                if (entityQuestionOption.isPresent()) {
                    if (StringUtil.nonNullNonEmpty(entityQuestionOption.get().getOptionImage())) {
                        deleteImageList.add(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getQuestionOptionFolderName() + entityQuestionOption.get().getOptionImage());
                    }
                    entityQuestionOption.get().setOptionImage(null);
                    entityQuestionOption.get().setDeleted(true);
                    entityQuestionOptionList.add(entityQuestionOption.get());
                }
            }
        }

        if (editQuestionReqDTO.getOptionList() != null && !editQuestionReqDTO.getOptionList().isEmpty()) {
            for (EditOptionReqDTO editOptionReqDTO : editQuestionReqDTO.getOptionList()) {

                if (Objects.nonNull(editOptionReqDTO.getOptionImage()) && StringUtil.nonNullNonEmpty(editOptionReqDTO.getExistingOptionImage())) {
                    throw new BusinessValidationException("question_option_image_error");
                }
                /** update the existing the option */
                EntityQuestionOption entityQuestionOption = questionOptionRepository.findByOptionId(editOptionReqDTO.getOptionId());
                if (Objects.nonNull(entityQuestionOption)) {
                    entityQuestionOption.setOptionName(editOptionReqDTO.getOptionName());
                    entityQuestionOption.setEntityQuestion(entityQuestion);
                    entityQuestionOption.setQuestionId(editQuestionReqDTO.getQuestionId());

                    if (StringUtil.nullOrEmpty(editOptionReqDTO.getExistingOptionImage())) {
                        if (StringUtil.nonNullNonEmpty(entityQuestionOption.getOptionImage())) {
                            deleteImageList.add(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getQuestionOptionFolderName() + entityQuestionOption.getOptionImage());
                            entityQuestionOption.setOptionImage(null);
                        }
                        if (editOptionReqDTO.getOptionImage() != null && !editOptionReqDTO.getOptionImage().isEmpty()) {
                            String extension = awss3Service.getFileExtension(editOptionReqDTO.getOptionImage().getOriginalFilename());
//                            if (!extension.equalsIgnoreCase(SVG)) {
//                                throw new BusinessValidationException("image_extension_not_supported");
//                            }
                            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(editOptionReqDTO.getOptionImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getQuestionOptionFolderName(), ImageType.questionOptionImage);
                            entityQuestionOption.setOptionImage(uploadedFileName.getImageName());
                        }
                    }
                }
                /** add new question option */
                else {
                    entityQuestionOption = new EntityQuestionOption();
                    entityQuestionOption.setOptionName(editOptionReqDTO.getOptionName());
                    entityQuestionOption.setEntityQuestion(entityQuestion);
                    entityQuestionOption.setQuestionId(editQuestionReqDTO.getQuestionId());
                    if (editOptionReqDTO.getOptionImage() != null && !editOptionReqDTO.getOptionImage().isEmpty()) {
                        String extension = awss3Service.getFileExtension(editOptionReqDTO.getOptionImage().getOriginalFilename());
//                        if (!extension.equalsIgnoreCase(SVG)) {
//                            throw new BusinessValidationException("image_extension_not_supported");
//                        }
                        UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(editOptionReqDTO.getOptionImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getQuestionOptionFolderName(), ImageType.questionOptionImage);
                        entityQuestionOption.setOptionImage(uploadedFileName.getImageName());
                    }
                }
                entityQuestionOptionList.add(entityQuestionOption);
            }
        }
        /** delete option images from s3 */
        if (deleteImageList != null && !deleteImageList.isEmpty()) {
            awss3Service.deleteFiles(deleteImageList);
        }
        entityQuestionOptionList = questionOptionRepository.saveAll(entityQuestionOptionList);

        QuestionOptionResDTO questionOptionResDTO = questionOptionMapper.toDTOFromEntityQuestion(entityQuestion);
        List<OptionResDTO> optionResDTOList = new ArrayList<>();
        if (entityQuestionOptionList != null && !entityQuestionOptionList.isEmpty()) {
            /** set only non-deleted option in response */
            List<EntityQuestionOption> filteredEntityQuestionOptionList = entityQuestionOptionList.stream().filter(e -> !e.isDeleted()).toList();
            for (EntityQuestionOption entityQuestionOption : filteredEntityQuestionOptionList) {
                OptionResDTO optionResDTO = questionOptionMapper.toDTOFromEntityQuestionOption(entityQuestionOption);
                if (StringUtil.nonNullNonEmpty(entityQuestionOption.getOptionImage())) {
                    optionResDTO.setOptionImage(globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getQuestionOptionFolderName() + optionResDTO.getOptionImage());
                }
                optionResDTOList.add(optionResDTO);
            }
        }
        questionOptionResDTO.setOptionList(optionResDTOList);
        return questionOptionResDTO;
    }

    @Override
    public SearchResultDTO<QuestionOptionResDTO> listQuestion(CommonListDTO commonListDTO, boolean checkIsActiveRecord) {
        Sort sort = null;
        if (commonListDTO.getSortBy() != null && commonListDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(commonListDTO.getSortBy().getDirection(), commonListDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(commonListDTO.getPage().getPageId(),
                commonListDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());

        Map<Long, QuestionOptionResDTO> map = new HashMap<>();
        long pageCount = 0L;
        pageCount = questionRepository.countQuestions(commonListDTO.getQueryToSearch(), checkIsActiveRecord, false);
        if (pageCount > 0) {
            String imagePrefix = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getQuestionOptionFolderName();
            List<QuestionOptionResDTO> questionList = questionRepository.findAllQuestionsWithOptions(commonListDTO.getQueryToSearch(), imagePrefix, checkIsActiveRecord, false);
            for (QuestionOptionResDTO question : questionList) {
                Long questionId = question.getQuestionId();
                if (!map.containsKey(questionId)) {
                    map.put(questionId, question);
                } else {
                    map.get(questionId).getOptionList().addAll(question.getOptionList());
                }
            }
        }
        List<QuestionOptionResDTO> paginatedResult = new ArrayList<>(map.values());
        int start = Math.min((int) pageable.getOffset(), paginatedResult.size());
        int end = Math.min((start + pageable.getPageSize()), paginatedResult.size());
        List<QuestionOptionResDTO> paginatedList = paginatedResult.subList(start, end);
        Comparator<QuestionOptionResDTO> comparator =  applySorting(sort,QuestionOptionResDTO.class);
        if (comparator != null) {
            paginatedList.sort(comparator);
        }
        return new SearchResultDTO<>(paginatedList, pageCount, commonListDTO.getPage().getLimit());
    }

    @Override
    public void deleteQuestion(Long questionId) {
        EntityQuestion entityQuestion = questionRepository.findByQuestionId(questionId);
        if (Objects.isNull(entityQuestion)) {
            throw new BusinessValidationException("question_not_found");
        }
        List<EntityUserAnswer> userAnswerList = userAnswerRepository.findAllByQuestionId(questionId);
        if(!userAnswerList.isEmpty()){
            throw new BusinessValidationException("question_can_not_delete");
        }

        List<String> deleteImageList = new ArrayList<>();
        List<EntityQuestionOption> entityQuestionOptionList = new ArrayList<>();

        List<EntityQuestionOption> questionOptionList = questionOptionRepository.findAllByQuestionIdAndIsActiveAndIsDeleted(questionId,true,false);
        for(EntityQuestionOption entityQuestionOption : questionOptionList){
             List<EntityUserAnswer> userAnswerListForQuestionOption = userAnswerRepository.findAllByEntityQuestionOption_OptionId(entityQuestionOption.getOptionId());
             if(!userAnswerListForQuestionOption.isEmpty()){
                 throw new BusinessValidationException("question_option_can_not_delete");
             }
             if (StringUtil.nonNullNonEmpty(entityQuestionOption.getOptionImage())) {
                    deleteImageList.add(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getQuestionOptionFolderName() + entityQuestionOption.getOptionImage());
                }
                entityQuestionOption.setOptionImage(null);
                entityQuestionOption.setDeleted(true);
                entityQuestionOption.setActive(false);
                entityQuestionOptionList.add(entityQuestionOption);
        }

        /** delete option images from s3 */
        if (deleteImageList != null && !deleteImageList.isEmpty()) {
            awss3Service.deleteFiles(deleteImageList);
        }

        //updating all question options
        questionOptionRepository.saveAll(entityQuestionOptionList);

        //Delete question from question table
        entityQuestion.setDeleted(true);
        entityQuestion.setActive(false);
        questionRepository.save(entityQuestion);

    }

    @Override
    public void changeQuestionOrder(List<ChangeQuestionOrderReqDTO> changeQuestionOrderReqDTOList) {
        if (CollectionUtils.isEmpty(changeQuestionOrderReqDTOList)) {
            throw new BusinessValidationException("question_order_list_required");
        }
        List<Long> questionIdList = changeQuestionOrderReqDTOList.stream().map(ChangeQuestionOrderReqDTO::getQuestionId).toList();
        List<EntityQuestion> entityQuestionList = questionRepository.findAllQuestionByQuestionIdList(questionIdList, true, false);

        boolean isValidQuestions = CollectionUtils.isEqualCollection(questionIdList, entityQuestionList.stream().map(EntityQuestion::getQuestionId).toList());
        if (!isValidQuestions) {
            throw new BusinessValidationException("some_question_id_not_found");
        }
        Map<Long, Integer> questionOrderMap = changeQuestionOrderReqDTOList.stream()
                .collect(Collectors.toMap(ChangeQuestionOrderReqDTO::getQuestionId, ChangeQuestionOrderReqDTO::getOrderNo));

        entityQuestionList.forEach(question -> {
            Integer newOrderNo = questionOrderMap.get(question.getQuestionId());
            if (newOrderNo != null) {
                question.setOrderNo(newOrderNo);
            }
        });
        questionRepository.saveAll(entityQuestionList);
    }
}
