package com.mindbody.api.service.impl;

import com.mindbody.api.dto.shopify.*;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.ShopifyProductService;
import com.mindbody.api.service.ShopifyProductThirdPartyService;
import org.springframework.stereotype.Service;

@Service
public class ShopifyProductServiceImpl extends BaseService implements ShopifyProductService {

    private final ShopifyProductThirdPartyService shopifyProductThirdPartyService;

    public ShopifyProductServiceImpl(MessageService messageService, ShopifyProductThirdPartyService shopifyProductThirdPartyService) {
        super(messageService);
        this.shopifyProductThirdPartyService = shopifyProductThirdPartyService;
    }

    @Override
    public ProductListResDTO getAllProductList(ProductListReqDTO productListReqDTO) {
        return shopifyProductThirdPartyService.getAllProductList(productListReqDTO);
    }

    @Override
    public ProductListByCategoryResDTO getProductByCategory(ProductByCategoryReqDTO productByCategoryReqDTO) {
        return shopifyProductThirdPartyService.getProductByCategory(productByCategoryReqDTO);
    }

    @Override
    public ProductResDTO getProductById(ProductByIdReqDTO productByIdReqDTO) {
        return shopifyProductThirdPartyService.getProductById(productByIdReqDTO);
    }
}
