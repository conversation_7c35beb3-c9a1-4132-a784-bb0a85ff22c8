package com.mindbody.api.service.impl;

import com.mindbody.api.dto.AddStreakReqDTO;
import com.mindbody.api.dto.SubmitUserAchievementReqDTO;
import com.mindbody.api.dto.UserStreakResDTO;
import com.mindbody.api.enums.AchievementActivityType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.UserStreakMapper;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.model.EntityUserStreak;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.repository.UserStreakRepository;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.UserAchievementService;
import com.mindbody.api.service.UserStreakService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Objects;
import java.util.Optional;

@Service
public class UserStreakServiceImpl extends BaseService implements UserStreakService {

    private final UserStreakRepository userStreakRepository;

    private final UserRepository userRepository;

    private final UserStreakMapper userStreakMapper;

    private final UserAchievementService userAchievementService;



    public UserStreakServiceImpl(MessageService messageService, UserStreakRepository userStreakRepository, UserRepository userRepository, UserStreakMapper userStreakMapper, UserAchievementService userAchievementService) {
        super(messageService);
        this.userStreakRepository = userStreakRepository;
        this.userRepository = userRepository;
        this.userStreakMapper = userStreakMapper;
        this.userAchievementService = userAchievementService;
    }

    @Override
    public UserStreakResDTO createStreak(AddStreakReqDTO addStreakReqDTO) {
        UserStreakResDTO userStreakResDTO = new UserStreakResDTO();
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(addStreakReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }
        LocalDate today = LocalDate.now();
        Optional<EntityUserStreak> optionalStreak = userStreakRepository.findByUserId(addStreakReqDTO.getUserId());

        EntityUserStreak userStreak;
        if (optionalStreak.isPresent()) {
            userStreak = optionalStreak.get();
            /** Reset the streak if user missed a day */
            if (userStreak.getLastStreakDate() != null && userStreak.getLastStreakDate().plusDays(1).isBefore(today)) {
                userStreak.setFirstStreakDate(today);
                userStreak.setStreakCount(0);
            }
            /** user get only one streak per day */
            if (!today.equals(userStreak.getLastStreakDate())) {
                userStreak.setStreakCount(userStreak.getStreakCount() + 1);
                userStreak.setTotalCount(userStreak.getTotalCount() + 1);
                userStreak.setLastStreakDate(today);
                userStreak.setEntityUser(entityUser);
                userStreak.setUserId(entityUser.getUserId());
                userStreakRepository.save(userStreak);
                userStreakResDTO = userStreakMapper.toDTO(userStreak);

                if(userStreak.getStreakCount() == 3 && !userAchievementService.existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Streaks_Login_3_days,entityUser.getUserId())){
                    userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Streaks_Login_3_days,entityUser.getUserId()));
                }
                if(userStreak.getStreakCount() == 7 && !userAchievementService.existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Streaks_Login_7_days,entityUser.getUserId())){
                    userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Streaks_Login_7_days,entityUser.getUserId()));
                }
                if(userStreak.getStreakCount() == 21 && !userAchievementService.existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Streaks_Login_21_days,entityUser.getUserId())){
                    userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Streaks_Login_21_days,entityUser.getUserId()));;
                }
                if(userStreak.getStreakCount() == 60 && !userAchievementService.existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Streaks_Login_60_days,entityUser.getUserId())){
                    userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Streaks_Login_60_days,entityUser.getUserId()));
                }
                if(userStreak.getStreakCount() == 365 && !userAchievementService.existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Streaks_Login_365_days,entityUser.getUserId())){
                    userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Streaks_Login_365_days,entityUser.getUserId()));
                }
            }
        } else {
            userStreak = new EntityUserStreak();
            userStreak.setUserId(addStreakReqDTO.getUserId());
            userStreak.setStreakCount(1);
            userStreak.setTotalCount(userStreak.getTotalCount() + 1);
            userStreak.setFirstStreakDate(today);
            userStreak.setLastStreakDate(today);
            userStreak.setEntityUser(entityUser);
            userStreak.setUserId(entityUser.getUserId());
            userStreakRepository.save(userStreak);
            userStreakResDTO = userStreakMapper.toDTO(userStreak);
        }
        return userStreakResDTO;
    }
}
