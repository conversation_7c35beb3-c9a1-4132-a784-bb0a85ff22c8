package com.mindbody.api.service.impl;

import com.mindbody.api.dto.shopify.*;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.ShopifyCommonService;
import com.mindbody.api.service.ShopifyProductThirdPartyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class ShopifyProductThirdPartyServiceImpl extends BaseService implements ShopifyProductThirdPartyService {

    private static final Logger logger = LoggerFactory.getLogger(ShopifyProductThirdPartyServiceImpl.class);

    private final ShopifyCommonService shopifyCommonService;

    public ShopifyProductThirdPartyServiceImpl(MessageService messageService, ShopifyCommonService shopifyCommonService) {
        super(messageService);
        this.shopifyCommonService = shopifyCommonService;
    }

    public ProductListResDTO getAllProductList(ProductListReqDTO productListReqDTO) {
        try {
            String requestBody = "{"
                    + "\"query\": \"" + getAllProductListGraphQl() + "\","
                    + "\"variables\": {"
                    + "\"first\": " + productListReqDTO.getFirst() + ","
                    + "\"after\": " + (productListReqDTO.getAfter() != null ? "\"" + productListReqDTO.getAfter() + "\"" : null) + ","
                    + "\"sortKey\": " + (isValidSortKey(productListReqDTO.getSortKey()) ? "\"" + productListReqDTO.getSortKey() + "\"" : null) + ","
                    + "\"reverse\": " + (productListReqDTO.getReverse() != null ? productListReqDTO.getReverse() : null)
                    + "}"
                    + "}";

            return shopifyCommonService.sendGraphQlRequestToShopify(requestBody, ProductListResDTO.class);
        } catch (Exception e) {
            logger.info("Unexpected error occurred while get all products: {}", e.getMessage());
            throw new BusinessValidationException("product_details_failed", e.getMessage());
        }
    }

    @Override
    public ProductResDTO getProductById(ProductByIdReqDTO productByIdReqDTO) {
        try {
            String requestBody = "{"
                    + "\"query\": \"" + getProductById() + "\","
                    + "\"variables\": {"
                    + "\"id\": \"" + productByIdReqDTO.getProductId() + "\""
                    + "}"
                    + "}";
            return shopifyCommonService.sendGraphQlRequestToShopify(requestBody, ProductResDTO.class);
        } catch (Exception e) {
            logger.info("Unexpected error occurred while get product variant detail by variant id: {}", e.getMessage());
            throw new BusinessValidationException("get_product_variant_detail_id_failed", e.getMessage());
        }
    }

    public ProductListByCategoryResDTO getProductByCategory(ProductByCategoryReqDTO productByCategoryReqDTO) {
        try {
            String requestBody = "{"
                    + "\"query\": \"" + getProductByCategoryGraphQl() + "\","
                    + "\"variables\": {"
                    + "\"first\": " + productByCategoryReqDTO.getFirst() + ","
                    + "\"after\": " + (productByCategoryReqDTO.getAfter() != null ? "\"" + productByCategoryReqDTO.getAfter() + "\"" : null) + ","
                    + "\"collectionId\": \"" + productByCategoryReqDTO.getCollectionId() + "\","
                    + "\"sortKey\": " + (isValidSortKey(productByCategoryReqDTO.getSortKey()) ? "\"" + productByCategoryReqDTO.getSortKey() + "\"" : null) + ","
                    + "\"reverse\": " + (productByCategoryReqDTO.getReverse() != null ? productByCategoryReqDTO.getReverse() : null)
                    + "}"
                    + "}";

            return shopifyCommonService.sendGraphQlRequestToShopify(requestBody, ProductListByCategoryResDTO.class);
        } catch (Exception e) {
            logger.info("Unexpected error occurred while get all products by category: {}", e.getMessage());
            throw new BusinessValidationException("product_details_by_collection_failed", e.getMessage());
        }
    }

    public boolean isValidSortKey(String sortKey) {
        return sortKey != null && (
                sortKey.equals("TITLE") ||
                        sortKey.equals("ID") ||
                        sortKey.equals("BEST_SELLING") ||
                        sortKey.equals("CREATED") ||
                        sortKey.equals("UPDATED") ||
                        sortKey.equals("PRICE") ||
                        sortKey.equals("PRODUCT_TYPE") ||
                        sortKey.equals("VENDOR")
        );
    }

    public String getAllProductListGraphQl() {

        return """
                query productsQuery($first: Int!, $after: String, $sortKey: ProductSortKeys, $reverse: Boolean) {
                     products(first: $first, after: $after, sortKey: $sortKey, reverse: $reverse) {
                       edges {
                         node {
                           id
                           title
                           descriptionHtml
                           vendor
                           productType
                           handle
                           tags
                           createdAt
                           updatedAt
                           publishedAt
                           images(first: 5) {
                             edges {
                               node {
                                 id
                                 src
                                 altText
                                 width
                                 height
                               }
                             }
                           }
                           variants(first: 5) {
                             edges {
                               node {
                                 id
                                 title
                                 sku
                                 priceV2 {
                                   amount
                                   currencyCode
                                 }
                                 selectedOptions {
                                   name
                                   value
                                 }
                               }
                             }
                           }
                         }
                       }
                       pageInfo {
                         hasNextPage
                         endCursor
                       }
                     }
                   }
                """;
    }


    public String getProductByCategoryGraphQl() {
        return """
                query productsByCollectionIdQuery($first: Int!, $after: String, $collectionId: ID!, $sortKey: ProductCollectionSortKeys, $reverse: Boolean) {
                      collection(id: $collectionId) {
                        products(first: $first, after: $after, sortKey: $sortKey, reverse: $reverse) {
                          edges {
                            node {
                              id
                              title
                              descriptionHtml
                              vendor
                              productType
                              handle
                              tags
                              createdAt
                              updatedAt
                              publishedAt
                              images(first: 5) {
                                edges {
                                  node {
                                    id
                                    src
                                    altText
                                    width
                                    height
                                  }
                                }
                              }
                              variants(first: 5) {
                                edges {
                                  node {
                                    id
                                    title
                                    sku
                                    priceV2 {
                                      amount
                                      currencyCode
                                    }
                                    selectedOptions {
                                      name
                                      value
                                    }
                                  }
                                }
                              }
                            }
                          }
                          pageInfo {
                            hasNextPage
                            endCursor
                          }
                        }
                      }
                    }
                
                
                """;
    }

    public String getProductById() {
        return """
                query getProductById($id: ID!) {
                   product(id: $id) {
                     id
                     title
                     descriptionHtml
                     vendor
                     productType
                     handle
                     tags
                     createdAt
                     updatedAt
                     publishedAt
                     images(first: 5) {
                       edges {
                         node {
                           id
                           src
                           altText
                           width
                           height
                         }
                       }
                     }
                     variants(first: 5) {
                       edges {
                         node {
                           id
                           title
                           sku
                           priceV2 {
                             amount
                             currencyCode
                           }
                           selectedOptions {
                             name
                             value
                           }
                         }
                       }
                     }
                   }
                 }
                """;
    }

}
