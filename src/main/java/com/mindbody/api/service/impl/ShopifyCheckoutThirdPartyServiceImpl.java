package com.mindbody.api.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mindbody.api.dto.shopify.UpdateBuyerIdentityReqDTO;
import com.mindbody.api.dto.shopify.UpdateBuyerIdentityResDTO;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.ShopifyCheckoutThirdPartyService;
import com.mindbody.api.service.ShopifyCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class ShopifyCheckoutThirdPartyServiceImpl extends BaseService implements ShopifyCheckoutThirdPartyService {

    private static final Logger logger = LoggerFactory.getLogger(ShopifyCartThirdPartyServiceImpl.class);

    private final ShopifyCommonService shopifyCommonService;

    private final ObjectMapper objectMapper;

    public ShopifyCheckoutThirdPartyServiceImpl(MessageService messageService, ShopifyCommonService shopifyCommonService, ObjectMapper objectMapper) {
        super(messageService);
        this.shopifyCommonService = shopifyCommonService;
        this.objectMapper = objectMapper;
    }

    @Override
    public UpdateBuyerIdentityResDTO addShippingAddress(UpdateBuyerIdentityReqDTO updateBuyerIdentityReqDTO) {
        return null;
    }
}
