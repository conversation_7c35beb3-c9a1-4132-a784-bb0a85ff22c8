package com.mindbody.api.service.impl;

import com.mindbody.api.dto.shopify.*;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.EntityShopifyCustomers;
import com.mindbody.api.repository.ShopifyCustomersRepository;
import com.mindbody.api.security.ExecutionContextUtil;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.ShopifyOrderService;
import com.mindbody.api.service.ShopifyOrderThirdPartyService;
import org.springframework.stereotype.Service;

@Service
public class ShopifyOrderServiceImpl extends BaseService implements ShopifyOrderService {


    private final ShopifyOrderThirdPartyService shopifyOrderThirdPartyService;

    private final ShopifyCustomersRepository shopifyCustomersRepository;

    public ShopifyOrderServiceImpl(MessageService messageService, ShopifyOrderThirdPartyService shopifyOrderThirdPartyService, ShopifyCustomersRepository shopifyCustomersRepository) {
        super(messageService);
        this.shopifyOrderThirdPartyService = shopifyOrderThirdPartyService;
        this.shopifyCustomersRepository = shopifyCustomersRepository;
    }

    @Override
    public OrderListResDTO getAllOrdersList(int limit) {
        Long userId = ExecutionContextUtil.getContext().getUserId();
        EntityShopifyCustomers entityShopifyCustomer = shopifyCustomersRepository.findByUserId(userId);
        OrderListReqDTO orderListReqDTO = new OrderListReqDTO();
        if (entityShopifyCustomer != null) {
            orderListReqDTO.setCustomerId(entityShopifyCustomer.getCustomerId());
        }
        orderListReqDTO.setLimit(limit);
        return shopifyOrderThirdPartyService.getAllOrderList(orderListReqDTO);
    }

    @Override
    public OrdersListGraphqlResDTO getAllOrdersListShopifyAdminGraphql(OrdersListGraphqlReqDTO ordersListGraphqlReqDTO) {
        Long userId = ExecutionContextUtil.getContext().getUserId();
        EntityShopifyCustomers entityShopifyCustomer = shopifyCustomersRepository.findByUserId(userId);
        if (entityShopifyCustomer != null) {
            ordersListGraphqlReqDTO.setCustomerId(entityShopifyCustomer.getCustomerId());
        }
        return shopifyOrderThirdPartyService.getAllOrdersListShopifyAdminGraphql(ordersListGraphqlReqDTO);
    }

    @Override
    public TrackOrderResDTO trackOrder(String id) {
        Long userId = ExecutionContextUtil.getContext().getUserId();
        EntityShopifyCustomers entityShopifyCustomer = shopifyCustomersRepository.findByUserId(userId);
        if (entityShopifyCustomer != null) {
            return shopifyOrderThirdPartyService.trackOrder(id);
        } else {
            throw new BusinessValidationException("user_account_not_found_in_shopify");
        }
    }


    @Override
    public int getTotalProductsPurchasedByCustomer(TotalNumberOfProductsReqDTO totalNumberOfProductsReqDTO) {
        return shopifyOrderThirdPartyService.getNumberOfProductsPurchasedByCustomer(totalNumberOfProductsReqDTO);
    }
}
