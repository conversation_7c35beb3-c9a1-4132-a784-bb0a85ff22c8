package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.UploadedFileNameResponseDTO;
import com.mindbody.api.dto.UserAudioPlaylistResDTO;
import com.mindbody.api.dto.cms.AddAudioPlaylistReqDTO;
import com.mindbody.api.dto.cms.AudioPlaylistDetailResDTO;
import com.mindbody.api.dto.cms.CMSAudioPlaylistResDTO;
import com.mindbody.api.dto.cms.EditAudioPlaylistReqDTO;
import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.enums.ImageType;
import com.mindbody.api.enums.UserType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.AudioPlaylistMapper;
import com.mindbody.api.model.EntityAudio;
import com.mindbody.api.model.EntityAudioPlaylist;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.repository.AudioPlaylistRepository;
import com.mindbody.api.repository.AudioRepository;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.security.ExecutionContextUtil;
import com.mindbody.api.service.AWSS3Service;
import com.mindbody.api.service.AudioPlaylistService;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.util.StringUtil;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class AudioPlaylistServiceImpl extends BaseService implements AudioPlaylistService {

    private final AudioPlaylistRepository audioPlaylistRepository;

    private final AudioRepository audioRepository;

    private final AudioPlaylistMapper audioPlaylistMapper;

    private final AWSS3Service awss3Service;

    private final GlobalConfiguration globalConfiguration;

    private final UserRepository userRepository;

    public AudioPlaylistServiceImpl(MessageService messageService, AudioPlaylistRepository audioPlaylistRepository, AudioRepository audioRepository, AudioPlaylistMapper audioPlaylistMapper, AWSS3Service awss3Service, GlobalConfiguration globalConfiguration, UserRepository userRepository) {
        super(messageService);
        this.audioPlaylistRepository = audioPlaylistRepository;
        this.audioRepository = audioRepository;
        this.audioPlaylistMapper = audioPlaylistMapper;
        this.awss3Service = awss3Service;
        this.globalConfiguration = globalConfiguration;
        this.userRepository = userRepository;
    }

    @Override
    public void addAudioPlaylist(MultipartFile audioPlaylistImage, AddAudioPlaylistReqDTO addAudioPlaylistReqDTO) {
        Optional<EntityAudioPlaylist> entityAudioPlaylist = audioPlaylistRepository.findByAudioPlaylistNameAndAudioPlaylistTypeAndIsActiveAndIsDeleted(addAudioPlaylistReqDTO.getAudioPlaylistName().trim(), AudioPlaylistType.valueOf(addAudioPlaylistReqDTO.getAudioPlaylistType()), true, false);
        if (entityAudioPlaylist.isPresent()) {
            throw new BusinessValidationException("audio_playlist_already_exists");
        }
        EntityAudioPlaylist audioPlaylist = audioPlaylistMapper.toModel(addAudioPlaylistReqDTO);
        if (audioPlaylistImage != null && !audioPlaylistImage.isEmpty()) {
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(audioPlaylistImage, globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioPlaylistFolderName(), ImageType.audioPlaylistImage);
            audioPlaylist.setAudioPlaylistImage(uploadedFileName.getImageName());
        }
        audioPlaylistRepository.save(audioPlaylist);
    }

    @Override
    public void editAudioPlaylist(MultipartFile audioPlaylistImage, EditAudioPlaylistReqDTO editAudioPlaylistReqDTO) {
        EntityAudioPlaylist entityAudioPlaylist = audioPlaylistRepository.findByAudioPlaylistIdAndIsDeleted(editAudioPlaylistReqDTO.getAudioPlaylistId(), false);
        if (Objects.isNull(entityAudioPlaylist)) {
            throw new BusinessValidationException("audio_playlist_not_found");
        }
        if (!entityAudioPlaylist.getAudioPlaylistName().equals(editAudioPlaylistReqDTO.getAudioPlaylistName())) {
            Optional<EntityAudioPlaylist> audioPlaylistName = audioPlaylistRepository.findByAudioPlaylistNameAndAudioPlaylistTypeAndIsActiveAndIsDeletedAndAudioPlaylistIdNot(editAudioPlaylistReqDTO.getAudioPlaylistName(), AudioPlaylistType.valueOf(editAudioPlaylistReqDTO.getAudioPlaylistType()), true, false, editAudioPlaylistReqDTO.getAudioPlaylistId());
            if (audioPlaylistName.isPresent()) {
                throw new BusinessValidationException("audio_playlist_already_exists");
            }
        }
        entityAudioPlaylist.setAudioPlaylistName(editAudioPlaylistReqDTO.getAudioPlaylistName());
        String newAudioPlaylistImageName = "";
        if (audioPlaylistImage != null && !audioPlaylistImage.isEmpty()) {
            if (!StringUtil.nullOrEmpty(entityAudioPlaylist.getAudioPlaylistImage())) {
                awss3Service.deleteFile(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioPlaylistFolderName() + entityAudioPlaylist.getAudioPlaylistImage(), ImageType.audioPlaylistImage);
            }
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(audioPlaylistImage, globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioPlaylistFolderName(), ImageType.audioPlaylistImage);
            newAudioPlaylistImageName = uploadedFileName.getImageName();
        }
        if (StringUtil.nonNullNonEmpty(newAudioPlaylistImageName)) {
            entityAudioPlaylist.setAudioPlaylistImage(newAudioPlaylistImageName);
        }
        audioPlaylistRepository.save(entityAudioPlaylist);
    }

    @Override
    public SearchResultDTO<AudioPlaylistDetailResDTO> listAudioPlaylist(CommonListDTO commonListDTO, boolean checkIsActiveRecord) {
        AudioPlaylistType audioPlaylistType = null;
        boolean isAudioPlaylistTypeFilter = false;
        if (commonListDTO.getFilters() != null && StringUtil.nonNullNonEmpty(commonListDTO.getFilters().getSearchFilter())) {
            try {
                audioPlaylistType = AudioPlaylistType.valueOf(commonListDTO.getFilters().getSearchFilter());
            } catch (Exception e) {
                throw new BusinessValidationException("invalid_audio_playlist_type");
            }
            isAudioPlaylistTypeFilter = true;
        }
        Sort sort = null;
        if (commonListDTO.getSortBy() != null && commonListDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(commonListDTO.getSortBy().getDirection(), commonListDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(commonListDTO.getPage().getPageId(), commonListDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());

        List<AudioPlaylistDetailResDTO> audioPlaylistDetailResDTOList = new ArrayList<>();
        long pageCount = 0L;
        pageCount = audioPlaylistRepository.countAudioPlaylists(commonListDTO.getQueryToSearch(), audioPlaylistType, isAudioPlaylistTypeFilter, false);
        if (pageCount > 0) {
            String audioPlaylistImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioPlaylistFolderName();
            audioPlaylistDetailResDTOList = audioPlaylistRepository.findAllAudioPlaylists(commonListDTO.getQueryToSearch(), audioPlaylistImagePath, pageable, audioPlaylistType, isAudioPlaylistTypeFilter, false);
        }
        return new SearchResultDTO<>(audioPlaylistDetailResDTOList, pageCount, commonListDTO.getPage().getLimit());
    }

    @Override
    public void deleteAudioPlaylist(Long audioPlaylistId) {
        EntityAudioPlaylist entityAudioPlaylist = audioPlaylistRepository.findByAudioPlaylistIdAndIsDeleted(audioPlaylistId, false);
        if (Objects.isNull(entityAudioPlaylist)) {
            throw new BusinessValidationException("audio_playlist_not_found");
        }

        List<EntityAudio> audioList = audioRepository.findAllByAudioPlaylistIdAndIsDeleted(entityAudioPlaylist.getAudioPlaylistId(), false);
        if (!audioList.isEmpty()) {
            throw new BusinessValidationException("audio_playlist_delete_error");
        }
        entityAudioPlaylist.setDeleted(true);
        entityAudioPlaylist.setActive(false);
        audioPlaylistRepository.save(entityAudioPlaylist);
    }

    @Override
    public String activeInactiveAudioPlaylist(Long audioPlaylistId) {
        String activeInactiveMsg = "audio_playlist_activate";
        EntityAudioPlaylist entityAudioPlaylist = audioPlaylistRepository.findByAudioPlaylistIdAndIsDeleted(audioPlaylistId, false);
        if (Objects.isNull(entityAudioPlaylist)) {
            throw new BusinessValidationException("audio_playlist_not_found");
        }
        if (entityAudioPlaylist.isActive()) {
            List<EntityAudio> audioList = audioRepository.findAllByAudioPlaylistIdAndIsDeleted(entityAudioPlaylist.getAudioPlaylistId(), false);
            if (!audioList.isEmpty()) {
                throw new BusinessValidationException("audio_playlist_inactive_error");
            }
            activeInactiveMsg = "audio_playlist_inactivate";
        }
        entityAudioPlaylist.setActive(!entityAudioPlaylist.isActive());
        audioPlaylistRepository.save(entityAudioPlaylist);
        return activeInactiveMsg;
    }

    @Override
    public List<CMSAudioPlaylistResDTO> listAudioPlaylistForSelectedAudioPlaylistTypeForCMS(String audioPlaylistTypeInString) {
        AudioPlaylistType audioPlaylistType = null;
        try {
            audioPlaylistType = AudioPlaylistType.valueOf(audioPlaylistTypeInString);
        } catch (Exception e) {
            throw new BusinessValidationException("invalid_audio_playlist_type");
        }
        return audioPlaylistRepository.findAllAudioPlaylistsForSelectedAudioPlaylistTypeForCMS(audioPlaylistType, true, false);
    }

    @Override
    public List<UserAudioPlaylistResDTO> listAudioPlaylistForSelectedAudioPlaylistTypeForUser(String audioPlaylistTypeInString) {
        AudioPlaylistType audioPlaylistType = null;
        try {
            audioPlaylistType = AudioPlaylistType.valueOf(audioPlaylistTypeInString);
        } catch (Exception e) {
            throw new BusinessValidationException("invalid_audio_playlist_type");
        }
        
        // Get current user
        Long currentUserId = ExecutionContextUtil.getContext().getUserId();
        EntityUser currentUser = userRepository.findByUserId(currentUserId);
        
        String audioPlaylistImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioPlaylistFolderName();
        List<UserAudioPlaylistResDTO> playlists = audioPlaylistRepository.findAllAudioPlaylistsForSelectedAudioPlaylistTypeForUser(audioPlaylistImagePath, audioPlaylistType, true, false);
        
        // Apply locking logic for NORMAL users
        if (currentUser != null && currentUser.getUserType() == UserType.NORMAL) {
            for (UserAudioPlaylistResDTO playlist : playlists) {
                boolean isLocked = isPlaylistLockedForNormalUser(audioPlaylistType, playlist.getAudioPlaylistName());
                playlist.setAudioPlaylistLocked(isLocked);
            }
        }
        
        return playlists;
    }

    /**
     * Determines if a playlist should be locked for NORMAL users based on audio playlist type and name
     * 
     * @param audioPlaylistType The type of audio playlist
     * @param audioPlaylistName The name of the audio playlist
     * @return true if the playlist should be locked, false otherwise
     */
    private boolean isPlaylistLockedForNormalUser(AudioPlaylistType audioPlaylistType, String audioPlaylistName) {
        if (audioPlaylistName == null) {
            return false;
        }
        
        switch (audioPlaylistType) {
            case Meditation:
                return "Manifest".equalsIgnoreCase(audioPlaylistName);
            case Sleep:
                return "Stories".equalsIgnoreCase(audioPlaylistName);
            case Renewal:
                return "HZ Healing Frequencies".equalsIgnoreCase(audioPlaylistName);
            default:
                return false;
        }
    }

}
