package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.ShopifyCommonService;
import com.mindbody.api.util.Urls;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.io.IOException;
import java.time.Duration;

@Service
public class ShopifyCommonServiceImpl extends BaseService implements ShopifyCommonService {

    private static final Logger logger = LoggerFactory.getLogger(ShopifyCommonServiceImpl.class);


    private final WebClient shopifyWebClientStoreFront;

    private final WebClient shopifyWebClientShopifyAdmin;

    private final GlobalConfiguration globalConfiguration;

    public ShopifyCommonServiceImpl(MessageService messageService, WebClient shopifyWebClientStoreFront, WebClient shopifyWebClientShopifyAdmin, GlobalConfiguration globalConfiguration) {
        super(messageService);
        this.shopifyWebClientStoreFront = shopifyWebClientStoreFront;
        this.shopifyWebClientShopifyAdmin = shopifyWebClientShopifyAdmin;
        this.globalConfiguration = globalConfiguration;
    }

    @Override
    public <T> T sendGraphQlRequestToShopify(String requestBody, Class<T> responseType) {
        return shopifyWebClientStoreFront.post()
                .uri(uriBuilder -> uriBuilder.path(Urls.API + globalConfiguration.getShopifyConfig().getVersion() + Urls.GRAPHQL_JSON).build())
                .bodyValue(requestBody)
                .retrieve()
                .onStatus(HttpStatusCode::isError, this::handleErrors)
                .bodyToMono(responseType)
                .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))  // Retry up to 3 times with exponential backoff
                        .maxBackoff(Duration.ofSeconds(5))     // Max backoff of 5 seconds
                        .jitter(0.5)                            // Add jitter to prevent synchronized retries
                        .filter(this::isRateLimitingOrNetworkError))     // Only retry on rate-limiting (HTTP 429)
                .block();
    }

    @Override
    public <T> T sendGraphQlRequestToShopifyAdmin(String requestBody, Class<T> responseType) {
        return shopifyWebClientShopifyAdmin.post()
                .uri(uriBuilder -> uriBuilder.path(Urls.ADMIN + Urls.API + globalConfiguration.getShopifyConfig().getVersion() + Urls.GRAPHQL_JSON).build())
                .bodyValue(requestBody)
                .retrieve()
                .onStatus(HttpStatusCode::isError, this::handleErrors)
                .bodyToMono(responseType)
                .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))  // Retry up to 3 times with exponential backoff
                        .maxBackoff(Duration.ofSeconds(5))     // Max backoff of 5 seconds
                        .jitter(0.5)                            // Add jitter to prevent synchronized retries
                        .filter(this::isRateLimitingOrNetworkError))     // Only retry on rate-limiting (HTTP 429)
                .block();
    }

    @Override
    public <T> Mono<T> handleErrors(ClientResponse response) {
        return response.bodyToMono(String.class)
                .flatMap(body -> {
                    if (response.statusCode().equals(HttpStatus.TOO_MANY_REQUESTS)) {
                        logger.info("Rate limit exceeded. Retrying after 5 seconds: {}", body);
                        return Mono.error(new BusinessValidationException("rate_limit_exceeded", body));
                    } else if (response.statusCode().is4xxClientError()) {
                        logger.info("Client error: {}", body);
                        return Mono.error(new BusinessValidationException("Client error: " + response.statusCode() + ", Message: " + body));
                    } else if (response.statusCode().is5xxServerError()) {
                        logger.info("Server error: {}", body);
                        return Mono.error(new BusinessValidationException("Server error: " + response.statusCode() + ", Message: " + body));
                    } else {
                        logger.info("Unexpected error: {}", body);
                        return Mono.error(new BusinessValidationException("Unexpected error: " + response.statusCode()));
                    }
                });
    }

    private boolean isRateLimitingOrNetworkError(Throwable throwable) {
        return (throwable instanceof BusinessValidationException &&
                throwable.getMessage().contains("Rate limit exceeded"))
                || throwable instanceof IOException
                || (throwable.getCause() != null &&
                throwable.getCause().getMessage().contains("recvAddress(..) failed: Connection reset by peer"));
    }

}
