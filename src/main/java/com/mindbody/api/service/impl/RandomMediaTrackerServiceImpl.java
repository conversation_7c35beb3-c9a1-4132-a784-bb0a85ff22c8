package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.PlayRandomReqDTO;
import com.mindbody.api.dto.RandomAudioResDTO;
import com.mindbody.api.dto.cms.AudioZodiacSignDTO;
import com.mindbody.api.dto.cms.ExerciseDetailResDTO;
import com.mindbody.api.dto.cms.WorkoutPlanZodiacSignDTO;
import com.mindbody.api.dto.cms.WorkoutPlanlistDetailResDTO;
import com.mindbody.api.enums.MediaType;
import com.mindbody.api.enums.WorkoutPlanType;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.EntityRandomMediaTracker;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.repository.*;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.RandomMediaTrackerService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class RandomMediaTrackerServiceImpl extends BaseService implements RandomMediaTrackerService {

    private final AudioRepository audioRepository;

    private final GlobalConfiguration globalConfiguration;

    private final UserRepository userRepository;

    private final RandomMediaTrackerRepository randomMediaTrackerRepository;

    private final WorkoutPlanRepository workoutPlanRepository;

    private final WorkoutPlanExerciseRepository workoutPlanExerciseRepository;

    private final WorkoutPlanZodiacRepository workoutPlanZodiacRepository;

    private final AudioZodiacSignRepository audioZodiacSignRepository;

    public RandomMediaTrackerServiceImpl(MessageService messageService, AudioRepository audioRepository, GlobalConfiguration globalConfiguration, UserRepository userRepository, RandomMediaTrackerRepository randomMediaTrackerRepository, WorkoutPlanRepository workoutPlanRepository, WorkoutPlanExerciseRepository workoutPlanExerciseRepository, WorkoutPlanZodiacRepository workoutPlanZodiacRepository, AudioZodiacSignRepository audioZodiacSignRepository) {
        super(messageService);
        this.audioRepository = audioRepository;
        this.globalConfiguration = globalConfiguration;
        this.userRepository = userRepository;
        this.randomMediaTrackerRepository = randomMediaTrackerRepository;
        this.workoutPlanRepository = workoutPlanRepository;
        this.workoutPlanExerciseRepository = workoutPlanExerciseRepository;
        this.workoutPlanZodiacRepository = workoutPlanZodiacRepository;
        this.audioZodiacSignRepository = audioZodiacSignRepository;
    }

    @Override
    public List<RandomAudioResDTO> playRandomAudio(PlayRandomReqDTO playRandomReqDTO) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(playRandomReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        String audioFilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioFileFolderName();
        String thumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioThumbnailFolderName();

        List<RandomAudioResDTO> entityAudioList = audioRepository.findAllByIsActiveAndIsDeleted(playRandomReqDTO.getUserId(), audioFilePath, thumbnailImagePath, true, false);
        if (entityAudioList.isEmpty()) {
            throw new BusinessValidationException("no_audio_available_to_play");
        }

        // Fetch zodiac signs by audio IDs
        List<Long> audioIds = entityAudioList.stream().map(RandomAudioResDTO::getAudioId).collect(Collectors.toList());
        List<AudioZodiacSignDTO> zodiacSigns = audioZodiacSignRepository.findZodiacSignsByAudioIds(audioIds);

        // Map zodiac signs to audio IDs
        Map<Long, List<ZodiacSignType>> zodiacMap = zodiacSigns.stream()
                .collect(Collectors.groupingBy(
                        AudioZodiacSignDTO::getAudioId,
                        Collectors.mapping(AudioZodiacSignDTO::getZodiacSign, Collectors.toList())
                ));

        // Set zodiac signs in each AudioResDTO
        for (RandomAudioResDTO audio : entityAudioList) {
            List<ZodiacSignType> signs = zodiacMap.get(audio.getAudioId());
            audio.setZodiacSigns(signs != null ? signs : new ArrayList<>());
        }

        EntityRandomMediaTracker entityRandomMediaTracker = randomMediaTrackerRepository.findByUserIdAndMediaTypeAndIsActiveAndIsDeleted(entityUser.getUserId(), MediaType.AUDIO, true, false).orElse(null);

        if (entityRandomMediaTracker == null) {
            entityRandomMediaTracker = new EntityRandomMediaTracker();
            entityRandomMediaTracker.setEntityUser(entityUser);
            entityRandomMediaTracker.setMediaType(MediaType.AUDIO);
            entityRandomMediaTracker.setMediaIndex(0);
            entityRandomMediaTracker.setUserId(entityUser.getUserId());
        }
        int mediaIndex = entityRandomMediaTracker.getMediaIndex();
        if (mediaIndex >= entityAudioList.size()) {
            mediaIndex = 0;
        }

        // Rotate the list to start from the next audio
        List<RandomAudioResDTO> rotatedAudioList = new ArrayList<>();
        rotatedAudioList.addAll(entityAudioList.subList(mediaIndex, entityAudioList.size()));  // From the current index to the end
        rotatedAudioList.addAll(entityAudioList.subList(0, mediaIndex));  // From the start to the current index

        // Update the media index for the next play
        entityRandomMediaTracker.setMediaIndex((mediaIndex + 1) % entityAudioList.size());  // Circular loop
        randomMediaTrackerRepository.save(entityRandomMediaTracker);

        return rotatedAudioList;
    }

    @Override
    public List<WorkoutPlanlistDetailResDTO> playRandomWorkoutPlan(PlayRandomReqDTO playRandomReqDTO) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(playRandomReqDTO.getUserId(), true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found_error");
        }

        String workoutVideoPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutVideoFolderName();
        String workoutVideoThumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getWorkoutVideoThumbnailFolderName();
        List<WorkoutPlanlistDetailResDTO> workOutPlanList = workoutPlanRepository.findAllByIsActiveAndIsDeleted(playRandomReqDTO.getUserId(), workoutVideoPath, workoutVideoThumbnailImagePath, true, false);

        // Fetch zodiac signs by audio IDs
        List<Long> audioIds = workOutPlanList.stream().map(WorkoutPlanlistDetailResDTO::getWorkoutPlanId).collect(Collectors.toList());
        List<WorkoutPlanZodiacSignDTO> zodiacSigns = workoutPlanZodiacRepository.findZodiacSignsByAudioIds(audioIds);

        // Map zodiac signs to audio IDs
        Map<Long, List<ZodiacSignType>> zodiacMap = zodiacSigns.stream()
                .collect(Collectors.groupingBy(
                        WorkoutPlanZodiacSignDTO::getWorkoutPlanId,
                        Collectors.mapping(WorkoutPlanZodiacSignDTO::getZodiacSign, Collectors.toList())
                ));

        for (WorkoutPlanlistDetailResDTO workoutPlan : workOutPlanList) {
            String exerciseVideoPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseVideoFolderName();
            String exerciseThumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getExerciseThumbnailFolderName();
            List<ExerciseDetailResDTO> entityWorkoutPlanExerciseList = workoutPlanExerciseRepository.findExerciseDetailsByWorkoutPlanIdAndType(workoutPlan.getWorkoutPlanId(), WorkoutPlanType.MULTI_EXERCISE_PLAN, exerciseVideoPath, exerciseThumbnailImagePath);
            if (!entityWorkoutPlanExerciseList.isEmpty()) {
                workoutPlan.setExerciseList(entityWorkoutPlanExerciseList);
            }
            // Set zodiac signs in each AudioResDTO
            List<ZodiacSignType> signs = zodiacMap.get(workoutPlan.getWorkoutPlanId());
            workoutPlan.setZodiacSigns(signs != null ? signs : new ArrayList<>());
        }
        if (workOutPlanList.isEmpty()) {
            throw new BusinessValidationException("no_workout_plan_available_to_play");
        }

        EntityRandomMediaTracker entityRandomMediaTracker = randomMediaTrackerRepository.findByUserIdAndMediaTypeAndIsActiveAndIsDeleted(entityUser.getUserId(), MediaType.WORKOUT_PLAN, true, false).orElse(null);

        if (entityRandomMediaTracker == null) {
            entityRandomMediaTracker = new EntityRandomMediaTracker();
            entityRandomMediaTracker.setEntityUser(entityUser);
            entityRandomMediaTracker.setMediaType(MediaType.WORKOUT_PLAN);
            entityRandomMediaTracker.setMediaIndex(0);
            entityRandomMediaTracker.setUserId(entityUser.getUserId());
        }
        int mediaIndex = entityRandomMediaTracker.getMediaIndex();
        if (mediaIndex >= workOutPlanList.size()) {
            mediaIndex = 0;
        }

        // Rotate the list to start from the next workout plan
        List<WorkoutPlanlistDetailResDTO> rotatedAudioList = new ArrayList<>();
        rotatedAudioList.addAll(workOutPlanList.subList(mediaIndex, workOutPlanList.size()));  // From the current index to the end
        rotatedAudioList.addAll(workOutPlanList.subList(0, mediaIndex));  // From the start to the current index

        // Update the media index for the next play
        entityRandomMediaTracker.setMediaIndex((mediaIndex + 1) % workOutPlanList.size());  // Circular loop
        randomMediaTrackerRepository.save(entityRandomMediaTracker);

        return rotatedAudioList;

    }
}
