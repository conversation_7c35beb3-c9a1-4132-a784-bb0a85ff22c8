package com.mindbody.api.service.impl;

import com.mindbody.api.dto.QuestionAnswerReqDTO;
import com.mindbody.api.dto.UserAstrologyDetailResDTO;
import com.mindbody.api.dto.UserQuestionAnswerReqDTO;
import com.mindbody.api.dto.UserQuestionAnswerResDTO;
import com.mindbody.api.enums.AccountStatus;
import com.mindbody.api.enums.RegisterType;
import com.mindbody.api.enums.RoleType;
import com.mindbody.api.enums.UserType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.UserInfoMapper;
import com.mindbody.api.model.*;
import com.mindbody.api.repository.*;
import com.mindbody.api.service.AstrologyService;
import com.mindbody.api.service.UserAnswerService;
import com.mindbody.api.util.EncryptUtil;
import com.mindbody.api.util.Methods;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserAnswerServiceImpl implements UserAnswerService {


    private final UserAnswerRepository userAnswerRepository;

    private final UserRepository userRepository;

    private final UserInfoRepository userInfoRepository;

    private final QuestionRepository questionRepository;

    private final UserInfoMapper userInfoMapper;

    private final AccessTokenRepository accessTokenRepository;

    private final AstrologyService astrologyService;


    public UserAnswerServiceImpl(UserAnswerRepository userAnswerRepository, UserRepository userRepository, UserInfoRepository userInfoRepository, QuestionRepository questionRepository, UserInfoMapper userInfoMapper, AccessTokenRepository accessTokenRepository, AstrologyService astrologyService) {
        this.userAnswerRepository = userAnswerRepository;
        this.userRepository = userRepository;
        this.userInfoRepository = userInfoRepository;
        this.questionRepository = questionRepository;
        this.userInfoMapper = userInfoMapper;
        this.accessTokenRepository = accessTokenRepository;
        this.astrologyService = astrologyService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserQuestionAnswerResDTO userQuestionAnswers(UserQuestionAnswerReqDTO userQuestionAnswerReqDTO) {

        List<EntityQuestion> entityQuestionList = questionRepository.findAllQuestionList(true, false);
        if (entityQuestionList.isEmpty()) {
            throw new BusinessValidationException("question_not_found");
        }
        List<Long> queList = entityQuestionList.stream().map(EntityQuestion::getQuestionId).toList();
        List<EntityQuestionOption> entityQuestionOptionList = questionRepository.findAllQuestionOptonsList(queList, true, false);

        Map<Long, EntityQuestion> questionMap = entityQuestionList.stream().collect(Collectors.toMap(EntityQuestion::getQuestionId, question -> question));
        Map<Long, EntityQuestionOption> optionMap = entityQuestionOptionList.stream().collect(Collectors.toMap(EntityQuestionOption::getOptionId, option -> option));

        validateQuestionOption(userQuestionAnswerReqDTO, questionMap, optionMap);

        EntityUser entityUser;
        EntityUserInfo entityUserInfo;
        if (userQuestionAnswerReqDTO.getUserId() == null) {
            /** save the user, at First time every user is ANONYMOUS user,
             after completing the signUp that user will be
             converted to the actual user
             */
            entityUser = new EntityUser();
            entityUser.setUserType(UserType.ANONYMOUS);
            entityUser.setRoleType(RoleType.USER);
            entityUser.setAccountStatus(AccountStatus.PENDING);
            entityUser.setRegisterType(RegisterType.NORMAL);
            entityUser.setProfileCompleted(true);
            entityUser = userRepository.save(entityUser);

            /** save the user basic information */
            entityUserInfo = userInfoMapper.toModel(userQuestionAnswerReqDTO);
            entityUserInfo.setEntityUser(entityUser);
            entityUserInfo.setUserId(entityUserInfo.getUserId());
            /** get the user's zodiac sign from the internal method */
            entityUserInfo.setZodiacSign(Methods.getZodiacSign(userQuestionAnswerReqDTO.getDateOfBirth(), userQuestionAnswerReqDTO.getTimezone()));
            entityUserInfo = userInfoRepository.save(entityUserInfo);
        } else {
            // Update existing user
            entityUser = userRepository.findById(userQuestionAnswerReqDTO.getUserId()).orElseThrow(() -> new BusinessValidationException("user_not_found_error"));

            // Update the user's basic information
            entityUserInfo = userInfoRepository.findByUserIdAndIsActiveAndIsDeleted(entityUser.getUserId(), true, false);
            if (Objects.isNull(entityUserInfo)) {
                throw new BusinessValidationException("user_info_not_found_error");
            }

            /** update the user basic information */
            entityUserInfo.setDateOfBirth(userQuestionAnswerReqDTO.getDateOfBirth());
            entityUserInfo.setPlaceOfBirth(userQuestionAnswerReqDTO.getPlaceOfBirth());
            entityUserInfo.setGenderType(userQuestionAnswerReqDTO.getGenderType());
            entityUserInfo.setTimezone(userQuestionAnswerReqDTO.getTimezone());
            entityUserInfo.setEntityUser(entityUser);
            entityUserInfo.setUserId(entityUserInfo.getUserId());
            /** get the user's zodiac sign from the internal method and update zodiac sign*/
            entityUserInfo.setZodiacSign(Methods.getZodiacSign(userQuestionAnswerReqDTO.getDateOfBirth(), userQuestionAnswerReqDTO.getTimezone()));
            entityUserInfo = userInfoRepository.save(entityUserInfo);
        }

        // Update or save the user answers
        List<EntityUserAnswer> entityUserAnswerList = new ArrayList<>();
        List<QuestionAnswerReqDTO> questionList = userQuestionAnswerReqDTO.getQuestionList();

        for (QuestionAnswerReqDTO questionAnswerReqDTO : questionList) {
            // Fetch existing answers for this user and question
            List<EntityUserAnswer> existingAnswers = userAnswerRepository.findAllByUserIdAndQuestionId(entityUser.getUserId(), questionAnswerReqDTO.getQuestionId());

            // Delete any existing answers if they are no longer present in the request
            userAnswerRepository.deleteAll(existingAnswers);

            // Add or update new answers
            for (Long answerId : questionAnswerReqDTO.getAnswerList()) {
                EntityUserAnswer entityUserAnswer = new EntityUserAnswer();
                entityUserAnswer.setEntityUser(entityUser);
                entityUserAnswer.setUserId(entityUser.getUserId());
                entityUserAnswer.setEntityQuestion(questionMap.get(questionAnswerReqDTO.getQuestionId()));
                entityUserAnswer.setQuestionId(questionAnswerReqDTO.getQuestionId());
                entityUserAnswer.setEntityQuestionOption(optionMap.get(answerId));
                entityUserAnswer.setAnswerId(answerId);
                entityUserAnswerList.add(entityUserAnswer);
            }
        }
        userAnswerRepository.saveAll(entityUserAnswerList);

        /** For the anonymous user we will generate the secretToken */
        String secretToken = EncryptUtil.encryptKey(UUID.randomUUID() + "_" + System.currentTimeMillis());
        EntityAccessToken entityAccessToken = new EntityAccessToken(entityUser.getUserId(), RoleType.USER, null, secretToken);
        accessTokenRepository.save(entityAccessToken);

        /** save the user planet details from the astrology api  */
        List<UserAstrologyDetailResDTO> userAstrologyDetailResDTOS = astrologyService.saveUserPlanetDetails(userQuestionAnswerReqDTO.getDateOfBirth(), entityUserInfo.getTimezone(), userQuestionAnswerReqDTO.getLatitude(), userQuestionAnswerReqDTO.getLongitude(), entityUser);

        /** set response */
        UserQuestionAnswerResDTO response = new UserQuestionAnswerResDTO();
        response.setUserId(entityUser.getUserId());
        response.setRoleType(entityUser.getRoleType());
        response.setUserType(entityUser.getUserType());
        response.setAccountStatus(entityUser.getAccountStatus());
        response.setPlaceOfBirth(entityUserInfo.getPlaceOfBirth());
        response.setDateOfBirth(entityUserInfo.getDateOfBirth());
        response.setGenderType(entityUserInfo.getGenderType());
        response.setTimezone(entityUserInfo.getTimezone());
        response.setZodiacSign(entityUserInfo.getZodiacSign());
        response.setRegisterType(entityUser.getRegisterType());
        response.setSecretToken(secretToken);
        response.setProfileCompleted(entityUser.isProfileCompleted());
        response.setUserAstrologyDetails(userAstrologyDetailResDTOS);
        return response;
    }

    private static void validateQuestionOption(UserQuestionAnswerReqDTO userQuestionAnswerReqDTO, Map<Long, EntityQuestion> questionMap, Map<Long, EntityQuestionOption> optionMap) {
        for (QuestionAnswerReqDTO questionAnswerReqDTO : userQuestionAnswerReqDTO.getQuestionList()) {
            if (!questionMap.containsKey(questionAnswerReqDTO.getQuestionId())) {
                throw new BusinessValidationException("Invalid question ID: " + questionAnswerReqDTO.getQuestionId());
            }
            for (Long answerId : questionAnswerReqDTO.getAnswerList()) {
                if (!optionMap.containsKey(answerId)) {
                    throw new BusinessValidationException("Invalid answer ID: " + answerId);
                }
            }
        }
    }

}
