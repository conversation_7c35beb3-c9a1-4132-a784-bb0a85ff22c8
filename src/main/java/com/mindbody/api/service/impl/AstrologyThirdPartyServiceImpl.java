package com.mindbody.api.service.impl;

import com.mindbody.api.dto.*;
import com.mindbody.api.dto.astrology.*;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.AstrologyThirdPartyMapper;
import com.mindbody.api.service.AstrologyThirdPartyService;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.util.StringUtil;
import com.mindbody.api.util.Urls;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class AstrologyThirdPartyServiceImpl extends BaseService implements AstrologyThirdPartyService {

    private static final Logger logger = LoggerFactory.getLogger(AstrologyThirdPartyServiceImpl.class);

    private final WebClient astrologyWebClient;

    private final AstrologyThirdPartyMapper astrologyThirdPartyMapper;

    private final Validator validator;

    public AstrologyThirdPartyServiceImpl(MessageService messageService, WebClient astrologyWebClient, AstrologyThirdPartyMapper astrologyThirdPartyMapper, Validator validator) {
        super(messageService);
        this.astrologyWebClient = astrologyWebClient;
        this.astrologyThirdPartyMapper = astrologyThirdPartyMapper;
        this.validator = validator;
    }

    @Override
    public TimezoneWithDstResDTO getTimeZoneWithDst(TimeZoneWithDstReqDTO timeZoneWithDstReqDTO) {
        logger.info("Get timezone with dst API is called ");
        validateRequest(timeZoneWithDstReqDTO);
        try {
            TimezoneWithDstThirdPartyResDTO timezoneWithDstThirdPartyResDTO = astrologyWebClient.post().uri(Urls.TIMEZONE_WITH_DST)
                    .bodyValue(astrologyThirdPartyMapper.toTimeZoneWithDstThirdPartyReqDTO(timeZoneWithDstReqDTO))
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, this::handleErrors)
                    .bodyToMono(TimezoneWithDstThirdPartyResDTO.class)
                    .block();
            return Objects.nonNull(timezoneWithDstThirdPartyResDTO) ? astrologyThirdPartyMapper.toTimezoneWithDstResDTO(timezoneWithDstThirdPartyResDTO) : new TimezoneWithDstResDTO();
        } catch (Exception e) {
            logger.info("Unexpected error occurred while fetching the timezone details: {}", e.getMessage());
            throw new BusinessValidationException("time_zone_details_failed", e.getMessage());
        }
    }

    @Override
    public List<UserAstrologyDetailResDTO> getUserPlanetDetails(UserAstrologyReqDTO userAstrologyReqDTO) {
        logger.info("Get the user planet details API is called ");
        validateRequest(userAstrologyReqDTO);
        try {
            List<UserAstrologyDetailThirdPartyResDTO> userAstrologyDetailThirdPartyResDTOS = astrologyWebClient.post().uri(Urls.PLANETS + Urls.TROPICAL)
                    .bodyValue(astrologyThirdPartyMapper.toUserAstrologyThirdPartyReqDTO(userAstrologyReqDTO))
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, this::handleErrors)
                    .bodyToFlux(UserAstrologyDetailThirdPartyResDTO.class)
                    .collectList()
                    .block();
            return userAstrologyDetailThirdPartyResDTOS != null ? userAstrologyDetailThirdPartyResDTOS.stream().map(astrologyThirdPartyMapper::toUserAstrologyDetailResDTO).collect(Collectors.toList()) : null;
        } catch (Exception e) {
            logger.info("Unexpected error occurred while fetching the user planet details: {}", e.getMessage());
            throw new BusinessValidationException("get_planet_details_failed", e.getMessage());
        }
    }

    @Override
    public UserWheelChartResDTO getUserWheelChartDetails(UserWheelChartReqDTO userWheelChartReqDTO) {
        logger.info("Get the user wheel chart details API is called ");
        validateRequest(userWheelChartReqDTO);
        try {
            UserWheelChartThirdPartyResDTO userWheelChartThirdPartyResDTO = astrologyWebClient.post().uri(Urls.NATAL_WHEEL_CHART)
                    .bodyValue(astrologyThirdPartyMapper.toUserWheelChartThirdPartyReqDTO(userWheelChartReqDTO))
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, this::handleErrors)
                    .bodyToMono(UserWheelChartThirdPartyResDTO.class)
                    .block();
            return Objects.nonNull(userWheelChartThirdPartyResDTO) ? astrologyThirdPartyMapper.toUserWheelChartResDTO(userWheelChartThirdPartyResDTO) : new UserWheelChartResDTO();
        } catch (Exception e) {
            logger.info("Unexpected error occurred while fetching the user wheel chart details: {}", e.getMessage());
            throw new BusinessValidationException("get_user_wheel_chart_details_failed", e.getMessage());
        }
    }

    @Override
    public UserPlanetSignReportResDTO getUserPlanetSignReport(UserAstrologyReqDTO userAstrologyReqDTO, String planetName) {
        logger.info("Get the user planet sing report API is called ");
        if (StringUtil.nullOrEmpty(planetName)) {
            throw new BusinessValidationException("planet_name_is_required");
        }
        validateRequest(userAstrologyReqDTO);
        try {
            UserPlanetSignReportThirdPartyResDTO userPlanetSignReportThirdPartyResDTO = astrologyWebClient.post().uri(Urls.GENERAL_SIGN_REPORT + Urls.TROPICAL + "/{planetName}", planetName)
                    .bodyValue(astrologyThirdPartyMapper.toUserAstrologyThirdPartyReqDTO(userAstrologyReqDTO))
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, this::handleErrors)
                    .bodyToMono(UserPlanetSignReportThirdPartyResDTO.class)
                    .block();
            return Objects.nonNull(userPlanetSignReportThirdPartyResDTO) ? astrologyThirdPartyMapper.toUserPlanetSignReportResDTO(userPlanetSignReportThirdPartyResDTO) : new UserPlanetSignReportResDTO();
        } catch (Exception e) {
            logger.info("Unexpected error occurred while fetching the user planet sing report: {}", e.getMessage());
            throw new BusinessValidationException("get_user_planet_sign_report_failed", e.getMessage());
        }
    }

    @Override
    public UserDailyHoroscopeResDTO getUserDailyHoroscope(UserHoroscopeReqDTO userHoroscopeReqDTO, String zodiacName) {
        logger.info("Get the user daily horoscope API is called ");
        if (StringUtil.nullOrEmpty(zodiacName)) {
            throw new BusinessValidationException("zodiac_name_is_required");
        }
        validateRequest(userHoroscopeReqDTO);
        try {
            UserDailyHoroscopeThirdPartyResDTO userDailyHoroscopeThirdPartyResDTO = astrologyWebClient.post().uri(Urls.SUN_SIGN_PREDICTION + Urls.DAILY + "/{zodiacName}", zodiacName)
                    .bodyValue(astrologyThirdPartyMapper.toUserHoroscopeThirdPartyReqDTO(userHoroscopeReqDTO))
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, this::handleErrors)
                    .bodyToMono(UserDailyHoroscopeThirdPartyResDTO.class)
                    .block();

            if (userDailyHoroscopeThirdPartyResDTO != null) {
                Map<String, String> predictionMap = Map.of(
                        "personalLife", userDailyHoroscopeThirdPartyResDTO.getPrediction().getPersonal_life(),
                        "profession", userDailyHoroscopeThirdPartyResDTO.getPrediction().getProfession(),
                        "health", userDailyHoroscopeThirdPartyResDTO.getPrediction().getHealth(),
                        "emotions", userDailyHoroscopeThirdPartyResDTO.getPrediction().getEmotions(),
                        "travel", userDailyHoroscopeThirdPartyResDTO.getPrediction().getTravel(),
                        "luck", userDailyHoroscopeThirdPartyResDTO.getPrediction().getLuck()
                );

                // Return mapped response
                return new UserDailyHoroscopeResDTO(
                        userDailyHoroscopeThirdPartyResDTO.isStatus(),
                        userDailyHoroscopeThirdPartyResDTO.getSun_sign(),
                        userDailyHoroscopeThirdPartyResDTO.getPrediction_date(),
                        predictionMap
                );
            } else {
                return new UserDailyHoroscopeResDTO();
            }

        } catch (Exception e) {
            logger.info("Unexpected error occurred while fetching the user daily horoscope: {}", e.getMessage());
            throw new BusinessValidationException("get_user_daily_horoscope_failed", e.getMessage());
        }
    }

    @Override
    public UserMonthlyHoroscopeResDTO getUserMonthlyHoroscope(UserHoroscopeReqDTO userHoroscopeReqDTO, String zodiacName) {
        logger.info("Get the user monthly horoscope API is called ");
        if (StringUtil.nullOrEmpty(zodiacName)) {
            throw new BusinessValidationException("zodiac_name_is_required");
        }
        validateRequest(userHoroscopeReqDTO);
        try {
            UserMonthlyHoroscopeThirdPartyResDTO userMonthlyHoroscopeThirdPartyResDTO = astrologyWebClient.post().uri(Urls.HOROSCOPE_PREDICTION + Urls.MONTHLY + "/{zodiacName}", zodiacName)
                    .bodyValue(astrologyThirdPartyMapper.toUserHoroscopeThirdPartyReqDTO(userHoroscopeReqDTO))
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, this::handleErrors)
                    .bodyToMono(UserMonthlyHoroscopeThirdPartyResDTO.class)
                    .block();

            if (userMonthlyHoroscopeThirdPartyResDTO != null) {
                return new UserMonthlyHoroscopeResDTO(
                        userMonthlyHoroscopeThirdPartyResDTO.isStatus(),
                        userMonthlyHoroscopeThirdPartyResDTO.getSun_sign(),
                        userMonthlyHoroscopeThirdPartyResDTO.getPrediction_month(),
                        userMonthlyHoroscopeThirdPartyResDTO.getPrediction()
                );
            } else {
                return new UserMonthlyHoroscopeResDTO();
            }

        } catch (Exception e) {
            logger.info("Unexpected error occurred while fetching the user monthly horoscope: {}", e.getMessage());
            throw new BusinessValidationException("get_user_monthly_horoscope_failed", e.getMessage());
        }
    }

    private <T> Mono<T> handleErrors(ClientResponse response) {
        return response.bodyToMono(String.class)
                .flatMap(body -> {
                    if (response.statusCode().is4xxClientError()) {
                        return Mono.error(new BusinessValidationException("Client error: " + response.statusCode() + ", Message: " + body));
                    } else if (response.statusCode().is5xxServerError()) {
                        return Mono.error(new BusinessValidationException("Server error: " + response.statusCode() + ", Message: " + body));
                    } else {
                        return Mono.error(new RuntimeException("Unexpected error: " + response.statusCode()));
                    }
                });
    }

    public <T> void validateRequest(T data) {
        Set<ConstraintViolation<T>> violations = validator.validate(data);
        if (!violations.isEmpty()) {
            throw new ConstraintViolationException(violations);
        }
    }

}
