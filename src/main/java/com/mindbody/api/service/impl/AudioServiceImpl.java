package com.mindbody.api.service.impl;

import com.mindbody.api.config.GlobalConfiguration;
import com.mindbody.api.dto.*;
import com.mindbody.api.dto.cms.*;
import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.enums.ImageType;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.mapper.AudioMapper;
import com.mindbody.api.model.*;
import com.mindbody.api.repository.*;
import com.mindbody.api.service.AWSS3Service;
import com.mindbody.api.service.AudioService;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.UserUnlockedAudioService;
import com.mindbody.api.util.StringUtil;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class AudioServiceImpl extends BaseService implements AudioService {

    private final AudioRepository audioRepository;

    private final FavoriteAudioRepository favoriteAudioRepository;

    private final GlobalConfiguration globalConfiguration;

    private final AWSS3Service awss3Service;

    private final AudioMapper audioMapper;

    private final UserRepository userRepository;

    private final AudioPlaylistRepository audioPlaylistRepository;

    private final RandomMediaTrackerRepository randomMediaTrackerRepository;

    private final AudioZodiacSignRepository audioZodiacSignRepository;

    private final UserUnlockedAudioRepository userUnlockedAudioRepository;

    private final UserUnlockedAudioService userUnlockedAudioService;

    public AudioServiceImpl(MessageService messageService, AudioRepository audioRepository, FavoriteAudioRepository favoriteAudioRepository, GlobalConfiguration globalConfiguration, AWSS3Service awss3Service, AudioMapper audioMapper, UserRepository userRepository, AudioPlaylistRepository audioPlaylistRepository, RandomMediaTrackerRepository randomMediaTrackerRepository, AudioZodiacSignRepository audioZodiacSignRepository, UserUnlockedAudioRepository userUnlockedAudioRepository, UserUnlockedAudioService userUnlockedAudioService) {
        super(messageService);
        this.audioRepository = audioRepository;
        this.favoriteAudioRepository = favoriteAudioRepository;
        this.globalConfiguration = globalConfiguration;
        this.awss3Service = awss3Service;
        this.audioMapper = audioMapper;
        this.userRepository = userRepository;
        this.audioPlaylistRepository = audioPlaylistRepository;
        this.randomMediaTrackerRepository = randomMediaTrackerRepository;
        this.audioZodiacSignRepository = audioZodiacSignRepository;
        this.userUnlockedAudioRepository = userUnlockedAudioRepository;
        this.userUnlockedAudioService = userUnlockedAudioService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAudio(AddAudioReqDTO addAudioReqDTO) {
        validateDTO(addAudioReqDTO);
        EntityAudioPlaylist entityAudioPlaylist = audioPlaylistRepository.findByAudioPlaylistIdAndIsActiveAndIsDeleted(addAudioReqDTO.getAudioPlaylistId(), true, false);
        if (Objects.isNull(entityAudioPlaylist)) {
            throw new BusinessValidationException("audio_playlist_not_found");
        }
        if (!addAudioReqDTO.getAudioPlaylistType().equalsIgnoreCase(String.valueOf(entityAudioPlaylist.getAudioPlaylistType()))) {
            throw new BusinessValidationException("playlist_module_different_error");
        }

        // Validate exclusive audio limit
        if (addAudioReqDTO.isExclusive()) {
            long exclusiveCount = audioRepository.countByIsExclusiveAndIsActiveAndIsDeleted(true, true, false);
            if (exclusiveCount >= 6) {
                throw new BusinessValidationException("maximum_exclusive_audios_limit_reached");
            }
        }

        // Validate redeemable audio limit
        if (addAudioReqDTO.isRedeemable()) {
            long redeemableCount = audioRepository.countByIsRedeemableAndIsActiveAndIsDeleted(true, true, false);
            if (redeemableCount >= 6) {
                throw new BusinessValidationException("maximum_redeemable_audios_limit_reached");
            }
        }

        EntityAudio entityAudio = audioMapper.toModelFromAddAudioReqDTO(addAudioReqDTO);
        entityAudio.setEntityAudioPlaylist(entityAudioPlaylist);
        entityAudio.setAudioPlaylistId(entityAudioPlaylist.getAudioPlaylistId());
        entityAudio.setExclusive(addAudioReqDTO.isExclusive());
        entityAudio.setRedeemable(addAudioReqDTO.isRedeemable());
        entityAudio.setPoints(1000);

//        UploadedFileNameResponseDTO uploadedAudioFile = awss3Service.uploadFile(addAudioReqDTO.getAudioFile(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioFileFolderName(), ImageType.audioFile);
//        entityAudio.setAudioFile(uploadedAudioFile.getImageName());

        UploadedFileNameResponseDTO uploadedAudioThumbnail = awss3Service.uploadFile(addAudioReqDTO.getThumbnailImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioThumbnailFolderName(), ImageType.audioThumbnail);
        entityAudio.setThumbnailImage(uploadedAudioThumbnail.getImageName());

        EntityAudio savedAudio=audioRepository.save(entityAudio);

        /** Store multiple zodiac signs for audio **/
        List<EntityAudioZodiacSign> zodiacSignEntities = new ArrayList<>();
        for (String zodiacSignString : addAudioReqDTO.getZodiacSigns()) {
            try {
                ZodiacSignType zodiacSignType = ZodiacSignType.valueOf(zodiacSignString);
                EntityAudioZodiacSign audioZodiacSign = new EntityAudioZodiacSign();
                audioZodiacSign.setZodiacSign(zodiacSignType);
                audioZodiacSign.setEntityAudio(savedAudio); // Set the relationship back to the audio
                zodiacSignEntities.add(audioZodiacSign);
            } catch (Exception e) {
                throw new BusinessValidationException("invalid_zodiac_sign");
            }
        }

        // Save all zodiac signs
        audioZodiacSignRepository.saveAll(zodiacSignEntities);
    }

    public boolean validateDTO(AddAudioReqDTO addAudioReqDTO) {
        if (Objects.isNull(addAudioReqDTO.getAudioFile())) {
            throw new BusinessValidationException("audio_file_required");
        }
        if (Objects.isNull(addAudioReqDTO.getThumbnailImage())) {
            throw new BusinessValidationException("thumbnail_image_required");
        }
        String[] validExtensions = {"mp3", "wav", "aac", "flac", "ogg", "wma", "m4a"};
        String extension = awss3Service.getFileExtension(addAudioReqDTO.getAudioFile());
        for (String ext : validExtensions) {
            if (ext.equals(extension)) {
                return true;
            }
        }
        throw new BusinessValidationException("audio_file_extension_not_support");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAudio(EditAudioReqDTO editAudioReqDTO) {
        validateFileConditions(editAudioReqDTO);
        EntityAudio entityAudio = audioRepository.findByAudioIdAndIsDeleted(editAudioReqDTO.getAudioId(), false);
        if (Objects.isNull(entityAudio)) {
            throw new BusinessValidationException("audio_not_found");
        }
        EntityAudioPlaylist entityAudioPlaylist = audioPlaylistRepository.findByAudioPlaylistIdAndIsActiveAndIsDeleted(editAudioReqDTO.getAudioPlaylistId(), true, false);
        if (Objects.isNull(entityAudioPlaylist)) {
            throw new BusinessValidationException("audio_playlist_not_found");
        }
        if (!editAudioReqDTO.getAudioPlaylistType().equalsIgnoreCase(String.valueOf(entityAudioPlaylist.getAudioPlaylistType()))) {
            throw new BusinessValidationException("playlist_module_different_error");
        }

        // Validate exclusive audio limit - only check if changing from non-exclusive to exclusive
        if (editAudioReqDTO.isExclusive() && !entityAudio.isExclusive()) {
            long exclusiveCount = audioRepository.countByIsExclusiveAndIsActiveAndIsDeleted(true, true, false);
            if (exclusiveCount >= 6) {
                throw new BusinessValidationException("maximum_exclusive_audios_limit_reached");
            }
        }

        // Validate redeemable audio limit - only check if changing from non-redeemable to redeemable
        if (editAudioReqDTO.isRedeemable() && !entityAudio.isRedeemable()) {
            long redeemableCount = audioRepository.countByIsRedeemableAndIsActiveAndIsDeleted(true, true, false);
            if (redeemableCount >= 6) {
                throw new BusinessValidationException("maximum_redeemable_audios_limit_reached");
            }
        }

        // Handle zodiac sign deletions
        List<String> deleteZodiacSigns = editAudioReqDTO.getDeleteZodiacSigns();
        if (deleteZodiacSigns != null && !deleteZodiacSigns.isEmpty()) {
            // Convert List<String> to List<ZodiacSignType>
            List<ZodiacSignType> deleteZodiacSignTypes = deleteZodiacSigns.stream()
                    .map(ZodiacSignType::valueOf)
                    .collect(Collectors.toList());
            List<EntityAudioZodiacSign> existingZodiacSigns = audioZodiacSignRepository.findByEntityAudio_AudioIdAndZodiacSignIn(entityAudio.getAudioId(), deleteZodiacSignTypes);
            audioZodiacSignRepository.deleteAll(existingZodiacSigns);
        }

        // Handle zodiac sign updates
        if (editAudioReqDTO.getZodiacSigns() != null && !editAudioReqDTO.getZodiacSigns().isEmpty()) {
            // Convert List<String> to List<ZodiacSignType>
            List<ZodiacSignType> requestedZodiacSigns = editAudioReqDTO.getZodiacSigns().stream()
                    .map(ZodiacSignType::valueOf)
                    .collect(Collectors.toList());

            // Fetch existing zodiac signs for the audio
            List<EntityAudioZodiacSign> existingZodiacSigns = audioZodiacSignRepository.findByEntityAudio_AudioIdAndZodiacSignIn(entityAudio.getAudioId(),requestedZodiacSigns);

            // Create a set of existing zodiac signs for quick lookup
            Set<ZodiacSignType> existingZodiacSignSet = existingZodiacSigns.stream()
                    .map(EntityAudioZodiacSign::getZodiacSign)
                    .collect(Collectors.toSet());

            // Filter out zodiac signs that are already in the database
            List<EntityAudioZodiacSign> filteredAudioZodiacSignList = requestedZodiacSigns.stream()
                    .filter(zodiacSign -> !existingZodiacSignSet.contains(zodiacSign)) // Keep only the missing ones
                    .map(zodiacSign -> {
                        EntityAudioZodiacSign entityAudioZodiacSign = new EntityAudioZodiacSign();
                        entityAudioZodiacSign.setEntityAudio(entityAudio);
                        entityAudioZodiacSign.setZodiacSign(zodiacSign);
                        return entityAudioZodiacSign;
                    })
                    .collect(Collectors.toList());

            // Save all new zodiac signs
            if (!filteredAudioZodiacSignList.isEmpty()) {
                audioZodiacSignRepository.saveAll(filteredAudioZodiacSignList);
            }
        }

        uploadNewFileAndDeleteOld(editAudioReqDTO, entityAudio);
        entityAudio.setTitle(editAudioReqDTO.getTitle());
        entityAudio.setDescription(editAudioReqDTO.getDescription());
        entityAudio.setAudioPlaylistType(AudioPlaylistType.valueOf(editAudioReqDTO.getAudioPlaylistType()));
        entityAudio.setEntityAudioPlaylist(entityAudioPlaylist);
        entityAudio.setAudioPlaylistId(entityAudioPlaylist.getAudioPlaylistId());
        entityAudio.setExclusive(editAudioReqDTO.isExclusive());
        entityAudio.setRedeemable(editAudioReqDTO.isRedeemable());
        audioRepository.save(entityAudio);
    }

    @Override
    public void deleteAudio(Long audioId) {
        EntityAudio entityAudio = audioRepository.findByAudioIdAndIsDeleted(audioId, false);
        if (Objects.isNull(entityAudio)) {
            throw new BusinessValidationException("audio_not_found");
        }
        entityAudio.setDeleted(true);
        entityAudio.setActive(false);
        audioRepository.save(entityAudio);

        /** Hard delete audio from EntityFavoriteAudio table. **/
        favoriteAudioRepository.deleteByAudioId(entityAudio.getAudioId());
    }

    @Override
    public boolean activeInactiveAudio(Long audioId) {
        boolean isActive = true;
        EntityAudio entityAudio = audioRepository.findByAudioIdAndIsDeleted(audioId, false);
        if (Objects.isNull(entityAudio)) {
            throw new BusinessValidationException("audio_not_found");
        }
        if (entityAudio.isActive()) {
            isActive = false;
        }
        entityAudio.setActive(!entityAudio.isActive());
        audioRepository.save(entityAudio);
        return isActive;
    }

    @Override
    public SearchResultDTO<AudioResDTO> listAudioCms(AudioListReqDTO audioListReqDTO) {
        AudioPlaylistType audioPlaylistType = null;
        Long audioPlaylistId = null;
        boolean isAudioPlayListTypeFilter = false;
        boolean isAudioPlayListFilter = false;
        if (audioListReqDTO.getFilters() != null && StringUtil.nonNullNonEmpty(audioListReqDTO.getFilters().getAudioPlaylistType())) {
            audioPlaylistType = AudioPlaylistType.valueOf(audioListReqDTO.getFilters().getAudioPlaylistType());
            isAudioPlayListTypeFilter = true;
        }
        if (audioListReqDTO.getFilters() != null && audioListReqDTO.getFilters().getAudioPlaylistId() != null) {
            audioPlaylistId = audioListReqDTO.getFilters().getAudioPlaylistId();
            isAudioPlayListFilter = true;
        }
        Sort sort = null;
        if (audioListReqDTO.getSortBy() != null && audioListReqDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(audioListReqDTO.getSortBy().getDirection(), audioListReqDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(audioListReqDTO.getPage().getPageId(), audioListReqDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());
        List<AudioResDTO> audioResDTOList = new ArrayList<>();
        long pageCount = audioRepository.countAudio(audioListReqDTO.getQueryToSearch(), audioPlaylistType, isAudioPlayListTypeFilter, audioPlaylistId, isAudioPlayListFilter, false);
        if (pageCount > 0) {
            String audioFilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioFileFolderName();
            String thumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioThumbnailFolderName();
            audioResDTOList = audioRepository.findAllAudio(audioListReqDTO.getQueryToSearch(), pageable, audioPlaylistType, isAudioPlayListTypeFilter, audioPlaylistId, isAudioPlayListFilter, audioFilePath, thumbnailImagePath, false);

            // Fetch zodiac signs by audio IDs
            List<Long> audioIds = audioResDTOList.stream().map(AudioResDTO::getAudioId).collect(Collectors.toList());
            List<AudioZodiacSignDTO> zodiacSigns = audioZodiacSignRepository.findZodiacSignsByAudioIds(audioIds);

            // Map zodiac signs to audio IDs
            Map<Long, List<ZodiacSignType>> zodiacMap = zodiacSigns.stream()
                    .collect(Collectors.groupingBy(
                            AudioZodiacSignDTO::getAudioId,
                            Collectors.mapping(AudioZodiacSignDTO::getZodiacSign, Collectors.toList())
                    ));

            // Set zodiac signs in each AudioResDTO
            for (AudioResDTO audio : audioResDTOList) {
                List<ZodiacSignType> signs = zodiacMap.get(audio.getAudioId());
                audio.setZodiacSigns(signs != null ? signs : new ArrayList<>());
            }
        }

        return new SearchResultDTO<>(audioResDTOList, pageCount, audioListReqDTO.getPage().getLimit());
    }

    @Override
    public SearchResultDTO<UserAudioListResDTO> listAudioUser(CommonAudioListUserReqDTO commonAudioListUserReqDTO) {
        AudioPlaylistType audioPlaylistType = AudioPlaylistType.valueOf(commonAudioListUserReqDTO.getFilters().getAudioPlaylistType());
        ZodiacSignType zodiacSign = null;
        if (commonAudioListUserReqDTO.getFilters() != null && StringUtil.nonNullNonEmpty(commonAudioListUserReqDTO.getFilters().getZodiacSign())) {
            zodiacSign = ZodiacSignType.valueOf(commonAudioListUserReqDTO.getFilters().getZodiacSign());
        }
        Long audioPlaylistId = commonAudioListUserReqDTO.getFilters().getAudioPlaylistId();
        boolean isAudioPlayListFilter = audioPlaylistId != null;
        Sort sort = null;
        if (commonAudioListUserReqDTO.getSortBy() != null && commonAudioListUserReqDTO.getSortBy().getProperty() != null) {
            sort = Sort.by(commonAudioListUserReqDTO.getSortBy().getDirection(), commonAudioListUserReqDTO.getSortBy().getProperty());
        }
        Pageable pageable = PageRequest.of(commonAudioListUserReqDTO.getPage().getPageId(), commonAudioListUserReqDTO.getPage().getLimit(), sort != null ? sort : Sort.unsorted());
        List<UserAudioListResDTO> audioResDTOList = new ArrayList<>();
        long pageCount = audioRepository.countAudioForUser(commonAudioListUserReqDTO.getQueryToSearch(), audioPlaylistType, audioPlaylistId, zodiacSign, true, false);
        if (pageCount > 0) {
            String audioFilePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioFileFolderName();
            String thumbnailImagePath = globalConfiguration.getAmazonS3().getUrlUpToBucket() + globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioThumbnailFolderName();
            audioResDTOList = audioRepository.findAllAudioForUser(commonAudioListUserReqDTO.getQueryToSearch(), commonAudioListUserReqDTO.getFilters().getUserId(), pageable, audioPlaylistId, zodiacSign, audioFilePath, thumbnailImagePath, true, false, audioPlaylistType, isAudioPlayListFilter);

            // Get all audio IDs from the list
            List<Long> audioIds = audioResDTOList.stream()
                    .map(UserAudioListResDTO::getAudioId)
                    .collect(Collectors.toList());

            // Fetch zodiac signs by audio IDs
            List<AudioZodiacSignDTO> zodiacSigns = audioZodiacSignRepository.findZodiacSignsByAudioIds(audioIds);

            // Map zodiac signs to audio IDs
            Map<Long, List<ZodiacSignType>> zodiacMap = zodiacSigns.stream()
                    .collect(Collectors.groupingBy(
                            AudioZodiacSignDTO::getAudioId,
                            Collectors.mapping(AudioZodiacSignDTO::getZodiacSign, Collectors.toList())
                    ));

            // Set zodiac signs in each AudioResDTO
            for (UserAudioListResDTO audio : audioResDTOList) {
                List<ZodiacSignType> signs = zodiacMap.get(audio.getAudioId());
                audio.setZodiacSigns(signs != null ? signs : new ArrayList<>());
            }
        }
        return new SearchResultDTO<>(audioResDTOList, pageCount, commonAudioListUserReqDTO.getPage().getLimit());
    }

    private void uploadNewFileAndDeleteOld(EditAudioReqDTO editAudioReqDTO, EntityAudio entityAudio) {
        List<String> deleteImageList = new ArrayList<>();
        if (StringUtil.nullOrEmpty(editAudioReqDTO.getExistingAudioFile()) && StringUtil.nonNullNonEmpty(entityAudio.getAudioFile())) {
            deleteImageList.add(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioFileFolderName() + entityAudio.getAudioFile());
            entityAudio.setAudioFile(null);
        }
        if (StringUtil.nullOrEmpty(editAudioReqDTO.getExistingThumbnailImage()) && StringUtil.nonNullNonEmpty(entityAudio.getThumbnailImage())) {
            deleteImageList.add(globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioThumbnailFolderName() + entityAudio.getThumbnailImage());
            entityAudio.setThumbnailImage(null);
        }
        if (StringUtil.nonNullNonEmpty(editAudioReqDTO.getAudioFile())) {
//            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(editAudioReqDTO.getAudioFile(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioFileFolderName(), ImageType.audioFile);
            entityAudio.setAudioFile(editAudioReqDTO.getAudioFile());
        }
        if (editAudioReqDTO.getThumbnailImage() != null) {
            UploadedFileNameResponseDTO uploadedFileName = awss3Service.uploadFile(editAudioReqDTO.getThumbnailImage(), globalConfiguration.getAmazonS3().getUploadFolderName() + globalConfiguration.getAmazonS3().getAudioThumbnailFolderName(), ImageType.audioThumbnail);
            entityAudio.setThumbnailImage(uploadedFileName.getImageName());
        }
        if (deleteImageList != null && !deleteImageList.isEmpty()) {
            awss3Service.deleteFiles(deleteImageList);
        }
    }

    private void validateFileConditions(EditAudioReqDTO editAudioReqDTO) {
        if (Objects.nonNull(editAudioReqDTO.getAudioFile()) && StringUtil.nonNullNonEmpty(editAudioReqDTO.getExistingAudioFile())) {
            throw new BusinessValidationException("audio_file_error");
        }
        if (Objects.nonNull(editAudioReqDTO.getThumbnailImage()) && StringUtil.nonNullNonEmpty(editAudioReqDTO.getExistingThumbnailImage())) {
            throw new BusinessValidationException("thumbnail_audio_file_error");
        }
        if (Objects.isNull(editAudioReqDTO.getAudioFile()) && StringUtil.nullOrEmpty(editAudioReqDTO.getExistingAudioFile())) {
            throw new BusinessValidationException("audio_file_required");
        }
        if (Objects.isNull(editAudioReqDTO.getThumbnailImage()) && StringUtil.nullOrEmpty(editAudioReqDTO.getExistingThumbnailImage())) {
            throw new BusinessValidationException("thumbnail_audio_file_required");
        }
    }
}
