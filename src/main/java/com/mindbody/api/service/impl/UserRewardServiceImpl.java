package com.mindbody.api.service.impl;

import com.mindbody.api.dto.RewardStatusDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.RewardRedemptionResDTO;
import com.mindbody.api.enums.RewardType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.model.EntityReward;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.model.EntityUserPointsTracker;
import com.mindbody.api.model.EntityUserRedeemedReward;
import com.mindbody.api.repository.RewardRepository;
import com.mindbody.api.repository.UserPointsTrackerRepository;
import com.mindbody.api.repository.UserRedeemedRewardRepository;
import com.mindbody.api.repository.UserRepository;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.UserRewardService;
import com.mindbody.api.config.GlobalConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class UserRewardServiceImpl extends BaseService implements UserRewardService {

    private final UserRepository userRepository;
    private final RewardRepository rewardRepository;
    private final UserRedeemedRewardRepository userRedeemedRewardRepository;
    private final UserPointsTrackerRepository userPointsTrackerRepository;
    private final GlobalConfiguration globalConfiguration;
    private final SecureRandom secureRandom;

    public UserRewardServiceImpl(
            MessageService messageService,
            UserRepository userRepository,
            RewardRepository rewardRepository,
            UserRedeemedRewardRepository userRedeemedRewardRepository,
            UserPointsTrackerRepository userPointsTrackerRepository,
            GlobalConfiguration globalConfiguration) {
        super(messageService);
        this.userRepository = userRepository;
        this.rewardRepository = rewardRepository;
        this.userRedeemedRewardRepository = userRedeemedRewardRepository;
        this.userPointsTrackerRepository = userPointsTrackerRepository;
        this.globalConfiguration = globalConfiguration;
        this.secureRandom = new SecureRandom();
    }

    @Override
    public SearchResultDTO<RewardStatusDTO> getRewardsWithStatus(Long userId, String queryToSearch, RewardType rewardType, Pageable pageable) {
        EntityUser entityUser = validateUser(userId);
        EntityUserPointsTracker pointsTracker = validateUserPoints(userId);

        List<EntityReward> rewards = rewardRepository.findAvailableRewards(
                queryToSearch,
                true,
                false,
                pageable
        );

        long totalCount = rewardRepository.countAvailableRewards(
                queryToSearch,
                rewardType,
                true,
                false
        );

        List<RewardStatusDTO> rewardStatusDTOs = mapToRewardStatusDTOs(rewards, entityUser, pointsTracker);
        return new SearchResultDTO<>(rewardStatusDTOs, totalCount, pageable.getPageSize());
    }

    @Override
    @Transactional
    public RewardRedemptionResDTO redeemReward(Long userId, Long rewardId) {
        EntityUser entityUser = validateUser(userId);
        EntityUserPointsTracker pointsTracker = validateUserPoints(userId);
        EntityReward reward = validateReward(rewardId);

        // Check if already redeemed
        if (userRedeemedRewardRepository.isRewardRedeemedByUser(userId, rewardId, true, false)) {
            throw new BusinessValidationException("reward_already_redeemed");
        }

        // Check if user has enough points
        if (pointsTracker.getTotalPoints() < reward.getPointsRequired()) {
            throw new BusinessValidationException("insufficient_points");
        }

        // Create redemption record
        EntityUserRedeemedReward redeemedReward = new EntityUserRedeemedReward();
        redeemedReward.setUserId(userId);
        redeemedReward.setRewardId(rewardId);
        redeemedReward.setPointsSpent(reward.getPointsRequired());

        String couponCode = null;
        // Generate coupon code for store discount
        if (reward.getRewardType() == RewardType.TEN_PERCENT_DISCOUNT_MBW_STORE) {
            couponCode = generateCouponCode();
            redeemedReward.setRewardMetadata(couponCode);
        }

        userRedeemedRewardRepository.save(redeemedReward);

        // Update user points
        pointsTracker.setTotalPoints(pointsTracker.getTotalPoints() - reward.getPointsRequired());
        userPointsTrackerRepository.save(pointsTracker);

        // Create response
        return new RewardRedemptionResDTO(
                reward.getTitle(),
                pointsTracker.getTotalPoints().intValue(),
                couponCode
        );
    }

    /**
     * Generates a unique 9-digit coupon code
     * @return 9-digit coupon code
     */
    private String generateCouponCode() {
        String couponCode;
        int maxAttempts = 10; // Maximum attempts to generate a unique code
        int attempts = 0;

        do {
            StringBuilder code = new StringBuilder();
            for (int i = 0; i < 9; i++) {
                code.append(secureRandom.nextInt(10));
            }
            couponCode = code.toString();
            attempts++;

            // Break if we found a unique code or reached max attempts
            if (!userRedeemedRewardRepository.existsByCouponCode(couponCode) || attempts >= maxAttempts) {
                break;
            }
        } while (true);

        // If we couldn't generate a unique code after max attempts, throw an exception
        if (userRedeemedRewardRepository.existsByCouponCode(couponCode)) {
            throw new BusinessValidationException("failed_to_generate_unique_coupon");
        }

        return couponCode;
    }

    private EntityUser validateUser(Long userId) {
        EntityUser entityUser = userRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false);
        if (Objects.isNull(entityUser)) {
            throw new BusinessValidationException("user_not_found");
        }
        return entityUser;
    }

    private EntityUserPointsTracker validateUserPoints(Long userId) {
        return userPointsTrackerRepository.findByUserIdAndIsActiveAndIsDeleted(userId, true, false)
                .orElseThrow(() -> new BusinessValidationException("user_points_not_found"));
    }

    private EntityReward validateReward(Long rewardId) {
        return rewardRepository.findByRewardIdAndIsActiveAndIsDeleted(rewardId, true, false)
                .orElseThrow(() -> new BusinessValidationException("reward_not_found"));
    }

    private List<RewardStatusDTO> mapToRewardStatusDTOs(
            List<EntityReward> rewards,
            EntityUser user,
            EntityUserPointsTracker pointsTracker) {
        List<RewardStatusDTO> dtos = new ArrayList<>();
        String thumbnailPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getRewardThumbnailFolderName();
        String rewardItemPath = globalConfiguration.getAmazonS3().getUrlUpToBucket() +
                globalConfiguration.getAmazonS3().getUploadFolderName() +
                globalConfiguration.getAmazonS3().getRewardItemAssetsFolderName();



        for (EntityReward reward : rewards) {
            RewardStatusDTO dto = new RewardStatusDTO();
            dto.setRewardId(reward.getRewardId());
            dto.setTitle(reward.getTitle());
            dto.setDescription(reward.getDescription());
            dto.setThumbnailImage(reward.getThumbnailImage() != null ? thumbnailPath + reward.getThumbnailImage() : null);
            dto.setPointsRequired(reward.getPointsRequired());
            dto.setRewardType(reward.getRewardType());
            dto.setRewardMetadata( reward.getRewardMetadata() != null ? rewardItemPath + reward.getRewardMetadata() : null);
            dto.setActive(reward.isActive());
            dto.setCreatedAt(reward.getCreatedAt());
            dto.setUpdatedAt(reward.getUpdatedAt());

            // Check if user has already redeemed this reward
            boolean isRedeemed = userRedeemedRewardRepository.isRewardRedeemedByUser(
                    user.getUserId(),
                    reward.getRewardId(),
                    true,
                    false
            );
            dto.setRedeemed(isRedeemed);

            // Check if user can redeem this reward
            boolean canRedeem = !isRedeemed &&
                    pointsTracker.getTotalPoints() >= reward.getPointsRequired();
            dto.setCanRedeem(canRedeem);

            dtos.add(dto);
        }

        return dtos;
    }
} 