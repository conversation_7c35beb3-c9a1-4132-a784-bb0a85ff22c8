package com.mindbody.api.service.impl;

import com.mindbody.api.dto.ChatCategoryDetailsResDTO;
import com.mindbody.api.dto.GetCategoryDetailsDTO;
import com.mindbody.api.dto.MarkMessageAsReadReqDTO;
import com.mindbody.api.repository.ChatMessagesRepository;
import com.mindbody.api.service.ChatMessagesService;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ChatMessagesServiceImpl implements ChatMessagesService {

    private ChatMessagesRepository chatMessagesRepository;

    ChatMessagesServiceImpl(ChatMessagesRepository chatMessagesRepository){
        this.chatMessagesRepository=chatMessagesRepository;
    }

    @Override
    public ChatCategoryDetailsResDTO getChatCategoryDetails(Long userId) {
        // Fetch all available categories (assuming a method exists to fetch all categories)
        List<String> allCategories = List.of("EMPOWERMENT_COACH","ROMANTIC_COMPATIBILITY","NEWS_AND_UPDATES","MY_DAILY_READING");

        // Fetch stats for the user
        List<Object[]> rawCategoryDetails = chatMessagesRepository.findChatCategoryDetails(userId);

        // Map stats to a category-to-details map for easy lookup
        Map<String, Object[]> statsMap = rawCategoryDetails.stream()
                .collect(Collectors.toMap(record -> (String) record[0], record -> record));

        // Build the final list of category details
        List<GetCategoryDetailsDTO> categoryDetails = allCategories.stream().map(category -> {
            Object[] record = statsMap.get(category);
            Long unreadCount = record != null ? (Long) record[1] : 0L;
            LocalDateTime createdAt = record != null ? (LocalDateTime) record[2] : null;

            Long lastInteractionTimeDiff = createdAt != null
                    ? Duration.between(createdAt, LocalDateTime.now()).getSeconds()
                    : null;

            return new GetCategoryDetailsDTO(category, unreadCount, lastInteractionTimeDiff);
        }).collect(Collectors.toList());

        return new ChatCategoryDetailsResDTO(categoryDetails);
    }

    @Override
    public void markMessagesAsRead(MarkMessageAsReadReqDTO markMessageAsReadReqDTO) {
        chatMessagesRepository.markMessagesAsRead(markMessageAsReadReqDTO.getUserId(), markMessageAsReadReqDTO.getCategory());
    }

}
