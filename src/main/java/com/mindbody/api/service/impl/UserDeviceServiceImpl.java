package com.mindbody.api.service.impl;

import com.mindbody.api.enums.DeviceType;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.model.EntityUserDevice;
import com.mindbody.api.repository.UserDeviceRepository;
import com.mindbody.api.repository.UserReferralRepository;
import com.mindbody.api.service.UserDeviceService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserDeviceServiceImpl implements UserDeviceService {

    private final UserDeviceRepository userDeviceRepository;
    private final UserReferralRepository userReferralRepository;

    @Override
    @Transactional
    public EntityUserDevice trackUserDevice(EntityUser user, String deviceUniqueId, String ipAddress, DeviceType deviceType) {
        log.info("Tracking device for user ID: {}, device ID: {}, IP: {}", user.getUserId(), deviceUniqueId, ipAddress);

        // Check if we already have this device for this user
        Optional<EntityUserDevice> existingDevice = userDeviceRepository.findByDeviceUniqueIdAndUserIdAndIsDeleted(
                deviceUniqueId, user.getUserId(), false);

        if (existingDevice.isPresent()) {
            // Update the existing device record
            EntityUserDevice device = existingDevice.get();
            device.setIpAddress(ipAddress);
            device.setDeviceType(deviceType);
            device.setUpdatedAt(LocalDateTime.now());
            return userDeviceRepository.save(device);
        } else {
            // Create a new device record
            EntityUserDevice newDevice = EntityUserDevice.builder()
                    .userId(user.getUserId())
                    .entityUser(user)
                    .deviceUniqueId(deviceUniqueId)
                    .ipAddress(ipAddress)
                    .deviceType(deviceType)
                    .isActive(true)
                    .isDeleted(false)
                    .wasDeletedAccount(false)
                    .build();

            return userDeviceRepository.save(newDevice);
        }
    }

    @Override
    public boolean isDeviceOrIpAssociatedWithOtherUser(Long userId, String deviceUniqueId, String ipAddress) {
        // Check if the device is associated with another user
        boolean deviceAssociated = userDeviceRepository.existsByDeviceUniqueIdAndUserIdNotAndIsDeleted(
                deviceUniqueId, userId, false);

        // Check if the IP is associated with another user
        boolean ipAssociated = userDeviceRepository.existsByIpAddressAndUserIdNotAndIsDeleted(
                ipAddress, userId, false);

        return deviceAssociated || ipAssociated;
    }

    @Override
    @Transactional
    public void markUserDevicesAsDeletedAccount(Long userId) {
        log.info("Marking devices as belonging to deleted account for user ID: {}", userId);

        // Find all devices for this user
        List<EntityUserDevice> userDevices = userDeviceRepository.findAll().stream()
                .filter(device -> device.getUserId().equals(userId) && !device.isDeleted())
                .toList();

        // Mark each device as belonging to a deleted account
        for (EntityUserDevice device : userDevices) {
            device.setWasDeletedAccount(true);
            userDeviceRepository.save(device);
        }
    }

    @Override
    public boolean wasDeviceOrIpAssociatedWithDeletedAccount(String deviceUniqueId, String ipAddress) {
        // Check if the device was associated with a deleted account
        List<EntityUserDevice> deviceRecords = userDeviceRepository.findByDeviceUniqueIdAndWasDeletedAccountAndIsDeleted(
                deviceUniqueId, true, false);

        // Check if the IP was associated with a deleted account
        List<EntityUserDevice> ipRecords = userDeviceRepository.findByIpAddressAndWasDeletedAccountAndIsDeleted(
                ipAddress, true, false);

        return !deviceRecords.isEmpty() || !ipRecords.isEmpty();
    }

    @Override
    public String getCurrentIpAddress() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();

        // Try to get IP from X-FORWARDED-FOR header first (for clients behind proxies)
        String ipAddress = request.getHeader("X-FORWARDED-FOR");

        // If not found, use the remote address
        if (ipAddress == null || ipAddress.isEmpty()) {
            ipAddress = request.getRemoteAddr();
        }

        // If there are multiple IPs in X-FORWARDED-FOR, take the first one (client IP)
        if (ipAddress != null && ipAddress.contains(",")) {
            ipAddress = ipAddress.split(",")[0].trim();
        }

        return ipAddress;
    }

    @Override
    public boolean hasDeviceEnteredAnyReferralCodeBefore(String deviceUniqueId) {
        log.info("Checking if device has entered any referral code before. Device ID: {}", deviceUniqueId);

        if (deviceUniqueId == null || deviceUniqueId.isEmpty()) {
            return false;
        }

        // Find all users who have used this device
        List<EntityUserDevice> deviceRecords = userDeviceRepository.findByDeviceUniqueIdAndIsDeleted(deviceUniqueId, false);

        if (deviceRecords.isEmpty()) {
            return false;
        }

        // Extract user IDs from device records
        List<Long> userIds = deviceRecords.stream()
                .map(EntityUserDevice::getUserId)
                .toList();

        // Check if any of these users have entered a referral code
        return userReferralRepository.existsByToUserIdInAndIsDeleted(userIds, false);
    }
}
