package com.mindbody.api.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mindbody.api.dto.shopify.*;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.service.BaseService;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.service.ShopifyCartThirdPartyService;
import com.mindbody.api.service.ShopifyCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class ShopifyCartThirdPartyServiceImpl extends BaseService implements ShopifyCartThirdPartyService {

    private static final Logger logger = LoggerFactory.getLogger(ShopifyCartThirdPartyServiceImpl.class);

    private final ShopifyCommonService shopifyCommonService;

    private final ObjectMapper objectMapper;

    public ShopifyCartThirdPartyServiceImpl(MessageService messageService, ShopifyCommonService shopifyCommonService, ObjectMapper objectMapper) {
        super(messageService);
        this.shopifyCommonService = shopifyCommonService;
        this.objectMapper = objectMapper;
    }


    @Override
    public CreateCartWithFirstProductResDTO createCartWithFirstProduct(CreateCartWithFirstProductReqDTO createCartWithFirstProductReqDTO) {
        try {
            String requestBody = "{"
                    + "\"query\": \"" + createCartGraphQl() + "\","
                    + "\"variables\": {"
                    + "\"cartInput\": " + objectMapper.writeValueAsString(createCartWithFirstProductReqDTO.getCartInput())
                    + "}"
                    + "}";

            return shopifyCommonService.sendGraphQlRequestToShopify(requestBody, CreateCartWithFirstProductResDTO.class);
        } catch (Exception e) {
            logger.info("Unexpected error occurred while get create cart with first product: {}", e.getMessage());
            throw new BusinessValidationException("create_cart_with_product_failed", e.getMessage());
        }
    }

    @Override
    public AddProductToCartResDTO addProductToCart(AddProductToCartReqDTO addProductToCartReqDTO) {
        try {
            String requestBody = "{"
                    + "\"query\": \"" + addToCartGraphQl() + "\","
                    + "\"variables\": {"
                    + "\"cartId\": \"" + addProductToCartReqDTO.getCartId() + "\","
                    + "\"lines\": " + objectMapper.writeValueAsString(addProductToCartReqDTO.getLines())
                    + "}"
                    + "}";

            return shopifyCommonService.sendGraphQlRequestToShopify(requestBody, AddProductToCartResDTO.class);
        } catch (Exception e) {
            logger.info("Unexpected error occurred while add product to cart: {}", e.getMessage());
            throw new BusinessValidationException("add_product_to_cart_failed", e.getMessage());
        }
    }

    @Override
    public UpdateCartResDTO updateCart(UpdateCartReqDTO updateCartReqDTO) {
        try {
            String requestBody = "{"
                    + "\"query\": \"" + updateCartGraphQl() + "\","
                    + "\"variables\": {"
                    + "\"cartId\": \"" + updateCartReqDTO.getCartId() + "\","
                    + "\"lines\": " + objectMapper.writeValueAsString(updateCartReqDTO.getLines())
                    + "}"
                    + "}";

            return shopifyCommonService.sendGraphQlRequestToShopify(requestBody, UpdateCartResDTO.class);
        } catch (Exception e) {
            logger.info("Unexpected error occurred while update cart: {}", e.getMessage());
            throw new BusinessValidationException("update_cart_failed", e.getMessage());
        }
    }

    @Override
    public RemoveProductFromCartResDTO removeProductFromCart(RemoveProductFromCartReqDTO removeProductFromCartReqDTO) {
        try {
            String requestBody = "{"
                    + "\"query\": \"" + removeProductFromCartGraphQl() + "\","
                    + "\"variables\": {"
                    + "\"cartId\": \"" + removeProductFromCartReqDTO.getCartId() + "\","
                    + "\"lineIds\": " + objectMapper.writeValueAsString(removeProductFromCartReqDTO.getLinesId())
                    + "}"
                    + "}";
            return shopifyCommonService.sendGraphQlRequestToShopify(requestBody, RemoveProductFromCartResDTO.class);
        } catch (Exception e) {
            logger.info("Unexpected error occurred while remove product from cart: {}", e.getMessage());
            throw new BusinessValidationException("remove_cart_product_failed", e.getMessage());
        }
    }

    @Override
    public ViewCartResDTO viewCart(ViewCartReqDTO viewCartReqDTO) {
        try {
            String requestBody = "{"
                    + "\"query\": \"" + viewCartGraphQl() + "\","
                    + "\"variables\": {"
                    + "\"cartId\": \"" + viewCartReqDTO.getCartId() + "\""
                    + "}"
                    + "}";

            return shopifyCommonService.sendGraphQlRequestToShopify(requestBody, ViewCartResDTO.class);
        } catch (Exception e) {
            logger.info("Unexpected error occurred while view cart: {}", e.getMessage());
            throw new BusinessValidationException("view_cart_failed", e.getMessage());
        }
    }

    public String addToCartGraphQl() {
        return """
                mutation addCartLines($cartId: ID!, $lines: [CartLineInput!]!) {
                      cartLinesAdd(cartId: $cartId, lines: $lines) {
                        cart {
                          id
                          lines(first: 10) {
                            edges {
                              node {
                                quantity
                                merchandise {
                                  ... on ProductVariant {
                                    id
                                  }
                                }
                              }
                            }
                          }
                          cost {
                            totalAmount {
                              amount
                              currencyCode
                            }
                            subtotalAmount {
                              amount
                              currencyCode
                            }
                            totalTaxAmount {
                              amount
                              currencyCode
                            }
                            totalDutyAmount {
                              amount
                              currencyCode
                            }
                          }
                        }
                        userErrors {
                          field
                          message
                        }
                      }
                    }
                """;
    }

    public String createCartGraphQl() {
        return """ 
                mutation createCart($cartInput: CartInput) {
                    cartCreate(input: $cartInput) {
                      cart {
                        id
                        createdAt
                        updatedAt
                        checkoutUrl
                        lines(first: 10) {
                          edges {
                            node {
                              id
                              merchandise {
                                ... on ProductVariant {
                                  id
                                }
                              }
                            }
                          }
                        }
                        attributes {
                          key
                          value
                        }
                        cost {
                          totalAmount {
                            amount
                            currencyCode
                          }
                          subtotalAmount {
                            amount
                            currencyCode
                          }
                          totalTaxAmount {
                            amount
                            currencyCode
                          }
                          totalDutyAmount {
                            amount
                            currencyCode
                          }
                        }
                      }
                      userErrors {
                        field
                        message
                      }
                    }
                  }
                
                """;
    }

    public String viewCartGraphQl() {
        return """
                query cartQuery($cartId: ID!) {
                    cart(id: $cartId) {
                      id
                      createdAt
                      updatedAt
                      checkoutUrl
                      lines(first: 10) {
                        edges {
                          node {
                            id
                            quantity
                            merchandise {
                              ... on ProductVariant {
                                id
                                product {
                                  id
                                  title
                                  descriptionHtml
                                  vendor
                                  productType
                                  handle
                                  tags
                                  createdAt
                                  updatedAt
                                  publishedAt
                                  images(first: 5) {
                                    edges {
                                      node {
                                        id
                                        src
                                        altText
                                        width
                                        height
                                      }
                                    }
                                  }
                                  variants(first: 5) {
                                    edges {
                                      node {
                                        id
                                        title
                                        sku
                                        priceV2 {
                                          amount
                                          currencyCode
                                        }
                                        selectedOptions {
                                          name
                                          value
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                      cost {
                        totalAmount {
                          amount
                          currencyCode
                        }
                        subtotalAmount {
                          amount
                          currencyCode
                        }
                        totalTaxAmount {
                          amount
                          currencyCode
                        }
                        totalDutyAmount {
                          amount
                          currencyCode
                        }
                      }
                      buyerIdentity {
                        email
                        phone
                        customer {
                          id
                        }
                        countryCode
                      }
                    }
                  }
                
                """;
    }

    public String viewCartGraphQlCopy() {
        return """
            query cartQuery($cartId: ID!) {
                cart(id: $cartId) {
                    id
                    createdAt
                    updatedAt
                    checkoutUrl
                    lines(first: 10) {
                        edges {
                            node {
                                id
                                quantity
                                merchandise {
                                    ... on ProductVariant {
                                        id
                                        product {
                                            id
                                            title
                                            descriptionHtml
                                            vendor
                                            productType
                                            handle
                                            tags
                                            createdAt
                                            updatedAt
                                            publishedAt
                                            images(first: 5) {
                                                edges {
                                                    node {
                                                        id
                                                        src
                                                        altText
                                                        width
                                                        height
                                                    }
                                                }
                                            }
                                            variants(first: 5) {
                                                edges {
                                                    node {
                                                        id
                                                        title
                                                        sku
                                                        priceV2 {
                                                            amount
                                                            currencyCode
                                                        }
                                                        selectedOptions {
                                                            name
                                                            value
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    cost {
                        totalAmount {
                            amount
                            currencyCode
                        }
                        subtotalAmount {
                            amount
                            currencyCode
                        }
                        totalTaxAmount {
                            amount
                            currencyCode
                        }
                        totalDutyAmount {
                            amount
                            currencyCode
                        }
                    }
                    buyerIdentity {
                        email
                        phone
                        customer {
                            id
                            addresses(first: 5) {
                                edges {
                                    node {
                                        name
                                        address1
                                        address2
                                        city
                                        province
                                        country
                                        zip
                                    }
                                }
                            }
                        }
                        deliveryAddressPreferences {
                            ... on MailingAddress {
                                name
                                address1
                                address2
                                city
                                province
                                country
                                zip
                            }
                        }
                        countryCode
                    }
                }
            }
            """;
    }

    public String updateCartGraphQl() {
        return """
                mutation updateCartLines($cartId: ID!, $lines: [CartLineUpdateInput!]!) {
                   cartLinesUpdate(cartId: $cartId, lines: $lines) {
                     cart {
                       id
                       lines(first: 10) {
                         edges {
                           node {
                             id
                             quantity
                             merchandise {
                               ... on ProductVariant {
                                 id
                               }
                             }
                           }
                         }
                       }
                       cost {
                         totalAmount {
                           amount
                           currencyCode
                         }
                         subtotalAmount {
                           amount
                           currencyCode
                         }
                         totalTaxAmount {
                           amount
                           currencyCode
                         }
                         totalDutyAmount {
                           amount
                           currencyCode
                         }
                       }
                     }
                     userErrors {
                       field
                       message
                     }
                   }
                 }
                
                """;
    }

    public String removeProductFromCartGraphQl() {
        return """
                mutation removeCartLines($cartId: ID!, $lineIds: [ID!]!) {
                  cartLinesRemove(cartId: $cartId, lineIds: $lineIds) {
                    cart {
                      id
                        lines(first: 10){
                            edges
                            {
                                node{
                                    quantity
                                    merchandise{
                                        ... on ProductVariant {
                                            id
                                        }
                                    }
                                }
                            }
                        }
                        cost {
                        totalAmount {
                          amount
                          currencyCode
                        }
                        subtotalAmount {
                          amount
                          currencyCode
                        }
                        totalTaxAmount {
                          amount
                          currencyCode
                        }
                        totalDutyAmount {
                          amount
                          currencyCode
                        }
                      }
                    }
                    userErrors {
                      field
                      message
                    }
                  }
                }
                """;
    }


}
