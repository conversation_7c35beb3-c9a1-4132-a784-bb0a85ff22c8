package com.mindbody.api.service;

import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.cms.AddDailyTipsReqDTO;
import com.mindbody.api.dto.cms.DailyTipsResSTO;
import com.mindbody.api.dto.cms.EditDailyTipsReqDTO;
import org.springframework.web.multipart.MultipartFile;

public interface DailyTipsService {

    void addDailyTips(AddDailyTipsReqDTO addDailyTipsReqDTO, MultipartFile dailyTipsFile);

    SearchResultDTO<DailyTipsResSTO> listDailyTips(CommonListDTO commonListDTO);

    void editDailyTips(EditDailyTipsReqDTO editDailyTipsReqDTO);

    void deleteDailyTip(Long dailyTipsId);

    boolean activeInactiveDailyTip(Long dailyTipsId);

    void updateDailyTipsScheduler();

}
