package com.mindbody.api.service;

import com.mindbody.api.dto.AudioListReqDTO;
import com.mindbody.api.dto.CommonAudioListUserReqDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.UserAudioListResDTO;
import com.mindbody.api.dto.cms.AddAudioReqDTO;
import com.mindbody.api.dto.cms.AudioResDTO;
import com.mindbody.api.dto.cms.EditAudioReqDTO;

public interface AudioService {

    void addAudio(AddAudioReqDTO addAudioReqDTO);

    void updateAudio(EditAudioReqDTO editAudioReqDTO);

    void deleteAudio(Long audioId);

    boolean activeInactiveAudio(Long audioId);

    SearchResultDTO<AudioResDTO> listAudioCms(AudioListReqDTO audioListReqDTO);

    SearchResultDTO<UserAudioListResDTO> listAudioUser(CommonAudioListUserReqDTO listUserReqDTO);


}
