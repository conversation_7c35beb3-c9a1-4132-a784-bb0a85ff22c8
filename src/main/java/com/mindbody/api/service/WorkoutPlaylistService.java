package com.mindbody.api.service;

import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.UserWorkoutPlaylistResDTO;
import com.mindbody.api.dto.cms.AddWorkoutPlaylistReqDTO;
import com.mindbody.api.dto.cms.EditWorkoutPlaylistReqDTO;
import com.mindbody.api.dto.cms.WorkoutPlaylistDetailResDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface WorkoutPlaylistService {

    void addWorkoutPlaylist(MultipartFile workoutPlaylistImage, AddWorkoutPlaylistReqDTO addWorkoutPlaylistReqDTO);

    void editWorkoutPlaylist(MultipartFile workoutPlaylistImage, EditWorkoutPlaylistReqDTO editWorkoutPlaylistReqDTO);

    SearchResultDTO<WorkoutPlaylistDetailResDTO> listWorkoutPlaylist(CommonListDTO commonListDTO, boolean checkIsActiveRecord);

    void deleteWorkoutPlaylist(Long workoutPlaylistId);

    String activeInactiveWorkoutPlaylist(Long workoutPlaylistId);

    List<UserWorkoutPlaylistResDTO> listWorkoutPlaylistForSelectedWorkoutPlaylistType(String workoutPlaylistType);
}
