package com.mindbody.api.service;

import com.mindbody.api.dto.*;
import com.mindbody.api.model.EntityUser;

import java.time.LocalDateTime;
import java.util.List;

public interface AstrologyService {

    GenerateUserAstrologyResDTO generateUserAstrologyDetails(UserIdDTO userIdDTO);

    String updateUserAstrologyDetails(Long userId, double latitude, double longitude, LocalDateTime date, String timezone);

    List<UserAstrologyDetailResDTO> saveUserPlanetDetails(LocalDateTime dateOfBirth, String timezone, double latitude, double longitude, EntityUser entityUser);

    UserDailyHoroscopeResDTO generateUserDailyHoroscope(UserIdDTO userIdDTO);

    UserMonthlyHoroscopeResDTO generateUserMonthlyHoroscope(UserIdDTO userIdDTO);



}

















