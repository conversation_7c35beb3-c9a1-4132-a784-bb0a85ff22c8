package com.mindbody.api.service;

import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.SearchResultDTO;
import com.mindbody.api.dto.cms.AddMagazineReqDTO;
import com.mindbody.api.dto.cms.EditMagazineReqDTO;
import com.mindbody.api.dto.cms.MagazineDetailResDTO;

public interface MagazineService {

    void addMagazine(AddMagazineReqDTO addMagazineReqDTO);

    void editMagazine(EditMagazineReqDTO editMagazineReqDTO);

    SearchResultDTO<MagazineDetailResDTO> listMagazine(CommonListDTO commonListDTO, boolean checkIsActiveRecord);

    String activeInactiveMagazine(Long magazineId);
}
