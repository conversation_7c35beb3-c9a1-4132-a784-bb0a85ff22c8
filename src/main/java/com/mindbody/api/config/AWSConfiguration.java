package com.mindbody.api.config;

import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for AWS s3 bucket.
 */
@Configuration
public class AWSConfiguration {

    @Autowired
    private GlobalConfiguration globalConfiguration;

    @Bean
    public AmazonS3 s3client() {
        return AmazonS3ClientBuilder
                .standard()
                .withRegion(globalConfiguration.getAmazonS3().getRegion())
                .withCredentials(getAWSCredentialProvider())
                .build();
    }

    /**
     * This method use <PERSON><PERSON><PERSON>,SecretKey from Constant file.
     *
     * @return provides authentication for AWS.
     */
    private AWSCredentialsProvider getAWSCredentialProvider() {
        BasicAWSCredentials credentials = new BasicAWSCredentials(globalConfiguration.getAmazonS3().getAccessKey(), globalConfiguration.getAmazonS3().getSecretKey());
        return new AWSStaticCredentialsProvider(credentials);
    }

    @Bean
    TransferManager s3TransferManager(AmazonS3 amazonS3) {
        return TransferManagerBuilder.standard().withS3Client(amazonS3).build();
    }
}
