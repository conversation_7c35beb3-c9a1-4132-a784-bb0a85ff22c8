package com.mindbody.api.config;


import com.mindbody.api.security.CustomAuthenticationEntryPoint;
import com.mindbody.api.security.CustomUserDetailsService;
import com.mindbody.api.security.JwtAuthenticationFilter;
import com.mindbody.api.util.Urls;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.firewall.DefaultHttpFirewall;
import org.springframework.security.web.firewall.HttpFirewall;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.List;

import static org.springframework.security.config.Customizer.withDefaults;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(
        securedEnabled = true,
        jsr250Enabled = true
)
public class SecurityConfig {

    private final CustomUserDetailsService userDetailsService;
    private final CustomAuthenticationEntryPoint point;
    private final PasswordEncoder passwordEncoder;

    public SecurityConfig(CustomUserDetailsService userDetailsService, CustomAuthenticationEntryPoint point, PasswordEncoder passwordEncoder) {
        this.userDetailsService = userDetailsService;
        this.point = point;
        this.passwordEncoder = passwordEncoder;
    }

    @Bean
    public DaoAuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder);
        return authProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager(HttpSecurity http) throws Exception {
        return http.getSharedObject(AuthenticationManagerBuilder.class)
                .authenticationProvider(daoAuthenticationProvider())
                .build();
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable)
                .cors(withDefaults())
                .authorizeHttpRequests(authorizeRequests ->
                        authorizeRequests
                                .requestMatchers(
                                        "/v2/api-docs/**",
                                        "/v3/api-docs/**",
                                        "/v3/api-docs",
                                        "/configuration/ui",
                                        "/swagger-resources/**",
                                        "/configuration/security",
                                        "/swagger-ui.html",
                                        "/swagger-ui/**",
                                        "/favicon.ico",
                                        "/error",
                                        "/webjars/**",
                                        "/actuator/**",
                                        "/ws/**",
                                        "/mypath/**",
                                        "/socket.io/**",
                                        "/.well-known/**",
                                        Urls.BASE_PATH_CMS + Urls.ADMIN + Urls.LOGIN,
                                        Urls.BASE_PATH + Urls.USER + Urls.ACTIVE_INACTIVE,
                                        Urls.BASE_PATH + Urls.USER + Urls.QUESTION + Urls.LIST,
                                        Urls.BASE_PATH + Urls.USER + Urls.QUESTION + Urls.ANSWER,
                                        Urls.BASE_PATH + Urls.USER + Urls.SIGN_UP,
                                        Urls.BASE_PATH_CMS + Urls.ADMIN + Urls.SEND_FORGOT_PASSWORD_LINK,
                                        Urls.BASE_PATH_CMS + Urls.ADMIN + Urls.RESET_PASSWORD,
                                        Urls.BASE_PATH + Urls.USER + Urls.SEND_OTP,
                                        Urls.BASE_PATH + Urls.USER + Urls.VERIFY_OTP,
                                        Urls.BASE_PATH + Urls.USER + Urls.RESET_PASSWORD_USER,
                                        Urls.BASE_PATH + Urls.USER + Urls.LOGIN,
                                        Urls.BASE_PATH + Urls.USER + Urls.SOCIAL_MEDIA + Urls.LOGIN,
                                        Urls.BASE_PATH + Urls.USER + Urls.SHOPIFY + Urls.WEBHOOK + Urls.CUSTOMERS_CREATE,
                                        Urls.BASE_PATH + Urls.USER + Urls.SHOPIFY + Urls.WEBHOOK + Urls.ORDERS_CREATE,
                                        Urls.BASE_PATH + Urls.USER + Urls.SHOPIFY + Urls.WEBHOOK + Urls.ORDERS_PAID,
                                        Urls.BASE_PATH + Urls.USER + Urls.SHOPIFY + Urls.WEBHOOK + Urls.ORDERS_PAYMENT,
                                        Urls.BASE_PATH + Urls.USER + Urls.SHOPIFY + Urls.WEBHOOK + Urls.ORDERS_FULFILLMENT,
                                        Urls.BASE_PATH + Urls.USER + Urls.REACTIVATE
                                ).permitAll()
                                .anyRequest().authenticated()
                )
                .exceptionHandling(ex -> ex.authenticationEntryPoint(point))
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));

        http.addFilterAfter(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
        return http.build();
    }

    @Bean
    public JwtAuthenticationFilter jwtAuthenticationFilter() {
        return new JwtAuthenticationFilter();
    }

    @Bean
    public HttpFirewall allowUrlEncodedSlashHttpFirewall() {
        DefaultHttpFirewall firewall = new DefaultHttpFirewall();
        firewall.setAllowUrlEncodedSlash(true);
        return firewall;
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(List.of("*"));
        configuration.setAllowedMethods(List.of("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(List.of("*"));
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
