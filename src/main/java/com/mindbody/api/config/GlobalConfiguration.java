package com.mindbody.api.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;


@Configuration
@PropertySource(factory = YamlPropertySourceFactory.class, value = "classpath:globalconfig-${spring.profiles.active:localhost}.yml")

@ConfigurationProperties(prefix = "app")
@Getter
@Setter
@NoArgsConstructor
public class GlobalConfiguration {

    private AmazonS3 amazonS3;

    private MailConfig mailConfig;

    private TwillioConfig twillioConfig;

    private AstrologyConfig astrologyConfig;

    private ShopifyConfig shopifyConfig;


    @Getter
    @Setter
    @NoArgsConstructor
    public static class AmazonS3 {

        private String accessKey;

        private String region;

        private String secretKey;

        private String bucket;

        private String urlUpToBucket;

        private String userFolderName;

        private String uploadFolderName;

        private String adminFolderName;

        private String questionOptionFolderName;

        private String userProfileFolderName;

        private String magazineFolderName;

        private String magazineThumbnailFolderName;

        private String productCategoryFolderName;

        private String audioPlaylistFolderName;

        private String workoutPlaylistFolderName;

        private String audioFileFolderName;

        private String audioThumbnailFolderName;

        private String exerciseVideoFolderName;

        private String exerciseThumbnailFolderName;

        private String workoutVideoFolderName;

        private String workoutVideoThumbnailFolderName;

        private String achievementBadgeFolderName;

        private String achievementBorderFolderName;

        private String soundFileFolderName;

        private String notificationImageFolderName;

        private String rewardThumbnailFolderName;

        private String rewardItemAssetsFolderName;

    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class MailConfig {

        private String mailFrom;

        private String mailFromAuth;

        private String mailPassword;

        private String mailService;

        private String mailHost;

        private String mailPort;

        private String mailMethod;

        private String mailSecure;

    }


    @Getter
    @Setter
    @NoArgsConstructor
    public static class TwillioConfig {

        private String accountSid;

        private String authToken;

        private String fromNumber;
    }


    @Getter
    @Setter
    @NoArgsConstructor
    public static class AstrologyConfig {

        private String url;

        private String username;

        private String password;
    }


    @Getter
    @Setter
    @NoArgsConstructor
    public static class ShopifyConfig {

        private String url;

        private String version;

        private String shopifyAccessToken;

        private String shopifyStorefrontAccessToken;
    }


}
