package com.mindbody.api.config;


import com.mindbody.api.logging.LoggableRequest;
import com.mindbody.api.security.ExecutionContextUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Optional;


public class HttpApiInterceptor implements HandlerInterceptor {


    private static final String START_TIME = "startTime";
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpApiInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        request.setAttribute(START_TIME, System.currentTimeMillis());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        LoggableRequest requestLog = new LoggableRequest();

        long startTime = (Long) request.getAttribute(START_TIME);
        long timeTakenInMs = System.currentTimeMillis() - startTime;

        Optional<String> ipAddr = getIPAddress(request);
        ipAddr.ifPresent(requestLog::setRequestedHost);

        requestLog.setResponseStatus(response.getStatus());
        requestLog.setUrl(request.getRequestURL().toString());
        requestLog.setMethod(request.getMethod());
        requestLog.setTimeTaken(timeTakenInMs);

        // Logging for slow response
        if (timeTakenInMs > 4000) {
            LOGGER.info("Slow request detected: {}", requestLog);
        }

        LOGGER.debug("Request details: {}", requestLog);

        // Clean up MDC and ExecutionContextUtil if necessary
        try {
            ExecutionContextUtil.getContext().destroy();
        } catch (Exception e) {
            LOGGER.error("Error destroying execution context", e);
        }

        try {
            MDC.clear();
        } catch (Exception e) {
            LOGGER.error("Error clearing MDC", e);
        }
    }

    private Optional<String> getIPAddress(HttpServletRequest request) {
        String ipAddress = request.getHeader("X-FORWARDED-FOR");
        if (ipAddress != null && !ipAddress.isEmpty()) {
            return Optional.of(ipAddress);
        }
        return Optional.of(request.getRemoteAddr());
    }
}

