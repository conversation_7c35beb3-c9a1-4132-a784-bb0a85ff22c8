package com.mindbody.api.repository;

import com.mindbody.api.model.EntityNotificationSchedule;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface NotificationScheduleRepository extends JpaRepository<EntityNotificationSchedule, Long> {

    // Custom query to find notifications that are scheduled but not yet sent
    @Query("SELECT n FROM EntityNotificationSchedule n WHERE n.scheduledTime <= :currentTime AND n.sent = false")
    List<EntityNotificationSchedule> findTop5000PendingNotifications(Pageable pageable, @Param("currentTime") LocalDateTime currentTime);

    void deleteBySent(boolean sent);
}
