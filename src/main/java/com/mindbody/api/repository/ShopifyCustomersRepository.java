package com.mindbody.api.repository;


import com.mindbody.api.model.EntityShopifyCustomers;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ShopifyCustomersRepository extends JpaRepository<EntityShopifyCustomers,Long> {
    EntityShopifyCustomers findByUserId(Long userId);
    EntityShopifyCustomers findByCustomerId(String customerId);
}
