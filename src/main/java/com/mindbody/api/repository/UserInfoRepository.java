package com.mindbody.api.repository;

import com.mindbody.api.dto.LeaderboardResDTO;
import com.mindbody.api.dto.UserCurrentConnectionDTO;
import com.mindbody.api.model.EntityUserInfo;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
@Transactional
public interface UserInfoRepository extends JpaRepository<EntityUserInfo,Long> {

    EntityUserInfo findByUserIdAndIsActiveAndIsDeleted(Long userId, boolean isActive, boolean isDeleted);

    EntityUserInfo findByUserIdAndIsDeleted(Long userId, boolean isDeleted);

    List<EntityUserInfo> findByDateOfBirthAndIsDeleted(LocalDateTime dateOfBirth, boolean isDeleted);

    @Query("SELECT ui FROM EntityUserInfo ui WHERE FUNCTION('MONTH', ui.dateOfBirth) = FUNCTION('MONTH', CURRENT_DATE) " +
            "AND FUNCTION('DAYOFMONTH', ui.dateOfBirth) = FUNCTION('DAYOFMONTH', CURRENT_DATE)")
    List<EntityUserInfo> findUsersWithBirthdayToday();

    EntityUserInfo findByUserId(Long userId);

    Optional<List<EntityUserInfo>> findByIsDeletedAndIsActive(boolean isDeleted, boolean isActive);

    Optional<List<EntityUserInfo>> findByUserIdInAndIsActiveAndIsDeleted(Long[] userIds, boolean isActive, boolean isDeleted);


    @Query("""
            SELECT new com.mindbody.api.dto.UserCurrentConnectionDTO(
                u.facebookEmailId,
                u.facebookSocialMediaId,
                ui.facebookAccountName,
                u.appleEmailId,
                u.appleSocialMediaId,
                ui.appleAccountName,
                u.googleEmailId,
                u.googleSocialMediaId,
                ui.googleAccountName
            )
            FROM EntityUser u LEFT JOIN EntityUserInfo ui ON u.userId = ui.userId
            WHERE u.userId = :userId AND u.isActive = true AND u.isDeleted = false
            """)
    Optional<UserCurrentConnectionDTO> listCurrentConnection(Long userId);



//    @Query("""
//            SELECT new com.mindbody.api.dto.LeaderboardResDTO(
//                ui.name,
//                ui.facebookAccountName,
//                ui.googleAccountName,
//                ui.appleAccountName,
//                ui.titleAchievementId.entityAchievementTitle.title,
//                (CASE WHEN ui.medalAchievementId IS NOT NULL THEN CONCAT(:badgeImagePath, ma.badgeImage) ELSE ma.badgeImage END),
//                (CASE WHEN ui.borderAchievementId IS NOT NULL THEN CONCAT(:borderImagePath, ba.borderImage) ELSE ba.borderImage END),
//                u.userId,
//                ui.zodiacSign,
//                upt.currentLevel,
//                upt.totalPoints,
//                upt.totalWxp,
//                utp.mindCategoryTimeSpent
//            )
//            FROM EntityUser u
//            LEFT JOIN EntityUserInfo ui ON u.userId = ui.userId
//            LEFT JOIN EntityUserPointsTracker upt ON u.userId = upt.userId
//            WHERE u.userId = :userId AND u.isActive = true AND u.isDeleted = false
//
//            """)
//    Optional<List<LeaderboardResDTO>> generateLeaderboard(Long userId, String borderImagePath, String badgeImagePath);

    /**
     * Finds users within a specified radius (in kilometers) from a given latitude and longitude.
     *
     * @param latitude  The latitude of the center point.
     * @param longitude The longitude of the center point.
     * @param radius    The radius in kilometers.
     * @return A list of users within the specified radius.
     */
    @Query("SELECT e FROM EntityUserInfo e WHERE " +
            "(6371 * acos(cos(radians(:latitude)) * cos(radians(e.latitude)) * " +
            "cos(radians(e.longitude) - radians(:longitude)) + sin(radians(:latitude)) * sin(radians(e.latitude)))) <= :radius")
    List<EntityUserInfo> findUsersWithinRadius(@Param("latitude") Double latitude,
                                               @Param("longitude") Double longitude,
                                               @Param("radius") Double radius);

}
