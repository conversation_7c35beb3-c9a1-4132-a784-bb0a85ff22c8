package com.mindbody.api.repository;

import com.mindbody.api.dto.AchievementDTO;
import com.mindbody.api.dto.CategoryAndCountDTO;
import com.mindbody.api.dto.cms.AchievementListResDTO;
import com.mindbody.api.enums.AchievementActivityType;
import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.model.EntityAchievements;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AchievementsRepository extends JpaRepository<EntityAchievements, Long> {

    @Query("SELECT COUNT(a) " +
            "FROM EntityAchievements a " +
            "LEFT JOIN a.entityAudioPlaylist ap " +
            "LEFT JOIN a.entityWorkoutPlaylist wp " +
            "LEFT JOIN a.entityWarriorSet ws " +
            "WHERE a.isDeleted = :isDeleted " +
            "AND (:isAchievementCategoryTypeFilter = false OR a.achievementCategoryType = :achievementCategoryType) " +
            "AND (" +
            "  (LOWER(CONCAT(ap.audioPlaylistName, '') ) LIKE LOWER(CONCAT('%', :queryToSearch, '%'))) " +
            "  OR (LOWER(CONCAT(wp.workoutPlaylistName, '') ) LIKE LOWER(CONCAT('%', :queryToSearch, '%'))) " +
            "  OR (LOWER(CONCAT(ws.warriorSetName, '') ) LIKE LOWER(CONCAT('%', :queryToSearch, '%'))) " +
            ")"
    )
    long countAchievements(String queryToSearch, AchievementCategoryType achievementCategoryType, boolean isAchievementCategoryTypeFilter, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.cms.AchievementListResDTO( " +
            "a.achievementsId, " +
            "a.achievementCategoryType, " +
            "CASE WHEN a.mindSubCategoryType IS NULL THEN 'DEFAULT' ELSE CONCAT(a.mindSubCategoryType, '') END, " +
            "CASE WHEN a.bodySubCategoryType IS NULL THEN 'DEFAULT' ELSE CONCAT(a.bodySubCategoryType, '') END, " +
            "CASE WHEN a.warriorSubCategoryType IS NULL THEN 'DEFAULT' ELSE CONCAT(a.warriorSubCategoryType, '') END, " +
            "CASE WHEN a.mindSubCategoryType != 'DEFAULT' THEN CONCAT(a.mindSubCategoryType, '') " +
            "WHEN a.bodySubCategoryType != 'DEFAULT' THEN CONCAT(a.bodySubCategoryType, '') " +
            "WHEN a.warriorSubCategoryType != 'DEFAULT' THEN CONCAT(a.warriorSubCategoryType, '') " +
            "ELSE CONCAT('DEFAULT', '') " +
            "END, " +
            "a.activityDetails, " +
            "t.title, " +
            "b.border, " +
            "m.medalName, " +
            "ap.audioPlaylistName, " +
            "wp.workoutPlaylistName, " +
            "ws.warriorSetName, " +
            "CASE WHEN ap.audioPlaylistName IS NOT NULL THEN CONCAT(ap.audioPlaylistName, '') " +
            "WHEN wp.workoutPlaylistName IS NOT NULL THEN CONCAT(wp.workoutPlaylistName, '') " +
            "WHEN ws.warriorSetName IS NOT NULL THEN CONCAT(ws.warriorSetName, '') " +
            "END, " +
            "b.achievementBorderId, " +
            "m.achievementMedalId, " +
            "t.achievementTitleId, " +
            "CONCAT(:badgeImagePath, a.badgeImage)," +
            "CONCAT(:borderImagePath, a.borderImage)" +
            ") " +
            "FROM EntityAchievements a " +
            "LEFT JOIN a.entityAchievementTitle t " +
            "LEFT JOIN a.entityAchievementBorder b " +
            "LEFT JOIN a.entityAchievementMedals m " +
            "LEFT JOIN a.entityAudioPlaylist ap " +
            "LEFT JOIN a.entityWorkoutPlaylist wp " +
            "LEFT JOIN a.entityWarriorSet ws " +
            "WHERE a.isDeleted = :isDeleted " +
            "AND (:isAchievementCategoryTypeFilter = false OR a.achievementCategoryType = :achievementCategoryType) " +
            "AND (" +
            "(lower(CONCAT(ap.audioPlaylistName, '')) LIKE lower(concat('%',:queryToSearch,'%'))) " +
            "OR (lower(CONCAT(wp.workoutPlaylistName, '')) LIKE lower(concat('%',:queryToSearch,'%'))) " +
            "OR (lower(CONCAT(ws.warriorSetName, '')) LIKE lower(concat('%',:queryToSearch,'%'))) " +
            ")"
    )
    List<AchievementListResDTO> findAllAchievements(String queryToSearch,
                                                    Pageable pageable,
                                                    AchievementCategoryType achievementCategoryType,
                                                    String badgeImagePath,
                                                    String borderImagePath,
                                                    boolean isAchievementCategoryTypeFilter,
                                                    boolean isDeleted);


    EntityAchievements findByAchievementsIdAndIsDeleted(Long achievementId, boolean isDeleted);

    Integer countByEntityAchievementTitle_AchievementTitleIdAndAchievementsIdIsNot(Long achievementTitleId, Long achievementsId);


    List<EntityAchievements> findByAchievementCategoryTypeAndIsDeleted(AchievementCategoryType selectedCategoryType, boolean isDeleted);

    Integer countByEntityAudioPlaylist_AudioPlaylistId(Long audioPlaylistId);

    Integer countByEntityWorkoutPlaylist_WorkoutPlaylistId(Long workoutPlaylistId);

    Integer countByEntityWarriorSet_WarriorSetId(Long warriorSetId);

    @Query("SELECT COUNT(e) FROM EntityAchievements e " +
            "WHERE e.isActive = true AND e.isDeleted = false AND e.achievementCategoryType = :achievementCategoryType")
    Integer countAchievementsByCategoryType(AchievementCategoryType achievementCategoryType);

    @Query("SELECT DISTINCT e.achievementCategoryType FROM EntityAchievements e " +
            "WHERE e.isActive = true AND e.isDeleted = false")
    List<AchievementCategoryType> findAllDistinctAchievementCategories();

    Optional<EntityAchievements> findByAchievementActivityTypeAndIsDeleted(AchievementActivityType achievementActivityType, boolean isDeleted);

    @Query("""
                SELECT new com.mindbody.api.dto.AchievementDTO(
                    a.achievementsId,
                    a.achievementCategoryType,
                    CASE WHEN a.mindSubCategoryType != 'DEFAULT' THEN CONCAT(a.mindSubCategoryType, '')
                         WHEN a.bodySubCategoryType != 'DEFAULT' THEN CONCAT(a.bodySubCategoryType, '')
                         WHEN a.warriorSubCategoryType != 'DEFAULT' THEN CONCAT(a.warriorSubCategoryType, '')
                         ELSE CONCAT('DEFAULT', '') END,
                    a.activityDetails,
                    a.achievementActivityType,
                    at.title,
                    a.isDeleted,
                    a.isActive,
                    ua.userAchievementsId,
                    ua.userId,
                    ua.pointsEarned,
                    ua.wxpEarned,
                    ua.acquiredDate,
                    ua.isClaimed,
                    m.medalName
                    )
                    FROM EntityAchievements a
                    LEFT JOIN a.entityAchievementTitle at
                    LEFT JOIN EntityUserAchievements ua ON a.achievementsId = ua.entityAchievements.achievementsId
                    AND ua.achievementTitleId.achievementTitleId = at.achievementTitleId
                    AND ua.userId = :userId
                    LEFT JOIN a.entityAchievementMedals m
                    WHERE a.entityAudioPlaylist.audioPlaylistId = :audioPlaylistId
            """)
    List<AchievementDTO> findAchievementsByAudioPlaylist(@Param("audioPlaylistId") Long audioPlaylistId, @Param("userId") Long userId);


    @Query("""
                SELECT new com.mindbody.api.dto.AchievementDTO(
                    a.achievementsId,
                    a.achievementCategoryType,
                    CASE WHEN a.mindSubCategoryType != 'DEFAULT' THEN CONCAT(a.mindSubCategoryType, '')
                         WHEN a.bodySubCategoryType != 'DEFAULT' THEN CONCAT(a.bodySubCategoryType, '')
                         WHEN a.warriorSubCategoryType != 'DEFAULT' THEN CONCAT(a.warriorSubCategoryType, '')
                         ELSE CONCAT('DEFAULT', '') END,
                    a.activityDetails,
                    a.achievementActivityType,
                    at.title,
                    a.isDeleted,
                    a.isActive,
                    ua.userAchievementsId,
                    ua.userId,
                    ua.pointsEarned,
                    ua.wxpEarned,
                    ua.acquiredDate,
                    ua.isClaimed,
                    m.medalName
                    )
                    FROM EntityAchievements a
                    LEFT JOIN a.entityAchievementTitle at
                    LEFT JOIN EntityUserAchievements ua ON a.achievementsId = ua.entityAchievements.achievementsId
                    AND ua.achievementTitleId.achievementTitleId = at.achievementTitleId
                    AND ua.userId = :userId
                    LEFT JOIN a.entityAchievementMedals m
                    WHERE a.entityWorkoutPlaylist.workoutPlaylistId = :workoutPlaylistId
            """)
    List<AchievementDTO> findAchievementsByWorkoutPlaylist(@Param("workoutPlaylistId") Long workoutPlaylistId,
                                                           @Param("userId") Long userId);

    //
//
//
    @Query("""
                            SELECT new com.mindbody.api.dto.AchievementDTO(
                                a.achievementsId,
                                a.achievementCategoryType,
                                CASE WHEN a.mindSubCategoryType != 'DEFAULT' THEN CONCAT(a.mindSubCategoryType, '')
                                     WHEN a.bodySubCategoryType != 'DEFAULT' THEN CONCAT(a.bodySubCategoryType, '')
                                     WHEN a.warriorSubCategoryType != 'DEFAULT' THEN CONCAT(a.warriorSubCategoryType, '')
                                     ELSE CONCAT('DEFAULT', '') END,
                                a.activityDetails,
                                a.achievementActivityType,
                                at.title,
                                a.isDeleted,
                                a.isActive,
                                ua.userAchievementsId,
                                ua.userId,
                                ua.pointsEarned,
                                ua.wxpEarned,
                                ua.acquiredDate,
                                ua.isClaimed,
                                m.medalName
                                )
                                FROM EntityAchievements a
                                LEFT JOIN a.entityAchievementTitle at
                                LEFT JOIN EntityUserAchievements ua ON a.achievementsId = ua.entityAchievements.achievementsId
                                AND ua.achievementTitleId.achievementTitleId = at.achievementTitleId
                                AND ua.userId = :userId
                                LEFT JOIN a.entityAchievementMedals m
                                WHERE a.entityWarriorSet.warriorSetId = :warriorSetId
            """)
    List<AchievementDTO> findAchievementsByWarriorPlaylist(@Param("warriorSetId") Long warriorSetId,
                                                           @Param("userId") Long userId);


    @Query("""
            SELECT new com.mindbody.api.dto.CategoryAndCountDTO(
            COUNT(a),
            a.achievementCategoryType)
            FROM EntityAchievements a
            WHERE a.isDeleted=:isDeleted
            GROUP BY a.achievementCategoryType
            """)
    List<CategoryAndCountDTO> countAchievementsByCategoriesAndIsDeleted(boolean isDeleted);


    @Query("""
            SELECT new com.mindbody.api.dto.CategoryAndCountDTO(
            COUNT(a),
            a.achievementCategoryType)
            FROM EntityAchievements a
            WHERE a.badgeImage IS NOT NULL AND  a.isDeleted=:isDeleted
            GROUP BY a.achievementCategoryType
            """)
    List<CategoryAndCountDTO> countAchievementsByCategoriesByBadgesAndIsDeleted(boolean isDeleted);

    @Query("""
            SELECT new com.mindbody.api.dto.CategoryAndCountDTO(
            COUNT(a),
            a.achievementCategoryType)
            FROM EntityAchievements a
            WHERE a.borderImage IS NOT NULL AND  a.isDeleted=:isDeleted
            GROUP BY a.achievementCategoryType
            """)
    List<CategoryAndCountDTO> countAchievementsByCategoriesByBorderAndIsDeleted(boolean isDeleted);
}
