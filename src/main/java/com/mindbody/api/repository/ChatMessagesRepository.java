package com.mindbody.api.repository;


import com.mindbody.api.model.EntityChatMessages;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface ChatMessagesRepository extends JpaRepository<EntityChatMessages, Long> {

    @Query("SELECT cm.category AS chatCategory, " +
            "COUNT(CASE WHEN cm.isRead = false AND cm.userId = :userId THEN 1 END) AS unreadCount, " +
            "MAX(cm.createdAt) AS lastInteractionTime " +
            "FROM EntityChatMessages cm " +
            "WHERE cm.userId = :userId " +
            "GROUP BY cm.category")
    List<Object[]> findChatCategoryDetails(Long userId);

    @Modifying
    @Transactional
    @Query("UPDATE EntityChatMessages cm SET cm.isRead = true " +
            "WHERE cm.userId = :userId AND cm.category = :category AND cm.isRead = false")
    void markMessagesAsRead(Long userId, String category);

}
