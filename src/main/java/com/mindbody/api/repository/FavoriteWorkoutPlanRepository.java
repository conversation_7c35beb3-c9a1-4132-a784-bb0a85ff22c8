package com.mindbody.api.repository;

import com.mindbody.api.model.EntityFavoriteWorkoutPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface FavoriteWorkoutPlanRepository extends JpaRepository<EntityFavoriteWorkoutPlan, Long> {

    EntityFavoriteWorkoutPlan findByUserIdAndWorkoutPlanIdAndIsActiveAndIsDeleted(Long userId, Long workoutPlanId, boolean isActive, boolean isDeleted);

    @Transactional
    @Modifying
    @Query("DELETE FROM EntityFavoriteWorkoutPlan w WHERE w.workoutPlanId=:workoutPlanId")
    void deleteByWorkoutPlanId(Long workoutPlanId);
}
