package com.mindbody.api.repository;

import com.mindbody.api.dto.UserSignupResDTO;
import com.mindbody.api.dto.cms.UserManagementResDTO;
import com.mindbody.api.dto.cms.ViewUserDetailsResDTO;
import com.mindbody.api.dto.notification.FilterUsersForNotificationResDTO;
import com.mindbody.api.enums.GenderType;
import com.mindbody.api.enums.UserType;
import com.mindbody.api.model.EntityUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<EntityUser, Long> {

    @Query("SELECT u FROM EntityUser u WHERE u.email =:email AND u.isActive =:isActive AND u.isDeleted =:isDeleted")
    EntityUser findUserByEmail(String email, boolean isActive, boolean isDeleted);

    @Query("SELECT u FROM EntityUser u WHERE (u.email = :email OR u.facebookEmailId = :email OR u.appleEmailId = :email OR u.googleEmailId = :email) AND u.isActive = :isActive AND u.isDeleted = :isDeleted")
    EntityUser findUserByAllEmail(String email, boolean isActive, boolean isDeleted);

    @Query("SELECT u FROM EntityUser u WHERE u.phoneNumber =:phoneNumber AND u.countryCode =:countryCode AND u.isActive =:isActive AND u.isDeleted =:isDeleted")
    EntityUser findUserByCountryCodeAndPhoneNumber(String countryCode, String phoneNumber, boolean isActive, boolean isDeleted);

    EntityUser findByUserIdAndIsDeleted(Long userId, boolean isDeleted);

    EntityUser findByUserId(Long userId);

    boolean existsByEmailAndIsDeleted(String email, boolean isDeleted);

    @Query("SELECT COUNT(u) > 0 FROM EntityUser u WHERE (u.email = :email OR u.facebookEmailId = :email OR u.appleEmailId = :email OR u.googleEmailId = :email) AND u.isDeleted = :isDeleted AND u.userId != :userId")
    boolean existsByAllEmailAndIsDeleted(String email, boolean isDeleted, long userId);

    boolean existsByCountryCodeAndPhoneNumberAndIsDeleted(String countryCode, String phoneNumber, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.UserSignupResDTO(" +
            "u.userId, u.email, u.countryCode, u.phoneNumber, u.roleType, u.userType, u.accountStatus, u.registerType,u.isEmailRegistered, u.isProfileCompleted, u.createdAt,u.updatedAt, " +
            "u.isDeleted, u.isActive, ui.name, ui.dateOfBirth, ui.placeOfBirth, " +
            "(CASE WHEN ui.profileImage IS NOT NULL AND ui.profileImage <> '' THEN CONCAT(:profilePath, ui.profileImage) ELSE ui.profileImage END), " +
            "ui.genderType, ui.zodiacSign, ui.timezone,uc.cartId," +
            "(CASE WHEN ui.borderAchievementId IS NOT NULL THEN CONCAT(:borderImagePath, ea.borderImage) ELSE ea.borderImage END), " +
            "(CASE WHEN ui.medalAchievementId IS NOT NULL THEN CONCAT(:badgeImagePath, eb.badgeImage) ELSE eb.badgeImage END), " +
            "t.title," +
            "u.referralCode, " +
            "ui.googleAccountName, ui.appleAccountName, ui.facebookAccountName, " +
            "(CASE WHEN (SELECT COUNT(r) FROM EntityUserReferral r WHERE r.toUserId = u.userId AND r.isDeleted = false) > 0 THEN true ELSE false END)," +
            "ui.themeMode" +
            ") " +
            "FROM EntityUser u " +
            "LEFT JOIN EntityUserInfo ui ON u.userId = ui.userId " +
            "LEFT JOIN EntityUserCart uc ON u.userId = uc.userId " +
            "LEFT JOIN ui.titleAchievementId at " +
            "LEFT JOIN at.entityAchievementTitle t " +
            "LEFT JOIN ui.borderAchievementId ea " +
            "LEFT JOIN ui.medalAchievementId eb " +
            "WHERE u.userId = :userId AND u.isActive = true AND u.isDeleted = false")
    UserSignupResDTO findAllUserInformation(Long userId, String profilePath, String borderImagePath, String badgeImagePath);

    @Query("SELECT new com.mindbody.api.dto.cms.ViewUserDetailsResDTO(" +
            "u.userId, u.email, u.countryCode, u.phoneNumber, u.roleType, u.userType, u.accountStatus, u.registerType,u.isEmailRegistered, u.isProfileCompleted, " +
            "u.createdAt, u.updatedAt, u.isDeleted, u.isActive,u.referralCode, ui.name, ui.dateOfBirth, ui.placeOfBirth, " +
            "(CASE WHEN ui.profileImage IS NOT NULL AND ui.profileImage <> '' THEN CONCAT(:profilePath, ui.profileImage) ELSE ui.profileImage END), " +
            "ui.genderType,ui.zodiacSign, ui.timezone, " +
            "upt.totalPoints, upt.totalWxp, upt.currentLevel) " +
            "FROM EntityUser u " +
            "LEFT JOIN EntityUserInfo ui ON u.userId = ui.userId " +
            "LEFT JOIN EntityUserPointsTracker upt ON u.userId = upt.userId " +
            "WHERE u.userId = :userId AND u.isDeleted = false")
    ViewUserDetailsResDTO findAllUserInformationForCMS(Long userId, String profilePath);

    EntityUser findByUserIdAndIsActiveAndIsDeleted(Long userId, boolean isActive, boolean isDeleted);

    EntityUser findByEmailAndUserIdAndIsActiveAndIsDeleted(String email, Long userId, boolean isActive, boolean isDeleted);

    @Query("SELECT COUNT(u) FROM EntityUser u LEFT JOIN EntityUserInfo ui ON u.userId = ui.userId " +
            "WHERE (:checkIsUserType = false or u.userType=:userType )" +
            "AND (:userType='ANONYMOUS' OR lower(ui.name) LIKE lower(concat('%', :queryToSearch, '%')))" +
            "AND u.isDeleted =false ")
    long countUsers(String queryToSearch, boolean checkIsUserType, UserType userType);

    @Query("SELECT new com.mindbody.api.dto.cms.UserManagementResDTO(" +
            "u.userId, u.email, u.countryCode, u.phoneNumber, u.roleType, u.userType, u.accountStatus, u.registerType,u.isEmailRegistered, u.isProfileCompleted, " +
            "u.isDeleted, u.isActive, ui.name, ui.dateOfBirth, ui.placeOfBirth, " +
            "(CASE WHEN ui.profileImage IS NOT NULL AND ui.profileImage <> '' THEN CONCAT(:profilePath, ui.profileImage) ELSE ui.profileImage END), " +
            "ui.genderType,ui.zodiacSign, ui.timezone, u.createdAt, u.updatedAt) " +
            "FROM EntityUser u " +
            "LEFT JOIN EntityUserInfo ui ON u.userId = ui.userId " +
            "WHERE (:checkIsUserType = false OR u.userType = :userType) " +
            "AND (:userType='ANONYMOUS' OR lower(ui.name) LIKE lower(concat('%', :queryToSearch, '%')))" +
            "AND u.isDeleted = false ")
    Page<UserManagementResDTO> listUsers(boolean checkIsUserType, String queryToSearch, String profilePath, UserType userType, Pageable pageable);

    @Query(nativeQuery = true, name = "EntityUser.listUsersBasedOnGenderAndAgeFilters")
    List<FilterUsersForNotificationResDTO> listUsersBasedOnGenderAndAgeFilters(
            @Param("checkIsGenderType") Boolean checkIsGenderType,
            @Param("queryToSearch") String queryToSearch,
            @Param("genderType") String genderType,
            @Param("currentDate") LocalDate currentDate,
            @Param("startAge") Integer startAge,
            @Param("endAge") Integer endAge);

    @Query(nativeQuery = true, name = "EntityUser.listUsersBasedOnGenderAndZodiacSignFilters")
    List<FilterUsersForNotificationResDTO> listUsersBasedOnGenderAndZodiacSignFilters(
            @Param("checkIsGenderType") Boolean checkIsGenderType,
            @Param("queryToSearch") String queryToSearch,
            @Param("genderType") String genderType,
            @Param("currentDate") LocalDate currentDate,
            @Param("checkIsZodiacSignType") Boolean checkIsZodiacSignType,
            @Param("zodiacSignType") String zodiacSignType);

    @Query(nativeQuery = true, name = "EntityUser.listUsersBasedOnZodiacSignAndAgeFilters")
    List<FilterUsersForNotificationResDTO> listUsersBasedOnZodiacSignAndAgeFilters(
            @Param("checkIsZodiacSignType") Boolean checkIsZodiacSignType,
            @Param("queryToSearch") String queryToSearch,
            @Param("zodiacSignType") String zodiacSignType,
            @Param("currentDate") LocalDate currentDate,
            @Param("startAge") Integer startAge,
            @Param("endAge") Integer endAge);


    @Query(nativeQuery = true, name = "EntityUser.listUsersBasedOnGenderAndAgeAndZodiacSignFilters")
    List<FilterUsersForNotificationResDTO> listUsersBasedOnGenderAndAgeAndZodiacSignFilters(
            @Param("checkIsGenderType") Boolean checkIsGenderType,
            @Param("checkIsZodiacSignType") Boolean checkIsZodiacSignType,
            @Param("queryToSearch") String queryToSearch,
            @Param("genderType") String genderType,
            @Param("zodiacSignType") String zodiacSignType,
            @Param("currentDate") LocalDate currentDate,
            @Param("startAge") Integer startAge,
            @Param("endAge") Integer endAge);

    @Query(nativeQuery = true, name = "EntityUser.listUsersBasedOnGenderFilter")
    List<FilterUsersForNotificationResDTO> listUsersBasedOnGenderFilter(
            @Param("queryToSearch") String queryToSearch,
            @Param("genderType") String genderType,
            @Param("currentDate") LocalDate currentDate);

    @Query(nativeQuery = true, name = "EntityUser.listUsersBasedOnAgeFilter")
    List<FilterUsersForNotificationResDTO> listUsersBasedOnAgeFilter(
            @Param("queryToSearch") String queryToSearch,
            @Param("currentDate") LocalDate currentDate,
            @Param("startAge") Integer startAge,
            @Param("endAge") Integer endAge);

    @Query(nativeQuery = true, name = "EntityUser.listUsersBasedOnZodiacSignFilter")
    List<FilterUsersForNotificationResDTO> listUsersBasedOnZodiacSignFilter(
            @Param("checkIsZodiacSignType") Boolean checkIsZodiacSignType,
            @Param("queryToSearch") String queryToSearch,
            @Param("zodiacSignType") String zodiacSignType,
            @Param("currentDate") LocalDate currentDate);

    @Query(nativeQuery = true, name = "EntityUser.listAllUsersForNotifications")
    List<FilterUsersForNotificationResDTO> listAllUsersForNotifications(
            @Param("queryToSearch") String queryToSearch, @Param("currentDate") LocalDate currentDate);


    EntityUser findByEmailAndIsDeleted(String email, boolean isDeleted);

    @Query("SELECT u FROM EntityUser u WHERE (u.email = :email OR u.facebookEmailId = :email OR u.appleEmailId = :email OR u.googleEmailId = :email) AND u.isDeleted = :isDeleted")
    EntityUser findByAllEmailAndIsDeleted(String email, boolean isDeleted);

    EntityUser findByEmailAndIsActiveAndIsDeleted(String email, boolean isActive, boolean isDeleted);

    @Query("SELECT COUNT(u) FROM EntityUser u WHERE u.createdAt >= :startDateTime AND u.userType = :userType")
    long countUsersByDateAndType(LocalDateTime startDateTime, UserType userType);

    List<EntityUser> findAllByEmailAndIsDeleted(String email, boolean isDeleted);

    @Query("SELECT u FROM EntityUser u WHERE (u.email = :email OR u.facebookEmailId = :email OR u.appleEmailId = :email OR u.googleEmailId = :email) AND u.isDeleted = :isDeleted")
    List<EntityUser> findAllByAllEmailAndIsDeleted(String email, boolean isDeleted);

    @Query("SELECT u FROM EntityUser u WHERE u.userId IN :userIds")
    List<EntityUser> findByUserIdIn(@Param("userIds") List<Long> userIds);

    @Query("SELECT u FROM EntityUser u WHERE u.userId IN :userIds AND u.isActive = :isActive AND u.isDeleted = :isDeleted")
    List<EntityUser> findByUserIdInAndIsActiveAndIsDeleted(@Param("userIds") Collection<Long> userIds,
                                                          @Param("isActive") boolean isActive,
                                                          @Param("isDeleted") boolean isDeleted);

    Optional<EntityUser> findByReferralCodeAndIsDeletedAndIsActive(String referralCode, boolean isDeleted, boolean isActive);


//    @Query("""
//            SELECT new com.mindbody.api.dto.cms.UserRequestManagementResDTO
//            (
//                u.userId,
//                u.email,
//                u.countryCode,
//                u.phoneNumber,
//                ui.name,
//                u.isDeleted,
//                u.isActive,
//                u.createdAt,
//                u.updatedAt
//            )
//            FROM EntityUser u
//            LEFT JOIN EntityUserInfo ui ON u.userId = ui.userId
//            WHERE u.isReactivationRequestPending = true
//            AND u.isDeleted = false
//            AND (:checkIsUserType = false OR u.userType = :userType)
//            AND (:userType='ANONYMOUS' OR lower(ui.name) LIKE lower(concat('%', :queryToSearch, '%')))
//            """)
//    Page<UserRequestManagementResDTO> listUsersRequestManagement(Pageable pageable,boolean checkIsUserType, String queryToSearch, UserType userType);

//    @Query("SELECT COUNT(u) FROM EntityUser u LEFT JOIN EntityUserInfo ui ON u.userId = ui.userId " +
//            "WHERE u.isReactivationRequestPending = true AND (:checkIsUserType = false or u.userType=:userType )" +
//            "AND (:userType='ANONYMOUS' OR lower(ui.name) LIKE lower(concat('%', :queryToSearch, '%')))" +
//            "AND u.isDeleted =false ")
//    long countUsersForRequest(String queryToSearch, boolean checkIsUserType, UserType userType);



}
