package com.mindbody.api.repository;

import com.mindbody.api.model.EntityUserAnswer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserAnswerRepository extends JpaRepository<EntityUserAnswer, Long> {

    List<EntityUserAnswer> findAllByQuestionId(Long questionId);
    List<EntityUserAnswer> findAllByEntityQuestionOption_OptionId(Long questionOptionId);
    List<EntityUserAnswer> findAllByUserId(Long userId);
    List<EntityUserAnswer> findAllByUserIdAndQuestionId(Long userId,Long questionId);
}
