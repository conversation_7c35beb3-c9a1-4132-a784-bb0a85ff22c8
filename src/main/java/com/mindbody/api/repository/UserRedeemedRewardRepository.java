package com.mindbody.api.repository;

import com.mindbody.api.model.EntityUserRedeemedReward;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRedeemedRewardRepository extends JpaRepository<EntityUserRedeemedReward, Long> {

    Optional<EntityUserRedeemedReward> findByUserIdAndRewardIdAndIsActiveAndIsDeleted(
            Long userId, Long rewardId, boolean isActive, boolean isDeleted);

    List<EntityUserRedeemedReward> findAllByUserIdAndIsActiveAndIsDeleted(
            Long userId, boolean isActive, boolean isDeleted, Pageable pageable);

    long countByUserIdAndIsActiveAndIsDeleted(Long userId, boolean isActive, boolean isDeleted);

    @Query("SELECT COUNT(rr) > 0 FROM EntityUserRedeemedReward rr " +
            "WHERE rr.userId = :userId " +
            "AND rr.rewardId = :rewardId " +
            "AND rr.redemptionStatus = 'REDEEMED' " +
            "AND rr.isActive = :isActive " +
            "AND rr.isDeleted = :isDeleted")
    boolean isRewardRedeemedByUser(Long userId, Long rewardId, boolean isActive, boolean isDeleted);

    @Query("SELECT COUNT(rr) FROM EntityUserRedeemedReward rr " +
            "WHERE rr.rewardId = :rewardId " +
            "AND rr.redemptionStatus = 'REDEEMED' " +
            "AND rr.isActive = true " +
            "AND rr.isDeleted = false")
    long countRedemptionsForReward(Long rewardId);

    @Query("SELECT COUNT(rr) > 0 FROM EntityUserRedeemedReward rr " +
            "WHERE rr.rewardMetadata = :couponCode " +
            "AND rr.isActive = true " +
            "AND rr.isDeleted = false")
    boolean existsByCouponCode(String couponCode);
} 