package com.mindbody.api.repository;

import com.mindbody.api.model.EntityUserHealthInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserHealthInfoRepository extends JpaRepository<EntityUserHealthInfo, Long> {

    EntityUserHealthInfo findByUserIdAndIsActiveAndIsDeleted(Long userId, boolean isActive, boolean isDeleted);
}
