package com.mindbody.api.repository;

import com.mindbody.api.dto.cms.AudioZodiacSignDTO;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.model.EntityAudioZodiacSign;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AudioZodiacSignRepository extends JpaRepository<EntityAudioZodiacSign, Long> {
    @Query("SELECT new com.mindbody.api.dto.cms.AudioZodiacSignDTO(azs.entityAudio.audioId, azs.zodiacSign) " +
            "FROM EntityAudioZodiacSign azs " +
            "WHERE azs.entityAudio.audioId IN :audioIds")
    List<AudioZodiacSignDTO> findZodiacSignsByAudioIds(List<Long> audioIds);

    List<EntityAudioZodiacSign> findByEntityAudio_AudioIdAndZodiacSignIn(Long audioId, List<ZodiacSignType> zodiacSigns);

    EntityAudioZodiacSign findByEntityAudio_AudioIdAndZodiacSign(Long audioId,ZodiacSignType zodiacSigns);
}
