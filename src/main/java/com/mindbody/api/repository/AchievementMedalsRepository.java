package com.mindbody.api.repository;

import com.mindbody.api.dto.cms.AchievementMedalListResDTO;
import com.mindbody.api.model.EntityAchievementMedals;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AchievementMedalsRepository extends JpaRepository<EntityAchievementMedals, Long> {
    @Query("SELECT new com.mindbody.api.dto.cms.AchievementMedalListResDTO(am.achievementMedalId, am.medalName) " +
            "FROM EntityAchievementMedals am " +
            "WHERE am.isActive = :isActive " +
            "AND am.isDeleted = :isDeleted")
    List<AchievementMedalListResDTO> findAllActiveMedals(boolean isActive, boolean isDeleted);
}
