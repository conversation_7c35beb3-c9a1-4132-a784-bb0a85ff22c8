package com.mindbody.api.repository;

import com.mindbody.api.dto.cms.AchievementTitleListResDTO;
import com.mindbody.api.model.EntityAchievementTitle;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AchievementTitleRepository extends JpaRepository<EntityAchievementTitle,Long> {

    EntityAchievementTitle findByTitleAndIsActiveAndIsDeleted(String title,boolean isActive, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.cms.AchievementTitleListResDTO(at.achievementTitleId, at.title) " +
            "FROM EntityAchievementTitle at " +
            "WHERE at.isActive = :isActive " +
            "AND at.isDeleted = :isDeleted")
    List<AchievementTitleListResDTO> findAllActiveTitles(boolean isActive, boolean isDeleted);

}
