package com.mindbody.api.repository;

import com.mindbody.api.model.EntityFcmToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FcmTokenRepository extends JpaRepository<EntityFcmToken, Long> {

    EntityFcmToken findByDeviceUniqueIdAndUserId(String deviceUniqueId, Long id);

    void deleteByDeviceUniqueIdAndUserId(String deviceUniqueId, Long userId);

    List<EntityFcmToken> findByUserIdIn(List<Long> userIds);

    List<EntityFcmToken> findByUserIdAndEntityUser_IsActive(Long userId, boolean active);
}
