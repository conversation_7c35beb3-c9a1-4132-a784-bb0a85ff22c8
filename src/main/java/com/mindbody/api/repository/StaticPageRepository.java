package com.mindbody.api.repository;

import com.mindbody.api.enums.StaticPageType;
import com.mindbody.api.model.EntityStaticPage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface StaticPageRepository extends JpaRepository<EntityStaticPage,Long> {

    EntityStaticPage findByStaticPageTypeAndIsDeleted(StaticPageType staticPageType,boolean isDeleted);

    EntityStaticPage findByStaticPageIdAndIsDeleted(Long staticPageId,boolean isDeleted);

}
