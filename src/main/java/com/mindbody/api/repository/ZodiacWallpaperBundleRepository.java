package com.mindbody.api.repository;

import com.mindbody.api.model.EntityZodiacWallpaperBundle;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ZodiacWallpaperBundleRepository extends JpaRepository<EntityZodiacWallpaperBundle, Long> {
    List<EntityZodiacWallpaperBundle> findByRewardIdAndIsActiveAndIsDeleted(Long rewardId, boolean isActive, boolean isDeleted);
} 