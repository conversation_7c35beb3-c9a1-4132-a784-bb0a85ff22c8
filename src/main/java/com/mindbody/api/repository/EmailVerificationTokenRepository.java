package com.mindbody.api.repository;

import com.mindbody.api.model.EmailVerificationToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EmailVerificationTokenRepository extends JpaRepository<EmailVerificationToken, Long> {

    Optional<EmailVerificationToken> findByAdminId(Long adminId);

    Optional<EmailVerificationToken> findByVerificationToken(String verificationToken);

    Optional<EmailVerificationToken> findByUserId(Long userId);

    Optional<EmailVerificationToken> findByVerificationTokenAndIsActiveAndIsDeleted(String verificationToken, boolean isActive, boolean isDeleted);

    Optional<EmailVerificationToken> findByUserIdAndIsActiveAndIsDeleted(Long userId, boolean isActive, boolean isDeleted);
}
