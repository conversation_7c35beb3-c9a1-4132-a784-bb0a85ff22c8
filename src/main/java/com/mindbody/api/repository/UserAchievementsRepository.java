package com.mindbody.api.repository;

import com.mindbody.api.dto.*;
import com.mindbody.api.enums.AchievementActivityType;
import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.model.EntityUserAchievements;
import com.mindbody.api.model.EntityUserPointsTracker;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface UserAchievementsRepository extends JpaRepository<EntityUserAchievements, Long> {

    Integer countByEntityAudioPlaylist_AudioPlaylistIdAndUserId(Long audioPlaylistId, Long userId);

    Integer countByEntityWorkoutPlaylist_WorkoutPlaylistIdAndUserId(Long workoutPlaylistId, Long userId);

    Integer countByEntityWarriorSet_WarriorSetIdAndUserId(Long warriorSetId, Long userId);

    Optional<EntityUserAchievements> findByUserAchievementsIdAndUserIdAndIsDeleted(Long userAchievementsId, Long userId, boolean isDeleted);

    Integer countUserAchievementsByAchievementCategoryTypeAndUserId(AchievementCategoryType achievementCategoryType, Long userId);

    Boolean existsByEntityAudioPlaylist_AudioPlaylistIdAndUserIdAndIsClaimedAndIsDeleted(Long audioPlaylistId, Long userId, boolean isClaimed, boolean isDeleted);

    Boolean existsByEntityWorkoutPlaylist_WorkoutPlaylistIdAndUserIdAndIsClaimedAndIsDeleted(Long workoutPlaylistId, Long userId, boolean isClaimed, boolean isDeleted);

    Boolean existsByEntityWarriorSet_WarriorSetIdAndUserIdAndIsClaimedAndIsDeleted(Long warriorSetId, Long userId, boolean isClaimed, boolean isDeleted);

    @Query("""
            SELECT ua.achievementActivityType
            FROM EntityUserAchievements ua
            WHERE ua.userId = :userId
            AND ua.isClaimed = :isClaimed AND ua.isDeleted = :isDeleted
            """)
    List<AchievementActivityType> findAchievementActivityTypeByUserIdAndIsClaimedAndIsDeleted(Long userId, boolean isClaimed , boolean isDeleted);

    @Query("""
            SELECT new com.mindbody.api.dto.CategoryAndCountDTO(COUNT(*),
            achievementCategoryType)
            FROM EntityUserAchievements ua
            WHERE ua.userId=:userId
            AND ua.isDeleted=:isDeleted AND ua.isClaimed = true
            GROUP BY ua.achievementCategoryType
            """)
    List<CategoryAndCountDTO> countAchievementsByCategoriesAndUserIdAndIsDeleted(Long userId, boolean isDeleted);

    @Query("""
            SELECT new com.mindbody.api.dto.UserTitlesDTO(
            euat.title,
            eua.entityAchievements.achievementsId,
            eua.achievementCategoryType
            )
            FROM EntityUserAchievements eua
            INNER JOIN eua.achievementTitleId euat
            WHERE eua.userId = :userId AND eua.isClaimed = true
            """)
    List<UserTitlesDTO> findUserAchievementTitleByUserId(Long userId);

    @Query("""
            SELECT new com.mindbody.api.dto.UserAchievementDTO(
            ua.userAchievementsId,
            ua.achievementActivityType,
            ua.achievementCategoryType,
            ua.acquiredDate,
            ua.activityDetails,
            ua.isClaimed,
            ua.pointsEarned,
            ua.userId,
            ua.wxpEarned,
            t.title,
            m.medalName,
            CASE
            WHEN ua.mindSubCategoryType != 'DEFAULT' THEN CONCAT(ua.mindSubCategoryType, '')
            WHEN ua.bodySubCategoryType != 'DEFAULT' THEN CONCAT(ua.bodySubCategoryType, '')
            WHEN ua.warriorSubCategoryType != 'DEFAULT' THEN CONCAT(ua.warriorSubCategoryType, '')
            ELSE CONCAT('DEFAULT', '')
            END,
            CASE
            WHEN ap.audioPlaylistName IS NOT NULL THEN CONCAT(ap.audioPlaylistName, '')
            WHEN wp.workoutPlaylistName IS NOT NULL THEN CONCAT(wp.workoutPlaylistName, '')
            WHEN ws.warriorSetName IS NOT NULL THEN CONCAT(ws.warriorSetName, '')
            END
            )
            FROM EntityUserAchievements ua
            LEFT JOIN ua.achievementTitleId t
            LEFT JOIN ua.achievementBorderId b
            LEFT JOIN ua.achievementMedalId m
            LEFT JOIN ua.entityAudioPlaylist ap
            LEFT JOIN ua.entityWorkoutPlaylist wp
            LEFT JOIN ua.entityWarriorSet ws
            WHERE ua.userId = :userId AND ua.achievementActivityType = :achievementActivityType AND ua.isDeleted = false
            """)
    Optional<UserAchievementDTO> findByUserIdAndAchievementActivityType(Long userId, AchievementActivityType achievementActivityType);

    @Query("""
            SELECT new com.mindbody.api.dto.UserBadgeDTO(
            euaa.achievementsId,
            CONCAT(:badgePath,euaa.badgeImage),
            eua.achievementCategoryType
            )
            FROM EntityUserAchievements eua
            INNER JOIN eua.entityAchievements euaa
            WHERE eua.userId = :userId AND euaa.badgeImage IS NOT NULL AND eua.isClaimed = true
            """)
    List<UserBadgeDTO> findUserAchievementBadgeByUserId(String badgePath, Long userId);

    @Query("""
            SELECT new com.mindbody.api.dto.CategoryAndCountDTO(
            COUNT(*),
            ua.achievementCategoryType
            )
            FROM EntityUserAchievements ua
            INNER JOIN ua.entityAchievements eua
            WHERE ua.userId=:userId
            AND ua.isDeleted=:isDeleted AND ua.isClaimed = true
            AND eua.badgeImage IS NOT NULL
            GROUP BY ua.achievementCategoryType
            """)
    List<CategoryAndCountDTO> countAchievementsByCategoriesAndUserIdAndIsDeletedForBadges(Long userId, boolean isDeleted);

    @Query("""
            SELECT new com.mindbody.api.dto.CategoryAndCountDTO(
            COUNT(*),
            ua.achievementCategoryType
            )
            FROM EntityUserAchievements ua
            INNER JOIN ua.entityAchievements eua
            WHERE ua.userId=:userId
            AND ua.isDeleted=:isDeleted AND ua.isClaimed = true
            AND eua.borderImage IS NOT NULL
            GROUP BY ua.achievementCategoryType
            """)
    List<CategoryAndCountDTO> countAchievementsByCategoriesAndUserIdAndIsDeletedForBorders(Long userId, boolean isDeleted);

    @Query("""
            SELECT new com.mindbody.api.dto.UserBorderDTO(
            euaa.achievementsId,
            CONCAT(:borderPath,euaa.borderImage),
            eua.achievementCategoryType,
            euaab.border
            )
            FROM EntityUserAchievements eua
            INNER JOIN eua.entityAchievements euaa
            INNER JOIN euaa.entityAchievementBorder euaab
            WHERE eua.userId = :userId AND euaa.borderImage IS NOT NULL AND eua.isClaimed = true
            """)
    List<UserBorderDTO> findUserAchievementBorderByUserId(String borderPath, Long userId);

    boolean existsByAchievementActivityTypeAndUserIdAndIsDeleted(AchievementActivityType achievementActivityType, Long userId, boolean isDeleted);

    @Query("""
            SELECT new com.mindbody.api.dto.UserAllAchievementDTO(
            ua.userAchievementsId,
            CASE WHEN ap.audioPlaylistName IS NOT NULL THEN CONCAT(ap.audioPlaylistName, '')
            WHEN wp.workoutPlaylistName IS NOT NULL THEN CONCAT(wp.workoutPlaylistName, '')
            WHEN ws.warriorSetName IS NOT NULL THEN CONCAT(ws.warriorSetName, '')
            END,
            uaa.achievementCategoryType,
            CASE WHEN uaa.mindSubCategoryType != 'DEFAULT' THEN CONCAT(uaa.mindSubCategoryType, '')
            WHEN uaa.bodySubCategoryType != 'DEFAULT' THEN CONCAT(uaa.bodySubCategoryType, '')
            WHEN uaa.warriorSubCategoryType != 'DEFAULT' THEN CONCAT(uaa.warriorSubCategoryType, '')
            ELSE CONCAT('DEFAULT', '')
            END,
            uaa.entityAchievementMedals.medalName,
            uaa.entityAchievementTitle.title,
            uaa.activityDetails,
            uaa.entityAchievementBorder.border,
            ua.pointsEarned,
            ua.wxpEarned,
            ua.isClaimed,
            ua.userId,
            uaa.achievementsId
            )
            FROM EntityUserAchievements ua
            LEFT JOIN ua.entityAchievements uaa
            LEFT JOIN uaa.entityAchievementMedals
            LEFT JOIN uaa.entityAchievementTitle
            LEFT JOIN uaa.entityAchievementBorder
            LEFT JOIN uaa.entityAudioPlaylist ap
            LEFT JOIN uaa.entityWorkoutPlaylist wp
            LEFT JOIN uaa.entityWarriorSet ws
            WHERE ua.userId = :userId AND ua.isClaimed = true AND ua.isDeleted = false
    """)
    List<UserAllAchievementDTO> findAllUserAchievementsByUserId(Long userId, Pageable pageable);

    @Query("""
            SELECT COUNT(ua) FROM EntityUserAchievements ua where ua.userId = :userId AND ua.isDeleted = false AND ua.isClaimed = true
            """)
    Long countUserAchievementsByUserId(Long userId);

    boolean existsByAchievementCategoryTypeAndUserIdAndIsClaimedAndIsDeleted(AchievementCategoryType achievementCategoryType, Long userId, boolean isClaimed, boolean isDeleted);

    List<EntityUserAchievements> findAllByUserIdIn(Set<Long> userId);

    /**
     * Fetches all achievements for a specific user by their userId.
     *
     * @param userId The ID of the user.
     * @return A list of achievements for the user.
     */
    List<EntityUserAchievements> findAllByUserIdAndIsDeleted(Long userId, boolean isDeleted);
}
