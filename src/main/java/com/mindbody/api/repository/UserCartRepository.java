package com.mindbody.api.repository;

import com.mindbody.api.model.EntityUserCart;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserCartRepository extends JpaRepository<EntityUserCart, Long> {

    EntityUserCart findByUserIdAndCartIdAndIsActiveAndIsDeleted(Long userId, String cartId, boolean isActive, boolean isDeleted);

    EntityUserCart findByUserIdAndIsActiveAndIsDeleted(@NotNull(message = "user_id_required") Long userId, boolean isActive, boolean isDeleted);
}
