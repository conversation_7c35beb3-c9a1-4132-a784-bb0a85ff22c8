package com.mindbody.api.repository;

import com.mindbody.api.model.EntityQuestionOption;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface QuestionOptionRepository extends JpaRepository<EntityQuestionOption,Long> {

    EntityQuestionOption findByOptionId(Long optionId);

    Optional<EntityQuestionOption> findByOptionIdAndIsActiveAndIsDeleted(Long optionId, boolean isActive, boolean isDeleted);

    List<EntityQuestionOption> findAllByQuestionIdAndIsActiveAndIsDeleted(Long questionId,boolean isActive, boolean isDeleted);
}
