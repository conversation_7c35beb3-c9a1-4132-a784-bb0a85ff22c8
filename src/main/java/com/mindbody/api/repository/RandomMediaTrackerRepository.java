package com.mindbody.api.repository;

import com.mindbody.api.enums.MediaType;
import com.mindbody.api.model.EntityRandomMediaTracker;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface RandomMediaTrackerRepository extends JpaRepository<EntityRandomMediaTracker, Long> {

    Optional<EntityRandomMediaTracker> findByUserIdAndMediaTypeAndIsActiveAndIsDeleted(Long userId, MediaType mediaType, boolean isActive, boolean isDeleted);

}
