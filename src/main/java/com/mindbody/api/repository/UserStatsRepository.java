package com.mindbody.api.repository;

import com.mindbody.api.dto.AllTimeUserStatsReqDTO;
import com.mindbody.api.model.EntityUserStats;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface UserStatsRepository extends JpaRepository<EntityUserStats,Long> {

    @Query("""
            SELECT COUNT(us) FROM EntityUserStats us WHERE us.userId = :userId AND us.date BETWEEN :startOfDayUtc AND :endOfDayUtc
            """)
    long checkIfRecordsExistForToday(Long userId, LocalDateTime startOfDayUtc, LocalDateTime endOfDayUtc);

    EntityUserStats findByUserIdAndIsDeletedAndIsActive(Long userId, boolean isDeleted, boolean isActive);

    /**
     * Finds all user stats between the specified start and end dates (inclusive).
     *
     * @param startDate The start date of the range.
     * @param endDate   The end date of the range.
     * @return A list of user stats within the specified date range.
     */
    @Query("SELECT e FROM EntityUserStats e WHERE e.date BETWEEN :startDate AND :endDate")
    List<EntityUserStats> findByDateBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    @Query("SELECT e FROM EntityUserStats e WHERE e.date >= :startOfDay AND e.date < :endOfDay")
    List<EntityUserStats> findByDate(@Param("startOfDay") LocalDateTime startOfDay, @Param("endOfDay") LocalDateTime endOfDay);

    EntityUserStats findByUserId(Long userId);

    /**
     * Fetches all-time stats for a specific user by their userId.
     *
     * @param userId The ID of the user.
     * @return The aggregated stats for the user.
     */
    @Query("SELECT new com.mindbody.api.dto.AllTimeUserStatsReqDTO( " +
            "SUM(e.mindCategoryTimeSpent), " +
            "SUM(e.bodyCategoryTimeSpent), " +
            "SUM(e.wxpEarned), " +
            "e.userId) " +
            "FROM EntityUserStats e " +
            "WHERE e.userId = :userId " +
            "GROUP BY e.userId")
    AllTimeUserStatsReqDTO findAllTimeStatsByUserId(Long userId);

    boolean existsByUserIdAndIsDeleted(Long userId, boolean isDeleted);

    List<EntityUserStats> findAllByUserIdAndIsActiveAndIsDeleted(long userId, boolean isActive, boolean isDeleted);

    @Query("SELECT e FROM EntityUserStats e WHERE e.userId = :userId AND e.date BETWEEN :startDate AND :endDate")
    EntityUserStats findByDateBetweenByUserId(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, Long userId);

    /**
     * Count users with higher mind category time spent than the given user.
     * This is used to determine the user's rank on the mind leaderboard.
     *
     * @param userId The ID of the user.
     * @return The number of users with higher mind category time spent.
     */
    @Query("""
            SELECT COUNT(DISTINCT e.userId) FROM EntityUserStats e
            WHERE e.userId != :userId AND e.isDeleted = false
            GROUP BY e.userId
            HAVING SUM(e.mindCategoryTimeSpent) > (
                SELECT SUM(us.mindCategoryTimeSpent) FROM EntityUserStats us
                WHERE us.userId = :userId AND us.isDeleted = false
            )
            """)
    Long countUsersWithHigherMindTimeSpent(@Param("userId") Long userId);

    /**
     * Count users with higher body category time spent than the given user.
     * This is used to determine the user's rank on the body leaderboard.
     *
     * @param userId The ID of the user.
     * @return The number of users with higher body category time spent.
     */
    @Query("""
            SELECT COUNT(DISTINCT e.userId) FROM EntityUserStats e
            WHERE e.userId != :userId AND e.isDeleted = false
            GROUP BY e.userId
            HAVING SUM(e.bodyCategoryTimeSpent) > (
                SELECT SUM(us.bodyCategoryTimeSpent) FROM EntityUserStats us
                WHERE us.userId = :userId AND us.isDeleted = false
            )
            """)
    Long countUsersWithHigherBodyTimeSpent(@Param("userId") Long userId);

    /**
     * Count users with higher WXP earned than the given user.
     * This is used to determine the user's rank on the warrior leaderboard.
     *
     * @param userId The ID of the user.
     * @return The number of users with higher WXP earned.
     */
    @Query("""
            SELECT COUNT(DISTINCT e.userId) FROM EntityUserStats e
            WHERE e.userId != :userId AND e.isDeleted = false
            GROUP BY e.userId
            HAVING SUM(e.wxpEarned) > (
                SELECT SUM(us.wxpEarned) FROM EntityUserStats us
                WHERE us.userId = :userId AND us.isDeleted = false
            )
            """)
    Long countUsersWithHigherWxpEarned(@Param("userId") Long userId);
}
