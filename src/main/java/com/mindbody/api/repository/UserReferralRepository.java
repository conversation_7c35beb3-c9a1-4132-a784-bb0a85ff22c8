package com.mindbody.api.repository;

import com.mindbody.api.dto.ReferralCodeDTOList;
import com.mindbody.api.model.EntityUserReferral;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserReferralRepository extends JpaRepository<EntityUserReferral, Long> {

    @Query("""
            SELECT new com.mindbody.api.dto.ReferralCodeDTOList(
            ur.code,
            ui.name,
            CAST(ui.genderType AS string),
            ui.zodiacSign,
            CASE WHEN ui.titleAchievementId IS NOT NULL THEN ui.titleAchievementId.entityAchievementTitle.title ELSE NULL END,
            CASE WHEN ui.borderAchievementId IS NOT NULL THEN CONCAT(:borderImagePath, ui.borderAchievementId.borderImage) ELSE NULL END,
            CASE WHEN ui.medalAchievementId IS NOT NULL THEN CONCAT(:badgeImagePath, ui.medalAchievementId.badgeImage) ELSE NULL END,
            ui.facebookAccountName,
            ui.googleAccountName,
            ui.appleAccountName,
            1L
            ) FROM EntityUserReferral ur
            LEFT JOIN ur.toEntityUser tu
            LEFT JOIN EntityUserInfo ui ON tu.userId = ui.userId
            WHERE ur.fromUserId = :userId AND ur.isDeleted = false AND ur.isActive = true
            ORDER BY ur.createdAt DESC
            """)

    List<ReferralCodeDTOList> listReferrals(Long userId, String borderImagePath, String badgeImagePath, Pageable pageable);

    @Query("""
            SELECT COUNT(e) FROM EntityUserReferral e
            WHERE e.isDeleted = :isDeleted AND e.fromUserId = :userId
            """)
    long countReferrals(long userId, boolean isDeleted);

    boolean existsByToUserIdAndIsDeleted(Long toUserId, boolean isDeleted);

    @Query("""
            SELECT new com.mindbody.api.dto.ReferralCodeDTOList(
            r.code,
            COALESCE(ui.name, u.email, u.referralCode, 'Unknown'),
            COALESCE(CAST(ui.genderType AS string), CAST(u.userType AS string), 'UNKNOWN'),
            ui.zodiacSign,
            CASE 
                WHEN ui.titleAchievementId IS NOT NULL 
                THEN ui.titleAchievementId.entityAchievementTitle.title
                ELSE NULL 
            END,
            CASE 
                WHEN ui.borderAchievementId IS NOT NULL 
                THEN CONCAT(:borderImagePath, ui.borderAchievementId.borderImage)
                ELSE NULL 
            END,
            CASE 
                WHEN ui.medalAchievementId IS NOT NULL 
                THEN CONCAT(:badgeImagePath, ui.medalAchievementId.badgeImage)
                ELSE NULL 
            END,
            COALESCE(ui.facebookAccountName, ''),
            COALESCE(ui.googleAccountName, ''),
            COALESCE(ui.appleAccountName, ''),
            COUNT(DISTINCT r.userReferralId)
            )
            FROM EntityUserReferral r
            LEFT JOIN r.fromEntityUser u
            LEFT JOIN EntityUserInfo ui ON u.userId = ui.userId
            LEFT JOIN ui.titleAchievementId.entityAchievementTitle title
            LEFT JOIN ui.borderAchievementId border
            LEFT JOIN ui.medalAchievementId medal
            WHERE r.isDeleted = false AND r.isActive = true
            GROUP BY r.code, ui.name, u.email, u.referralCode, ui.genderType, u.userType, ui.zodiacSign,
                     ui.titleAchievementId, ui.borderAchievementId, ui.medalAchievementId,
                     ui.facebookAccountName, ui.googleAccountName, ui.appleAccountName
            """)
    List<ReferralCodeDTOList> listReferralCodesForCMS(String borderImagePath, String badgeImagePath, Pageable pageable);

    @Query("""
            SELECT COUNT(DISTINCT r.code) 
            FROM EntityUserReferral r
            LEFT JOIN r.fromEntityUser u
            LEFT JOIN EntityUserInfo ui ON u.userId = ui.userId
            WHERE r.isDeleted = false AND r.isActive = true
            """)
    long countAllReferrals();

    @Query(value = """
            SELECT new com.mindbody.api.dto.ReferralCodeDTOList(
            r.code,
            COALESCE(u.email, u.referralCode, 'Unknown'),
            COALESCE(CAST(u.userType AS string), 'NORMAL'),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            COUNT(r.userReferralId)
            )
            FROM EntityUserReferral r
            JOIN EntityUser u ON r.fromUserId = u.userId
            WHERE r.isDeleted = false
            GROUP BY r.code, u.email, u.referralCode, u.userType
            """)
    List<ReferralCodeDTOList> listSimpleReferralCodesForCMS(Pageable pageable);

    /**
     * Check if any user in the given list has entered a referral code
     *
     * @param userIds List of user IDs to check
     * @param isDeleted Whether to include deleted referrals
     * @return true if any user in the list has entered a referral code, false otherwise
     */
    @Query("SELECT CASE WHEN COUNT(r) > 0 THEN true ELSE false END FROM EntityUserReferral r " +
           "WHERE r.toUserId IN :userIds AND r.isDeleted = :isDeleted")
    boolean existsByToUserIdInAndIsDeleted(List<Long> userIds, boolean isDeleted);

    /**
     * Count referrals by code
     *
     * @param code The referral code to count
     * @return The number of times this code has been used
     */
    @Query("SELECT COUNT(r) FROM EntityUserReferral r WHERE r.code = :code AND r.isDeleted = false")
    long countByCode(String code);

    /**
     * Find user by referral code
     *
     * @param code The referral code
     * @return The user entity
     */
    @Query("SELECT u FROM EntityUser u WHERE u.referralCode = :code")
    com.mindbody.api.model.EntityUser findUserByReferralCode(String code);

    @Query("SELECT DISTINCT u.referralCode FROM EntityUser u WHERE u.referralCode IS NOT NULL")
    List<String> findAllReferralCodes();
}
