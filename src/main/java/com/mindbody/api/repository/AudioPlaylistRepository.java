package com.mindbody.api.repository;

import com.mindbody.api.dto.UserAudioPlaylistResDTO;
import com.mindbody.api.dto.cms.AudioPlaylistDetailResDTO;
import com.mindbody.api.dto.cms.CMSAudioPlaylistResDTO;
import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.model.EntityAudioPlaylist;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AudioPlaylistRepository extends JpaRepository<EntityAudioPlaylist, Long> {

    Optional<EntityAudioPlaylist> findByAudioPlaylistNameAndAudioPlaylistTypeAndIsActiveAndIsDeleted(String audioPlaylistName, AudioPlaylistType audioPlaylistType, boolean isActive, boolean isDeleted);
    Optional<EntityAudioPlaylist> findByAudioPlaylistNameAndAudioPlaylistTypeAndIsActiveAndIsDeletedAndAudioPlaylistIdNot(String audioPlaylistName, AudioPlaylistType audioPlaylistType, boolean isActive, boolean isDeleted,Long audioPlaylistId);

    EntityAudioPlaylist findByAudioPlaylistIdAndIsDeleted(Long audioPlaylistId, boolean isDeleted);

    @Query("SELECT COUNT(a) FROM EntityAudioPlaylist a where lower(a.audioPlaylistName) LIKE lower(concat('%',:queryToSearch,'%'))" +
            "AND (:isAudioPlaylistTypeFilter = false OR a.audioPlaylistType = :audioPlaylistType)" +
            "AND a.isDeleted = :isDeleted")
    long countAudioPlaylists(String queryToSearch, AudioPlaylistType audioPlaylistType, boolean isAudioPlaylistTypeFilter, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.cms.AudioPlaylistDetailResDTO(a.audioPlaylistId, a.audioPlaylistType, a.audioPlaylistName, a.isDeleted, a.isActive, a.createdAt, a.updatedAt, " +
            "CONCAT(:imagePrefix, a.audioPlaylistImage)) " +
            "FROM EntityAudioPlaylist a " +
            "WHERE lower(a.audioPlaylistName) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND (:isAudioPlaylistTypeFilter = false OR a.audioPlaylistType = :audioPlaylistType) " +
            "AND a.isDeleted = :isDeleted")
    List<AudioPlaylistDetailResDTO> findAllAudioPlaylists(String queryToSearch, String imagePrefix, Pageable pageable, AudioPlaylistType audioPlaylistType, boolean isAudioPlaylistTypeFilter, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.cms.CMSAudioPlaylistResDTO(a.audioPlaylistId,a.audioPlaylistName) " +
            "FROM EntityAudioPlaylist a " +
            "WHERE a.audioPlaylistType = :audioPlaylistType " +
            "AND a.isActive = :isActive " +
            "AND a.isDeleted = :isDeleted")
    List<CMSAudioPlaylistResDTO> findAllAudioPlaylistsForSelectedAudioPlaylistTypeForCMS(AudioPlaylistType audioPlaylistType, boolean isActive, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.UserAudioPlaylistResDTO(a.audioPlaylistId,a.audioPlaylistName, " +
            "CONCAT(:imagePrefix, a.audioPlaylistImage)) " +
            "FROM EntityAudioPlaylist a " +
            "WHERE a.audioPlaylistType = :audioPlaylistType " +
            "AND a.isActive = :isActive " +
            "AND a.isDeleted = :isDeleted")
    List<UserAudioPlaylistResDTO> findAllAudioPlaylistsForSelectedAudioPlaylistTypeForUser(String imagePrefix,AudioPlaylistType audioPlaylistType, boolean isActive, boolean isDeleted);

    EntityAudioPlaylist findByAudioPlaylistIdAndIsActiveAndIsDeleted(Long audioPlaylistId, boolean isActive, boolean isDeleted);
}
