package com.mindbody.api.repository;

import com.mindbody.api.dto.cms.WorkoutPlanlistDetailResDTO;
import com.mindbody.api.enums.DifficultyLevel;
import com.mindbody.api.enums.WorkoutPlaylistType;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.model.EntityWorkoutPlan;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface WorkoutPlanRepository extends JpaRepository<EntityWorkoutPlan, Long> {
    Optional<EntityWorkoutPlan> findByWorkoutPlanTitleAndIsDeleted(String workoutPlanTitle, boolean isDeleted);

    EntityWorkoutPlan findByWorkoutPlanIdAndIsDeleted(Long workoutPlanId, boolean isDeleted);

    @Query("SELECT  COUNT(DISTINCT w.workoutPlanId) FROM EntityWorkoutPlan w " +
            "LEFT JOIN EntityFavoriteWorkoutPlan fvp ON fvp.workoutPlanId = w.workoutPlanId AND (:userId IS NULL OR fvp.userId =:userId) " +
            "LEFT JOIN EntityWorkoutPlanZodiacSign wzs ON wzs.entityWorkoutPlan.workoutPlanId = w.workoutPlanId " +
            "WHERE lower(w.workoutPlanTitle) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND (:workoutPlaylistType IS NULL OR w.entityWorkoutPlaylist.workoutPlaylistType =:workoutPlaylistType)" +
            "AND (:workoutPlaylistId IS NULL OR w.workoutPlaylistId = :workoutPlaylistId) " +
            "AND (:difficultyLevel IS NULL OR w.difficultyLevel = :difficultyLevel) " +
            "AND (:zodiacSign IS NULL OR wzs.zodiacSign = :zodiacSign) " +
            "AND (:isActive IS NULL OR w.isActive = :isActive) " +
            "AND w.isDeleted =:isDeleted")
    long countWorkoutPlans(String queryToSearch, Long userId, WorkoutPlaylistType workoutPlaylistType, Long workoutPlaylistId, DifficultyLevel difficultyLevel, ZodiacSignType zodiacSign, Boolean isActive, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.cms.WorkoutPlanlistDetailResDTO(w.workoutPlanId, w.workoutPlanTitle, w.workoutPlanDescription, w.workoutPlanType, w.workoutPlaylistType, w.workoutPlaylistId, w.entityWorkoutPlaylist.workoutPlaylistName, w.equipment, w.workoutPlanDuration, w.difficultyLevel, CONCAT(:workoutVideoPath, w.workoutVideo), CONCAT(:workoutVideoThumbnailImage, w.workoutVideoThumbnailImage),COALESCE(fvp.isFavorite, false), w.isDeleted, w.isActive, w.createdAt, w.updatedAt) " +
            "FROM EntityWorkoutPlan w " +
            "LEFT JOIN EntityFavoriteWorkoutPlan fvp ON fvp.workoutPlanId = w.workoutPlanId AND (:userId IS NULL OR fvp.userId =:userId) " +
            "LEFT JOIN EntityWorkoutPlanZodiacSign wzs ON wzs.entityWorkoutPlan.workoutPlanId = w.workoutPlanId " +
            "WHERE lower(w.workoutPlanTitle) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND (:workoutPlaylistType IS NULL OR w.entityWorkoutPlaylist.workoutPlaylistType = :workoutPlaylistType) " +
            "AND (:workoutPlaylistId IS NULL OR w.workoutPlaylistId = :workoutPlaylistId) " +
            "AND (:difficultyLevel IS NULL OR w.difficultyLevel = :difficultyLevel) " +
            "AND (:zodiacSign IS NULL OR wzs.zodiacSign = :zodiacSign) " +
            "AND (:isActive IS NULL OR w.isActive = :isActive) " +
            "AND w.isDeleted = :isDeleted GROUP BY w.workoutPlanId")
    List<WorkoutPlanlistDetailResDTO> findAllWorkoutPlans(String queryToSearch, Pageable pageable, WorkoutPlaylistType workoutPlaylistType, Long workoutPlaylistId, Long userId, DifficultyLevel difficultyLevel, ZodiacSignType zodiacSign, String workoutVideoPath, String workoutVideoThumbnailImage, Boolean isActive, boolean isDeleted);

    List<EntityWorkoutPlan> findAllByWorkoutPlaylistIdAndIsDeleted(Long workoutPlaylistId, boolean isDeleted);

    EntityWorkoutPlan findByWorkoutPlanIdAndIsActiveAndIsDeleted(Long workoutPlanId, boolean isActive, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.cms.WorkoutPlanlistDetailResDTO(w.workoutPlanId, w.workoutPlanTitle, w.workoutPlanDescription, w.workoutPlanType, w.workoutPlaylistType, w.workoutPlaylistId, w.entityWorkoutPlaylist.workoutPlaylistName, w.equipment, w.workoutPlanDuration, w.difficultyLevel, CONCAT(:workoutVideoPath, w.workoutVideo), CONCAT(:workoutVideoThumbnailImagePath, w.workoutVideoThumbnailImage),COALESCE(fvp.isFavorite, false), w.isDeleted, w.isActive, w.createdAt, w.updatedAt) " +
            "FROM EntityWorkoutPlan w " +
            "LEFT JOIN EntityFavoriteWorkoutPlan fvp ON fvp.workoutPlanId = w.workoutPlanId AND (:userId IS NULL OR fvp.userId =:userId) " +
            "WHERE w.isActive = :isActive " +
            "AND w.isDeleted = :isDeleted GROUP BY w.workoutPlanId")
    List<WorkoutPlanlistDetailResDTO> findAllByIsActiveAndIsDeleted(Long userId, String workoutVideoPath, String workoutVideoThumbnailImagePath, boolean isActive, boolean isDeleted);

    @Query("SELECT COUNT(DISTINCT fw.favoriteWorkoutPlanId) FROM EntityFavoriteWorkoutPlan fw " +
            "INNER JOIN EntityWorkoutPlan w ON w.workoutPlanId = fw.workoutPlanId " +
            "where fw.userId =:userId " +
            "AND fw.isFavorite =:isFavorite " +
            "AND w.isActive =:isActive " +
            "AND w.isDeleted =:isDeleted")
    long countFavoriteWorkoutPlan(Long userId, boolean isFavorite, boolean isActive, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.cms.WorkoutPlanlistDetailResDTO(w.workoutPlanId, w.workoutPlanTitle," +
            " w.workoutPlanDescription, w.workoutPlanType, w.workoutPlaylistType," +
            " w.workoutPlaylistId, w.entityWorkoutPlaylist.workoutPlaylistName, " +
            "w.equipment, w.workoutPlanDuration, w.difficultyLevel, " +
            "CONCAT(:workoutVideoPath, w.workoutVideo), CONCAT(:workoutVideoThumbnailImage, w.workoutVideoThumbnailImage)," +
            "fvp.isFavorite, w.isDeleted, w.isActive, fvp.createdAt, fvp.updatedAt) " +
            "FROM EntityFavoriteWorkoutPlan fvp " +
            "LEFT JOIN EntityWorkoutPlan w ON w.workoutPlanId = fvp.workoutPlanId " +
            "WHERE fvp.userId =:userId " +
            "AND fvp.isFavorite =:isFavorite " +
            "AND w.isActive =:isActive " +
            "AND w.isDeleted =:isDeleted")
    List<WorkoutPlanlistDetailResDTO> findFavoriteWorkoutPlans(Long userId, Pageable pageable, String workoutVideoPath, String workoutVideoThumbnailImage, boolean isFavorite, boolean isActive, boolean isDeleted);

    @Query("SELECT  COUNT(wp) FROM EntityWorkoutPlan wp " +
            "WHERE wp.workoutPlaylistType=:workoutPlaylistType and wp.isDeleted =:isDeleted AND wp.isActive =:isActive ")
    long countWorkoutPlanForCMSDashboard(boolean isDeleted,boolean isActive,WorkoutPlaylistType workoutPlaylistType);

    @Query("SELECT DISTINCT new com.mindbody.api.dto.cms.WorkoutPlanlistDetailResDTO(w.workoutPlanId, w.workoutPlanTitle, w.workoutPlanDescription, w.workoutPlanType, w.workoutPlaylistType, w.workoutPlaylistId, w.entityWorkoutPlaylist.workoutPlaylistName, w.equipment, w.workoutPlanDuration, w.difficultyLevel, CONCAT(:workoutVideoPath, w.workoutVideo), CONCAT(:workoutVideoThumbnailImage, w.workoutVideoThumbnailImage), COALESCE(fvp.isFavorite, false), w.isDeleted, w.isActive, w.createdAt, w.updatedAt) " +
            "FROM EntityWorkoutPlan w " +
            "LEFT JOIN EntityFavoriteWorkoutPlan fvp ON fvp.workoutPlanId = w.workoutPlanId AND (:userId IS NULL OR fvp.userId = :userId) " +
            "LEFT JOIN EntityWorkoutPlanZodiacSign wzs ON wzs.entityWorkoutPlan.workoutPlanId = w.workoutPlanId " +
            "WHERE w.workoutPlanId = :workoutPlanId AND w.isDeleted = false")
    WorkoutPlanlistDetailResDTO findWorkoutPlanById(Long userId,Long workoutPlanId, String workoutVideoPath, String workoutVideoThumbnailImage);


}
