package com.mindbody.api.repository;

import com.mindbody.api.dto.UserWorkoutPlaylistResDTO;
import com.mindbody.api.dto.cms.WorkoutPlaylistDetailResDTO;
import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.enums.WorkoutPlaylistType;
import com.mindbody.api.model.EntityAudioPlaylist;
import com.mindbody.api.model.EntityWorkoutPlaylist;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface WorkoutPlaylistRepository extends JpaRepository<EntityWorkoutPlaylist, Long> {

    Optional<EntityWorkoutPlaylist> findByWorkoutPlaylistNameAndWorkoutPlaylistTypeAndIsActiveAndIsDeleted(String workoutPlaylistName, WorkoutPlaylistType workoutPlaylistType, boolean isActive, boolean isDeleted);

    Optional<EntityWorkoutPlaylist> findByWorkoutPlaylistNameAndWorkoutPlaylistTypeAndIsActiveAndIsDeletedAndWorkoutPlaylistIdNot(String workoutPlaylistName, WorkoutPlaylistType workoutPlaylistType, boolean isActive, boolean isDeleted, Long workoutPlaylistId);

    EntityWorkoutPlaylist findByWorkoutPlaylistIdAndIsDeleted(Long workoutPlaylistId, boolean isDeleted);

    @Query("SELECT COUNT(w) FROM EntityWorkoutPlaylist w where lower(w.workoutPlaylistName) LIKE lower(concat('%',:queryToSearch,'%'))" +
            "AND (:isWorkoutPlaylistTypeFilter = false OR w.workoutPlaylistType = :workoutPlaylistType) " +
            "AND w.isDeleted = :isDeleted")
    long countWorkoutPlaylists(String queryToSearch, WorkoutPlaylistType workoutPlaylistType, boolean isWorkoutPlaylistTypeFilter, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.cms.WorkoutPlaylistDetailResDTO(w.workoutPlaylistId, w.workoutPlaylistType, w.workoutPlaylistName, w.isDeleted, w.isActive, w.createdAt, w.updatedAt, " +
            "CONCAT(:imagePrefix, w.workoutPlaylistImage)) " +
            "FROM EntityWorkoutPlaylist w " +
            "WHERE lower(w.workoutPlaylistName) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND (:isWorkoutPlaylistTypeFilter = false OR w.workoutPlaylistType = :workoutPlaylistType) " +
            "AND w.isDeleted = :isDeleted")
    List<WorkoutPlaylistDetailResDTO> findAllWorkoutPlaylists(String queryToSearch, String imagePrefix, Pageable pageable, WorkoutPlaylistType workoutPlaylistType, boolean isWorkoutPlaylistTypeFilter, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.UserWorkoutPlaylistResDTO(w.workoutPlaylistId,w.workoutPlaylistName, " +
            "CONCAT(:imagePrefix, w.workoutPlaylistImage),w.workoutPlaylistType) " +
            "FROM EntityWorkoutPlaylist w " +
            "WHERE w.workoutPlaylistType = :workoutPlaylistType " +
            "AND w.isActive = :isActive " +
            "AND w.isDeleted = :isDeleted")
    List<UserWorkoutPlaylistResDTO> findAllWorkoutPlaylistsForSelectedWorkoutPlaylistType(String imagePrefix, WorkoutPlaylistType workoutPlaylistType, boolean isActive, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.UserWorkoutPlaylistResDTO(w.workoutPlaylistId,w.workoutPlaylistName, " +
            "CONCAT(:imagePrefix, w.workoutPlaylistImage),w.workoutPlaylistType) " +
            "FROM EntityWorkoutPlaylist w " +
            "WHERE w.isActive = :isActive " +
            "AND w.isDeleted = :isDeleted " +
            "ORDER BY w.workoutPlaylistName ASC LIMIT :count")
    List<UserWorkoutPlaylistResDTO> findWorkoutPlaylistsForHomeScreen(int count, String imagePrefix, boolean isActive, boolean isDeleted);


}
