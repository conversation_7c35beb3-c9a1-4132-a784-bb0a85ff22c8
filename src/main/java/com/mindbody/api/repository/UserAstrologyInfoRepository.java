package com.mindbody.api.repository;

import com.mindbody.api.model.EntityUserAstrologyInfo;
import jdk.jfr.Registered;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

@Registered
public interface UserAstrologyInfoRepository extends JpaRepository<EntityUserAstrologyInfo, Long> {

    List<EntityUserAstrologyInfo> findByUserIdAndIsActiveAndIsDeleted(Long userId, boolean isActive, boolean isDeleted);

}
