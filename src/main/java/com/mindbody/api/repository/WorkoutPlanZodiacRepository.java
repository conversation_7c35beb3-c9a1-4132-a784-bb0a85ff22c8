package com.mindbody.api.repository;

import com.mindbody.api.dto.cms.WorkoutPlanZodiacSignDTO;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.model.EntityWorkoutPlanZodiacSign;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WorkoutPlanZodiacRepository extends JpaRepository<EntityWorkoutPlanZodiacSign, Long> {

    @Query("SELECT new com.mindbody.api.dto.cms.WorkoutPlanZodiacSignDTO(wzs.entityWorkoutPlan.workoutPlanId, wzs.zodiacSign) " +
            "FROM EntityWorkoutPlanZodiacSign wzs " +
            "WHERE wzs.entityWorkoutPlan.workoutPlanId IN :workoutPlanIds")
    List<WorkoutPlanZodiacSignDTO> findZodiacSignsByAudioIds(List<Long> workoutPlanIds);

    List<EntityWorkoutPlanZodiacSign> findByEntityWorkoutPlan_WorkoutPlanIdAndZodiacSignIn(Long workoutPlanId, List<ZodiacSignType> zodiacSigns);

    @Query("SELECT new com.mindbody.api.dto.cms.WorkoutPlanZodiacSignDTO(wzs.entityWorkoutPlan.workoutPlanId,wzs.zodiacSign) " +
            "FROM EntityWorkoutPlanZodiacSign wzs " +
            "WHERE wzs.entityWorkoutPlan.workoutPlanId = :workoutPlanId")
    List<WorkoutPlanZodiacSignDTO> findZodiacSignsByWorkoutPlanId(Long workoutPlanId);

}
