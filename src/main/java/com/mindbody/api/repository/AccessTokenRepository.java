package com.mindbody.api.repository;


import com.mindbody.api.dto.UserAccessTokenDTO;
import com.mindbody.api.dto.cms.AdminAccessTokenDTO;
import com.mindbody.api.enums.RoleType;
import com.mindbody.api.model.EntityAccessToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface AccessTokenRepository extends JpaRepository<EntityAccessToken, Long> {

    @Query("SELECT new com.mindbody.api.dto.UserAccessTokenDTO(u.userId ,u.email ,u.countryCode ,u.phoneNumber ,u.roleType ,u.userType ,u.accountStatus ,at.accessToken ,at.secretToken ,u.isDeleted ,u.isActive) FROM EntityUser u JOIN EntityAccessToken at ON u.userId = at.userId WHERE at.accessToken = :jwtToken AND (u.email = :email OR (u.countryCode =:countryCode AND u.phoneNumber =:phoneNumber)) AND at.roleType = :roleType AND u.isActive = true AND u.isDeleted = false ")
    List<UserAccessTokenDTO> findByEmailAndRoleAndToken(String email, String countryCode, String phoneNumber, RoleType roleType, String jwtToken);

    @Query("SELECT new com.mindbody.api.dto.UserAccessTokenDTO(u.userId ,u.email ,u.countryCode ,u.phoneNumber ,u.roleType ,u.userType ,u.accountStatus ,at.accessToken ,at.secretToken ,u.isDeleted ,u.isActive) FROM EntityUser u JOIN EntityAccessToken at ON u.userId = at.userId WHERE at.accessToken = :jwtToken AND ((u.email = :email OR u.facebookEmailId = :email OR u.appleEmailId = :email OR u.googleEmailId = :email) OR (u.countryCode =:countryCode AND u.phoneNumber =:phoneNumber)) AND at.roleType = :roleType AND u.isActive = true AND u.isDeleted = false ")
    List<UserAccessTokenDTO> findByAllEmailAndRoleAndToken(String email, String countryCode, String phoneNumber, RoleType roleType, String jwtToken);

    @Query("SELECT new com.mindbody.api.dto.cms.AdminAccessTokenDTO(a.adminId, a.firstName, a.lastName, a.email, a.profileImage, a.isDeleted, a.isActive, at.accessToken) From EntityAdmin a JOIN EntityAccessToken at ON a.adminId = at.userId WHERE at.accessToken = :jwtToken AND a.email = :email AND at.roleType = :roleType AND a.isActive = true AND a.isDeleted = false ")
    List<AdminAccessTokenDTO> getAdminAccessToken(String email, RoleType roleType, String jwtToken);

    void deleteAllByUserId(Long userId);

    @Query("SELECT new com.mindbody.api.dto.UserAccessTokenDTO(u.userId ,u.email ,u.countryCode ,u.phoneNumber ,u.roleType ,u.userType ,u.accountStatus ,at.accessToken ,at.secretToken ,u.isDeleted ,u.isActive) FROM EntityUser u JOIN EntityAccessToken at ON u.userId = at.userId WHERE at.secretToken = :secretToken AND at.roleType = :roleType AND u.isActive = true AND u.isDeleted = false ")
    List<UserAccessTokenDTO> findByRoleAndSecretToken(RoleType roleType, String secretToken);


    @Transactional
    @Modifying
    @Query("DELETE FROM EntityAccessToken at WHERE at.userId = :userId and at.roleType =:roleType")
    void deleteSecretTokenByRoleAndUserId(Long userId, RoleType roleType);

    void deleteByAccessTokenAndUserId(String accessToken, Long userId);

    void deleteByUserId(Long userId);

    Optional<EntityAccessToken> findFirstByUserIdAndRoleTypeOrderByCreatedAtDesc(Long userId, RoleType roleType);


}
