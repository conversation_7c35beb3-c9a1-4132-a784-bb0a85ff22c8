package com.mindbody.api.repository;

import com.mindbody.api.model.EntityUserTopicSubscriptions;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserTopicSubscriptionsRepository extends JpaRepository<EntityUserTopicSubscriptions,Long> {
    void deleteByUserIdAndTopicNameIn(Long userId, List<String> topicNames);

    // Find userIds who are subscribed to any of the given topics
    @Query("SELECT DISTINCT e.userId FROM EntityUserTopicSubscriptions e WHERE e.topicName IN :topicNames")
    List<Long> findUserIdsByTopicNames(@Param("topicNames") List<String> topicNames);

    @Query("SELECT uts.topicName FROM EntityUserTopicSubscriptions uts WHERE uts.userId = :userId")
    List<String> findTopicNamesByUserId(@Param("userId") Long userId);
}
