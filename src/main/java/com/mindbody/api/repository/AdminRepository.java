package com.mindbody.api.repository;

import com.mindbody.api.model.EntityAdmin;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;


@Repository
public interface AdminRepository extends JpaRepository<EntityAdmin, Long> {

    @Query("SELECT a FROM EntityAdmin a WHERE a.email =:email AND a.isActive =:isActive AND a.isDeleted =:isDeleted")
    EntityAdmin findAdminByEmail(String email, boolean isActive, boolean isDeleted);

    @Query("SELECT a FROM EntityAdmin a WHERE a.isActive =:isActive AND a.isDeleted =:isDeleted")
    EntityAdmin findAdmin(boolean isActive, boolean isDeleted);

    Optional<EntityAdmin> findByAdminIdAndIsActiveAndIsDeleted(Long userId,boolean isActive, boolean isDeleted);



}
