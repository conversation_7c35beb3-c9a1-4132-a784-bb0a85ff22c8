package com.mindbody.api.repository;

import com.mindbody.api.enums.OtpModuleName;
import com.mindbody.api.model.EntityOTP;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.Optional;

@Repository
public interface OTPRepository extends JpaRepository<EntityOTP, Long> {


    @Query("SELECT e FROM EntityOTP e WHERE e.otp = :otp AND e.userId = :userId AND e.moduleName = :otpModuleName AND e.expiryDate >= :currentDate AND e.isActive = :isActive AND e.isDeleted = :isDeleted")
    Optional<EntityOTP> findByOtpAndUserIdAndModuleNameAndExpiryDateAndIsActiveAndIsDeleted(String otp, Long userId, OtpModuleName otpModuleName, Date currentDate, boolean isActive, boolean isDeleted);

    Optional<EntityOTP> findByUserIdAndModuleNameAndIsActiveAndIsDeleted(Long userId, OtpModuleName otpModuleName, boolean isActive, boolean isDeleted);
}
