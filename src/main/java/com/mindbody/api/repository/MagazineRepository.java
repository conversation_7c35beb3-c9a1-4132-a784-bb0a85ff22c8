package com.mindbody.api.repository;

import com.mindbody.api.dto.cms.MagazineDetailResDTO;
import com.mindbody.api.model.EntityMagazine;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MagazineRepository extends JpaRepository<EntityMagazine, Long> {
    Optional<EntityMagazine> findByTitleAndIsActiveAndIsDeleted(String questionName, boolean isActive, boolean isDeleted);

    @Query("SELECT COUNT(m) FROM EntityMagazine m where (lower(m.title) LIKE lower(concat('%',:queryToSearch,'%')))" +
            "AND m.isDeleted =:isDeleted " +
            "AND (:checkIsActiveRecord = false OR m.isActive = true)")
    long countMagazines(String queryToSearch, boolean checkIsActiveRecord, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.cms.MagazineDetailResDTO(" +
            "m.magazineId, m.title, m.subTitle, m.description, m.magazineLink, m.isActive, m.isDeleted, m.createdAt, m.updatedAt, " +
            "CONCAT(:imagePrefix, m.magazineImage), " +
            "CONCAT(:thumbnailImagePrefix, m.magazineThumbnailImage)) " +
            "FROM EntityMagazine m " +
            "WHERE lower(m.title) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND m.isDeleted = :isDeleted " +
            "AND (:checkIsActiveRecord = false OR m.isActive = true)")
    Page<MagazineDetailResDTO> findAllMagazines(String queryToSearch, String imagePrefix,String thumbnailImagePrefix, boolean checkIsActiveRecord, boolean isDeleted, Pageable pageable);


    @Query("SELECT new com.mindbody.api.dto.cms.MagazineDetailResDTO(" +
            "m.magazineId, m.title,m.subTitle, m.description,m.magazineLink,m.isActive, m.isDeleted,m.createdAt, m.updatedAt, " +
            "CONCAT(:imagePrefix, m.magazineImage), " +
            "CONCAT(:thumbnailImagePrefix, m.magazineThumbnailImage)) " +
            "FROM EntityMagazine m " +
            "WHERE  m.isDeleted =:isDeleted " +
            "AND m.isActive =:isActive " +
            "ORDER BY m.title ASC LIMIT :count")
    List<MagazineDetailResDTO> findMagazineForHomeScreen(int count, String imagePrefix,String thumbnailImagePrefix, boolean isActive, boolean isDeleted);

    EntityMagazine findByMagazineIdAndIsDeleted(Long magazineId, boolean isDeleted);
}
