package com.mindbody.api.repository;

import com.mindbody.api.model.EntityAchievementLevelUp;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AchievementLevelUpRepository extends JpaRepository<EntityAchievementLevelUp,Long> {

    @Query("SELECT e FROM EntityAchievementLevelUp e ORDER BY CAST(e.wxpNeeded AS integer) ASC")
    List<EntityAchievementLevelUp> findAllOrderedByWxpNeeded();
}
