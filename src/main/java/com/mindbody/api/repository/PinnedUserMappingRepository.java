package com.mindbody.api.repository;


import com.mindbody.api.model.EntityPinnedUserMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PinnedUserMappingRepository extends JpaRepository<EntityPinnedUserMapping, Long> {

    Optional<EntityPinnedUserMapping> findByPinnedUserIdAndForUserId(Long pinnedUserId, Long forUserId);
    List<EntityPinnedUserMapping> findAllByForUserId(Long forUserId);


}
