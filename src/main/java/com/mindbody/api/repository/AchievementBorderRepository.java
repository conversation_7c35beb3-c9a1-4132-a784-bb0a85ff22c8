package com.mindbody.api.repository;

import com.mindbody.api.dto.cms.AchievementBorderListResDTO;
import com.mindbody.api.model.EntityAchievementBorder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AchievementBorderRepository extends JpaRepository<EntityAchievementBorder, Long> {

    @Query("SELECT new com.mindbody.api.dto.cms.AchievementBorderListResDTO(ab.achievementBorderId, ab.border) " +
            "FROM EntityAchievementBorder ab " +
            "WHERE ab.isActive = :isActive " +
            "AND ab.isDeleted = :isDeleted")
    List<AchievementBorderListResDTO> findAllActiveBorders(boolean isActive, boolean isDeleted);

    Optional<EntityAchievementBorder> findByAchievementBorderIdAndIsDeleted(Long achievementBorderId, boolean isDeleted);


}
