package com.mindbody.api.repository;

import com.mindbody.api.model.EntityFeedback;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FeedbackRepository extends JpaRepository<EntityFeedback, Long> {
    
    @Query("SELECT COUNT(f) FROM EntityFeedback f WHERE f.isActive = :isActive AND f.isDeleted = :isDeleted")
    long countByIsActiveAndIsDeleted(@Param("isActive") boolean isActive, @Param("isDeleted") boolean isDeleted);

    @Query("SELECT f FROM EntityFeedback f WHERE f.isActive = :isActive AND f.isDeleted = :isDeleted")
    List<EntityFeedback> findByIsActiveAndIsDeleted(@Param("isActive") boolean isActive, @Param("isDeleted") boolean isDeleted, Pageable pageable);

}