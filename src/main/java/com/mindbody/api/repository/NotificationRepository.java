package com.mindbody.api.repository;

import com.mindbody.api.dto.notification.NotificationDetailResDTO;
import com.mindbody.api.model.EntityNotification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface NotificationRepository extends JpaRepository<EntityNotification, Long> {
    @Query(value = "select COUNT(n) from EntityNotification n " +
            "where n.userId=:userId and n.isNotificationRead = false ")
    long countUnreadNotificationOfUser(Long userId);

    @Query("SELECT new com.mindbody.api.dto.notification.NotificationDetailResDTO( " +
            "n.notificationId, n.title, n.message, n.notificationType,n.image,n.createdAt) " +
            "FROM EntityNotification n " +
            "WHERE n.userId = :userId AND n.createdAt >= :threeMonthsAgo " +
            "ORDER BY n.createdAt DESC")
    List<NotificationDetailResDTO> findAllNotifications(Long userId, LocalDateTime threeMonthsAgo);

    @Modifying
    @Query(value = "UPDATE notification " +
            "SET is_notification_read = true " +
            "WHERE user_id = :userId " +
            "AND is_notification_read = false",
            nativeQuery = true)
    void markNotificationsAsRead(Long userId);


}
