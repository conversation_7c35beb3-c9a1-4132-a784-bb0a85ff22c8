package com.mindbody.api.repository;

import com.mindbody.api.model.EntityUserDevice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserDeviceRepository extends JpaRepository<EntityUserDevice, Long> {

    /**
     * Find a device by its unique ID and user ID
     */
    Optional<EntityUserDevice> findByDeviceUniqueIdAndUserIdAndIsDeleted(String deviceUniqueId, Long userId, boolean isDeleted);

    /**
     * Find a device by its IP address and user ID
     */
    Optional<EntityUserDevice> findByIpAddressAndUserIdAndIsDeleted(String ipAddress, Long userId, boolean isDeleted);

    /**
     * Find all devices by device unique ID
     */
    List<EntityUserDevice> findByDeviceUniqueIdAndIsDeleted(String deviceUniqueId, boolean isDeleted);

    /**
     * Find all devices by IP address
     */
    List<EntityUserDevice> findByIpAddressAndIsDeleted(String ipAddress, boolean isDeleted);

    /**
     * Check if a device with the given unique ID exists for any user other than the specified user
     */
    @Query("SELECT CASE WHEN COUNT(ud) > 0 THEN true ELSE false END FROM EntityUserDevice ud " +
           "WHERE ud.deviceUniqueId = :deviceUniqueId AND ud.userId <> :userId AND ud.isDeleted = :isDeleted")
    boolean existsByDeviceUniqueIdAndUserIdNotAndIsDeleted(String deviceUniqueId, Long userId, boolean isDeleted);

    /**
     * Check if a device with the given IP address exists for any user other than the specified user
     */
    @Query("SELECT CASE WHEN COUNT(ud) > 0 THEN true ELSE false END FROM EntityUserDevice ud " +
           "WHERE ud.ipAddress = :ipAddress AND ud.userId <> :userId AND ud.isDeleted = :isDeleted")
    boolean existsByIpAddressAndUserIdNotAndIsDeleted(String ipAddress, Long userId, boolean isDeleted);

    /**
     * Find devices that were associated with deleted accounts
     */
    List<EntityUserDevice> findByDeviceUniqueIdAndWasDeletedAccountAndIsDeleted(String deviceUniqueId, boolean wasDeletedAccount, boolean isDeleted);

    /**
     * Find devices that were associated with deleted accounts by IP address
     */
    List<EntityUserDevice> findByIpAddressAndWasDeletedAccountAndIsDeleted(String ipAddress, boolean wasDeletedAccount, boolean isDeleted);
}
