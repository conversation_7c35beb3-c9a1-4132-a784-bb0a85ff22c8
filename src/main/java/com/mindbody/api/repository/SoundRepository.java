package com.mindbody.api.repository;

import com.mindbody.api.dto.cms.SoundDetailResDTO;
import com.mindbody.api.enums.SoundType;
import com.mindbody.api.model.EntitySound;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SoundRepository extends JpaRepository<EntitySound, Long> {
    Optional<EntitySound> findByTitleAndIsActiveAndIsDeleted(String title, Boolean isActive, Boolean isDeleted);

    Optional<EntitySound> findByTitleAndSoundTypeAndIsActiveAndIsDeleted(String title, SoundType soundType, Boolean isActive, Boolean isDeleted);

    EntitySound findBySoundIdAndIsDeleted(Long soundId, boolean isDeleted);

    @Query("SELECT COUNT(s) FROM EntitySound s where (lower(s.title) LIKE lower(concat('%',:queryToSearch,'%')))" +
            "AND s.isDeleted =:isDeleted " +
            "AND (:checkIsActiveRecord = false OR s.isActive = true)")
    long countSounds(String queryToSearch, boolean checkIsActiveRecord, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.cms.SoundDetailResDTO(" +
            "s.soundId, s.title, s.soundType, s.isActive, s.isDeleted, " +
            "CONCAT(:imagePrefix, COALESCE(s.soundFile, ''))) " +
            "FROM EntitySound s " +
            "WHERE LOWER(s.title) LIKE LOWER(CONCAT('%', :queryToSearch, '%')) " +
            "AND s.isDeleted = :isDeleted " +
            "AND (:checkIsActiveRecord = FALSE OR s.isActive = TRUE)")
    Page<SoundDetailResDTO> findAllSounds(String queryToSearch, String imagePrefix,
                                          boolean checkIsActiveRecord, boolean isDeleted,
                                          Pageable pageable);

}
