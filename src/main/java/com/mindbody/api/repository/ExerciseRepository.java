package com.mindbody.api.repository;

import com.mindbody.api.dto.cms.CMSListExerciseResDTO;
import com.mindbody.api.dto.cms.ExerciseResDTO;
import com.mindbody.api.model.EntityExercise;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ExerciseRepository extends JpaRepository<EntityExercise, Long> {

    EntityExercise findByExerciseIdAndIsDeleted(Long exerciseId, boolean isDeleted);

    @Query("SELECT  COUNT(e) FROM EntityExercise e " +
            "WHERE lower(e.title) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND e.isDeleted =:isDeleted ")
    long countExercise(String queryToSearch, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.cms.CMSListExerciseResDTO(e.exerciseId, e.title,CONCAT(:exerciseVideoPath, e.exerciseVideo), CONCAT(:exerciseThumbnailImagePath, e.exerciseThumbnailImage)) " +
            "FROM EntityExercise e " +
            "WHERE e.isActive=:isActive AND e.isDeleted=:isDeleted")
    List<CMSListExerciseResDTO> findAllExerciseToAddWorkoutPlanCMS(boolean isActive,boolean isDeleted,String exerciseVideoPath,String exerciseThumbnailImagePath);

    @Query("SELECT new com.mindbody.api.dto.cms.ExerciseResDTO(e.exerciseId, e.title, e.description, CONCAT(:exerciseVideoPath, e.exerciseVideo), CONCAT(:exerciseThumbnailImagePath, e.exerciseThumbnailImage), e.createdAt, e.updatedAt, e.isDeleted, e.isActive) " +
            "FROM EntityExercise e " +
            "WHERE lower(e.title) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND e.isDeleted =:isDeleted ")
    List<ExerciseResDTO> findAllExerciseCms(String queryToSearch, Pageable pageable, String exerciseVideoPath, String exerciseThumbnailImagePath, boolean isDeleted);

    boolean existsByTitleAndIsDeleted(String title, boolean isDeleted);

    boolean existsByExerciseIdNotAndTitleAndIsDeleted(Long exerciseId, String title, boolean isDeleted);

    Optional<EntityExercise> findByTitleAndIsActiveAndIsDeleted(String title,boolean isActive,boolean isDeleted);
}
