package com.mindbody.api.repository;

import com.mindbody.api.dto.cms.AchievementRewardsResDTO;
import com.mindbody.api.model.EntityAchievementRewards;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AchievementRewardsRepository extends JpaRepository<EntityAchievementRewards, Long> {

    @Query("SELECT new com.mindbody.api.dto.cms.AchievementRewardsResDTO(" +
            "ar.achievementRewardsId, " +
            "ar.points, " +
            "ar.wxp, " +
            "am.medalName) " +
            "FROM EntityAchievementRewards ar " +
            "JOIN ar.entityAchievementMedals am")
    List<AchievementRewardsResDTO> findAllAchievementRewards();

    EntityAchievementRewards findByAchievementRewardsId(Long achievementRewardsId);
}
