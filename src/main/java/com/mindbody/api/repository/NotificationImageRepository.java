package com.mindbody.api.repository;

import com.mindbody.api.enums.NotificationType;
import com.mindbody.api.model.EntityNotificationImage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface NotificationImageRepository extends JpaRepository<EntityNotificationImage,Long> {

    EntityNotificationImage findByNotificationType(NotificationType notificationType);

    @Query("SELECT ni FROM EntityNotificationImage ni WHERE ni.notificationType IN :notificationTypes")
    List<EntityNotificationImage> findByNotificationTypeIn(@Param("notificationTypes") Collection<NotificationType> notificationTypes);
}
