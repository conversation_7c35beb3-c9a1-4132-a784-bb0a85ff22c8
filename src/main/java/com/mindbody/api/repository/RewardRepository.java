package com.mindbody.api.repository;

import com.mindbody.api.enums.RewardType;
import com.mindbody.api.model.EntityReward;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface RewardRepository extends JpaRepository<EntityReward, Long> {

    Optional<EntityReward> findByRewardIdAndIsActiveAndIsDeleted(Long rewardId, boolean isActive, boolean isDeleted);

    @Query("SELECT r FROM EntityReward r " +
            "WHERE lower(r.title) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND r.isActive = :isActive " +
            "AND r.isDeleted = :isDeleted")
    List<EntityReward> findAvailableRewards(
            @Param("queryToSearch") String queryToSearch,
            @Param("isActive") boolean isActive,
            @Param("isDeleted") boolean isDeleted,
            Pageable pageable);

    @Query("SELECT COUNT(r) FROM EntityReward r " +
            "WHERE lower(r.title) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND (:rewardType IS NULL OR r.rewardType = :rewardType) " +
            "AND r.isActive = :isActive " +
            "AND r.isDeleted = :isDeleted")
    long countAvailableRewards(
            @Param("queryToSearch") String queryToSearch,
            @Param("rewardType") RewardType rewardType,
            @Param("isActive") boolean isActive,
            @Param("isDeleted") boolean isDeleted);

    List<EntityReward> findByRewardTypeAndIsActiveAndIsDeleted(RewardType rewardType, boolean isActive, boolean isDeleted);
} 