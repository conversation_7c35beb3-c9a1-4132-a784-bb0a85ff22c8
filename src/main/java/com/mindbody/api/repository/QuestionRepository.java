package com.mindbody.api.repository;

import com.mindbody.api.dto.QuestionOptionResDTO;
import com.mindbody.api.model.EntityQuestion;
import com.mindbody.api.model.EntityQuestionOption;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface QuestionRepository extends JpaRepository<EntityQuestion, Long> {

    Optional<EntityQuestion> findByQuestionNameAndIsActiveAndIsDeleted(String questionName, boolean isActive, boolean isDeleted);

    EntityQuestion findByQuestionId(Long questionId);

    Optional<EntityQuestion> findByQuestionNameAndQuestionIdNotAndIsActiveAndIsDeleted(String questionName, Long questionId, boolean isActive, boolean isDeleted);

    @Query("SELECT COUNT(q) FROM EntityQuestion q where (lower(q.questionName) LIKE lower(concat('%',:queryToSearch,'%')))" +
            "AND q.isDeleted =:isDeleted " +
            "AND (:checkIsActiveRecord = false OR q.isActive = true)")
    long countQuestions(String queryToSearch, boolean checkIsActiveRecord, boolean isDeleted);

    @Query("SELECT q FROM EntityQuestion q where q.isActive =:isActive " +
            "AND q.isDeleted =:isDeleted")
    List<EntityQuestion> findAllQuestionList(boolean isActive, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.QuestionOptionResDTO(" +
            "q.questionId, q.questionName,q.questionTitle,q.orderNo, q.isActive, q.isDeleted, q.createdAt, q.updatedAt, " +
            "o.optionId, o.optionName, CONCAT(:imagePrefix, o.optionImage)) " +
            "FROM EntityQuestion q " +
            "LEFT JOIN EntityQuestionOption o ON q.questionId = o.questionId " +
            "WHERE lower(q.questionName) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND q.isDeleted =:isDeleted " +
            "AND (:checkIsActiveRecord = false OR q.isActive = true)" +
            "AND o.isDeleted =:isDeleted " +
            "AND (:checkIsActiveRecord = false OR o.isActive = true)")
    List<QuestionOptionResDTO> findAllQuestionsWithOptions(String queryToSearch, String imagePrefix, boolean checkIsActiveRecord, boolean isDeleted);

    @Query("SELECT q FROM EntityQuestionOption q where q.questionId IN (:queList) " +
            "AND q.isActive =:isActive " +
            "AND q.isDeleted =:isDeleted")
    List<EntityQuestionOption> findAllQuestionOptonsList(List<Long> queList, boolean isActive, boolean isDeleted);

    @Query("SELECT q FROM EntityQuestion q where q.questionId IN (:questionIdList) AND q.isActive =:isActive " +
            "AND q.isDeleted =:isDeleted")
    List<EntityQuestion> findAllQuestionByQuestionIdList(List<Long> questionIdList, boolean isActive, boolean isDeleted);

    Optional<EntityQuestion> findTopByIsActiveAndIsDeletedOrderByOrderNoDesc(boolean isActive,boolean isDeleted);

}