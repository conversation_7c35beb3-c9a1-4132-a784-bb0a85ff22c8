package com.mindbody.api.repository;

import com.mindbody.api.dto.cms.ProductCategoryResDTO;
import com.mindbody.api.model.EntityProductCategory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductCategoryRepository extends JpaRepository<EntityProductCategory, Long> {

    boolean existsByProductCategoryNameAndIsDeleted(String productCategoryName, boolean isDeleted);

    EntityProductCategory findByProductCategoryIdAndIsDeleted(Long productCategoryId, boolean isDeleted);

    boolean existsByProductCategoryIdNotAndProductCategoryNameAndIsDeleted(Long productCategoryId, String productCategoryName, boolean isDeleted);


    @Query("SELECT  COUNT(pc) FROM EntityProductCategory pc " +
            "WHERE lower(pc.productCategoryName) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND pc.isDeleted =:isDeleted ")
    long countProductCategories(String queryToSearch, boolean isDeleted);


    @Query("SELECT new com.mindbody.api.dto.cms.ProductCategoryResDTO(pc.productCategoryId, pc.productCategoryName, CONCAT(:productCategoryImagePath, pc.productCategoryImage), pc.createdAt, pc.updatedAt, pc.isDeleted, pc.isActive) " +
            "FROM EntityProductCategory pc " +
            "WHERE lower(pc.productCategoryName) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND pc.isDeleted =:isDeleted ")
    List<ProductCategoryResDTO> findAllProductCategories(String queryToSearch, String productCategoryImagePath, Pageable pageable, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.cms.ProductCategoryResDTO(pc.productCategoryId, pc.productCategoryName, CONCAT(:productCategoryImagePath, pc.productCategoryImage), pc.createdAt, pc.updatedAt, pc.isDeleted, pc.isActive) " +
            "FROM EntityProductCategory pc " +
            "WHERE pc.isActive =:isActive " +
            "AND pc.isDeleted =:isDeleted")
    List<ProductCategoryResDTO> findAllProductCategoriesWithoutPagination(String productCategoryImagePath, boolean isActive, boolean isDeleted);
}
