package com.mindbody.api.repository;

import com.mindbody.api.dto.cms.DailyTipsResSTO;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.model.EntityDailyTips;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DailyTipsRepository extends JpaRepository<EntityDailyTips, Long> {

    @Query("SELECT COUNT(d) FROM EntityDailyTips d where lower(d.title) LIKE lower(concat('%',:queryToSearch,'%'))" +
            "AND (:isZodiacFilter = false OR d.zodiacSign = :zodiacSign)" +
            "AND d.isDeleted = :isDeleted")
    long countDailyTips(String queryToSearch, ZodiacSignType zodiacSign, boolean isZodiacFilter, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.cms.DailyTipsResSTO(d.dailyTipsId, d.zodiacSign, d.title, d.isDeleted, d.isActive, d.createdAt, d.updatedAt) " +
            "FROM EntityDailyTips d " +
            "WHERE lower(d.title) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND (:isZodiacFilter = false OR d.zodiacSign = :zodiacSign) " +
            "AND d.isDeleted = :isDeleted")
    List<DailyTipsResSTO> findAllDailyTips(String queryToSearch, Pageable pageable, ZodiacSignType zodiacSign, boolean isZodiacFilter, boolean isDeleted);

    EntityDailyTips findByDailyTipsIdAndIsDeleted(Long dailyTipsId, boolean isDeleted);

    EntityDailyTips findByDailyTipsId(Long dailyTipsId);

    List<EntityDailyTips> findAllByIsReadAndIsActiveAndIsDeletedOrderByZodiacSignAscTitleAsc(boolean isRead, boolean isActive, boolean isDeleted);

    @Transactional
    @Modifying
    @Query("UPDATE EntityDailyTips t SET t.isRead = false WHERE t.zodiacSign =:zodiacSignType")
    void updateAllDailyTipsByZodiacSign(ZodiacSignType zodiacSignType);

    @Transactional
    @Modifying
    @Query("UPDATE EntityDailyTips t SET t.isRead = true WHERE t.zodiacSign =:zodiacSignType AND t.dailyTipsId IN :tipsIdList")
    void updateThreeDailyTipsById(ZodiacSignType zodiacSignType, List<Long> tipsIdList);

    @Query("SELECT new com.mindbody.api.dto.cms.DailyTipsResSTO(d.dailyTipsId, d.zodiacSign, d.title, d.isDeleted, d.isActive, d.createdAt, d.updatedAt) " +
            "FROM EntityDailyTips d " +
            "WHERE d.zodiacSign = :zodiacSign " +
            "AND d.isActive =:isActive " +
            "AND d.isDeleted =:isDeleted " +
            "ORDER BY d.title ASC LIMIT :count")
    List<DailyTipsResSTO> findDailyTipsByZodiacSignWiseForHomeScreen(ZodiacSignType zodiacSign, int count, boolean isActive, boolean isDeleted);


}
