package com.mindbody.api.repository;

import com.mindbody.api.model.EntityUserUnlockedAudio;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing user unlocked audio entities
 */
@Repository
public interface UserUnlockedAudioRepository extends JpaRepository<EntityUserUnlockedAudio, Long> {

    /**
     * Find unlocked audio by user ID and audio ID
     *
     * @param userId   User ID
     * @param audioId  Audio ID
     * @param isActive Active status
     * @param isDeleted Deleted status
     * @return Optional of EntityUserUnlockedAudio
     */
    Optional<EntityUserUnlockedAudio> findByUserIdAndAudioIdAndIsActiveAndIsDeleted(
            Long userId, Long audioId, boolean isActive, boolean isDeleted);

    /**
     * Find all unlocked audios by user ID
     *
     * @param userId   User ID
     * @param isActive Active status
     * @param isDeleted Deleted status
     * @return List of EntityUserUnlockedAudio
     */
    List<EntityUserUnlockedAudio> findAllByUserIdAndIsActiveAndIsDeleted(
            Long userId, boolean isActive, boolean isDeleted);

    /**
     * Check if an audio is unlocked by a user
     *
     * @param userId   User ID
     * @param audioId  Audio ID
     * @param isActive Active status
     * @param isDeleted Deleted status
     * @return true if audio is unlocked, false otherwise
     */
    @Query("SELECT COUNT(ua) > 0 FROM EntityUserUnlockedAudio ua " +
            "WHERE ua.userId = :userId " +
            "AND ua.audioId = :audioId " +
            "AND ua.isUnlocked = true " +
            "AND ua.isActive = :isActive " +
            "AND ua.isDeleted = :isDeleted")
    boolean isAudioUnlockedByUser(Long userId, Long audioId, boolean isActive, boolean isDeleted);

    /**
     * Count unlocked audios by user ID
     *
     * @param userId   User ID
     * @param isActive Active status
     * @param isDeleted Deleted status
     * @return Count of unlocked audios
     */
    long countByUserIdAndIsUnlockedAndIsActiveAndIsDeleted(
            Long userId, boolean isUnlocked, boolean isActive, boolean isDeleted);
}
