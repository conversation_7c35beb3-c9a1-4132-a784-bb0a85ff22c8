package com.mindbody.api.repository;

import com.mindbody.api.model.EntityFavoriteAudio;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface FavoriteAudioRepository extends JpaRepository<EntityFavoriteAudio,Long> {

    EntityFavoriteAudio findByUserIdAndAudioIdAndIsActiveAndIsDeleted(Long userId, Long audioId, boolean isActive, boolean isDeleted);

    @Transactional
    @Modifying
    @Query("DELETE FROM EntityFavoriteAudio f WHERE f.audioId=:audioId")
    void deleteByAudioId(Long audioId);
}
