package com.mindbody.api.repository;

import com.mindbody.api.dto.cms.ExerciseDetailResDTO;
import com.mindbody.api.enums.WorkoutPlanType;
import com.mindbody.api.model.EntityWorkoutPlanExercise;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WorkoutPlanExerciseRepository extends JpaRepository<EntityWorkoutPlanExercise,Long> {

    @Query("SELECT new com.mindbody.api.dto.cms.ExerciseDetailResDTO(w.exerciseId,w.entityExercise.title,w.entityExercise.description, w.exerciseDuration, CONCAT(:exerciseVideoPath, e.exerciseVideo), CONCAT(:exerciseThumbnailImagePath, e.exerciseThumbnailImage)) " +
            "FROM EntityWorkoutPlanExercise w " +
            "JOIN w.entityExercise e " +
            "WHERE w.workoutPlanId = :workoutPlanId AND w.entityWorkoutPlan.workoutPlanType = :workoutPlanType")
    List<ExerciseDetailResDTO> findExerciseDetailsByWorkoutPlanIdAndType(Long workoutPlanId,WorkoutPlanType workoutPlanType,String exerciseVideoPath, String exerciseThumbnailImagePath);

    List<EntityWorkoutPlanExercise> findByWorkoutPlanIdAndExerciseIdIn(Long workoutPlanId,List<Long> exerciseVideoIds);

    List<EntityWorkoutPlanExercise> findAllByExerciseId(Long exerciseId);

    EntityWorkoutPlanExercise findByWorkoutPlanIdAndExerciseId(Long workoutPlanId,Long exerciseId);


}
