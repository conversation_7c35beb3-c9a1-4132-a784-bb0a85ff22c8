package com.mindbody.api.repository;


import com.mindbody.api.model.EntityUserPointsTracker;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface UserPointsTrackerRepository extends JpaRepository<EntityUserPointsTracker, Long> {

    Optional<EntityUserPointsTracker> findByUserIdAndIsDeleted(Long userId, boolean isDeleted);

    Optional<EntityUserPointsTracker> findByUserIdAndIsActiveAndIsDeleted(Long userId, boolean isActive, boolean isDeleted);

    @Query("SELECT COUNT(upt) FROM EntityUserPointsTracker upt WHERE upt.currentLevel BETWEEN :startLevel AND :endLevel")
    long countUsersByLevelRange(int startLevel, int endLevel);

    List<EntityUserPointsTracker> findAllByUserIdIn(Set<Long> userId);

}
