package com.mindbody.api.repository;

import com.mindbody.api.model.EntityUserRedeemedAudio;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing user redeemed audio entities
 */
@Repository
public interface UserRedeemedAudioRepository extends JpaRepository<EntityUserRedeemedAudio, Long> {

    /**
     * Find redeemed audio by user ID and audio ID
     *
     * @param userId User ID
     * @param audioId Audio ID
     * @param isActive Active status
     * @param isDeleted Deleted status
     * @return Optional of EntityUserRedeemedAudio
     */
    Optional<EntityUserRedeemedAudio> findByUserIdAndAudioIdAndIsActiveAndIsDeleted(
            Long userId, Long audioId, boolean isActive, boolean isDeleted);

    /**
     * Find all redeemed audios by user ID
     *
     * @param userId User ID
     * @param isActive Active status
     * @param isDeleted Deleted status
     * @return List of EntityUserRedeemedAudio
     */
    List<EntityUserRedeemedAudio> findAllByUserIdAndIsActiveAndIsDeleted(
            Long userId, boolean isActive, boolean isDeleted);

    /**
     * Check if an audio is redeemed by a user
     *
     * @param userId User ID
     * @param audioId Audio ID
     * @param isActive Active status
     * @param isDeleted Deleted status
     * @return true if audio is redeemed, false otherwise
     */
    @Query("SELECT COUNT(ra) > 0 FROM EntityUserRedeemedAudio ra " +
            "WHERE ra.userId = :userId " +
            "AND ra.audioId = :audioId " +
            "AND ra.isRedeemed = true " +
            "AND ra.isActive = :isActive " +
            "AND ra.isDeleted = :isDeleted")
    boolean isAudioRedeemedByUser(Long userId, Long audioId, boolean isActive, boolean isDeleted);
} 