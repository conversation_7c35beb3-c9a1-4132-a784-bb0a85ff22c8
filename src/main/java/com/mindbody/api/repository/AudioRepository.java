package com.mindbody.api.repository;

import com.mindbody.api.dto.ExclusiveAudioStatusDTO;
import com.mindbody.api.dto.RandomAudioResDTO;
import com.mindbody.api.dto.UserAudioListResDTO;
import com.mindbody.api.dto.cms.AudioResDTO;
import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.model.EntityAudio;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AudioRepository extends JpaRepository<EntityAudio, Long> {

    EntityAudio findByAudioIdAndIsDeleted(@NotNull(message = "audio_id_required") Long audioId, boolean isDeleted);

    @Query("SELECT  COUNT(a) FROM EntityAudio a " +
            "WHERE lower(a.title) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND (:isAudioPlayListTypeFilter = false OR a.audioPlaylistType =:audioPlaylistType)" +
            "AND (:isAudioPlayListFilter = false OR a.audioPlaylistId =:audioPlaylistId) " +
            "AND a.isDeleted =:isDeleted ")
    long countAudio(String queryToSearch, AudioPlaylistType audioPlaylistType, boolean isAudioPlayListTypeFilter, Long audioPlaylistId, boolean isAudioPlayListFilter, boolean isDeleted);

    @Query("SELECT  COUNT(a) FROM EntityAudio a " +
            "WHERE a.isDeleted =:isDeleted AND a.isActive =:isActive")
    long countAudioForCMSDashboard(boolean isDeleted,boolean isActive);

    @Query("SELECT  COUNT(DISTINCT a.audioId) FROM EntityAudio a " +
            "LEFT JOIN EntityAudioZodiacSign azs ON azs.entityAudio.audioId = a.audioId " +
            "WHERE lower(a.title) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND a.audioPlaylistType =:audioPlaylistType " +
            "AND a.audioPlaylistId =:audioPlaylistId " +
            "AND (:zodiacSign IS NULL OR azs.zodiacSign = :zodiacSign) " +
            "AND a.isActive =:isActive " +
            "AND a.isDeleted =:isDeleted")
    long countAudioForUser(String queryToSearch, AudioPlaylistType audioPlaylistType, Long audioPlaylistId, ZodiacSignType zodiacSign, boolean isActive, boolean isDeleted);


    @Query("SELECT new com.mindbody.api.dto.cms.AudioResDTO(a.audioId, a.title, a.description, a.audioPlaylistType, " +
            "CONCAT(:thumbnailImagePath, a.thumbnailImage), CONCAT(:audioFilePath, a.audioFile), a.audioPlaylistId, " +
            "a.entityAudioPlaylist.audioPlaylistName, a.createdAt, a.updatedAt, a.isDeleted, a.isActive, a.isExclusive, a.isRedeemable) " +
            "FROM EntityAudio a " +
            "WHERE lower(a.title) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND (:isAudioPlayListTypeFilter = false OR a.audioPlaylistType =:audioPlaylistType)" +
            "AND (:isAudioPlayListFilter = false OR a.audioPlaylistId =:audioPlaylistId) " +
            "AND a.isDeleted =:isDeleted ")
    List<AudioResDTO> findAllAudio(String queryToSearch, Pageable pageable, AudioPlaylistType audioPlaylistType, boolean isAudioPlayListTypeFilter, Long audioPlaylistId, boolean isAudioPlayListFilter, String audioFilePath, String thumbnailImagePath, boolean isDeleted);


    @Query("SELECT new com.mindbody.api.dto.UserAudioListResDTO(" +
            "a.audioId, a.title, a.description, a.audioPlaylistType, " +
            "CONCAT(:thumbnailImagePath, a.thumbnailImage), CONCAT(:audioFilePath, a.audioFile), a.audioPlaylistId, " +
            "a.entityAudioPlaylist.audioPlaylistName, a.createdAt, a.updatedAt, " +
            "COALESCE(ufa.isFavorite, false), a.isDeleted, a.isActive, " +
            "a.isExclusive, CASE WHEN a.isExclusive = false THEN true ELSE COALESCE(ua.isUnlocked, false) END, " +
            "a.isRedeemable, CASE WHEN a.isRedeemable = false THEN false ELSE COALESCE(ura.isRedeemed, false) END) " +
            "FROM EntityAudio a " +
            "LEFT JOIN EntityFavoriteAudio ufa ON ufa.audioId = a.audioId AND ufa.userId = :userId " +
            "LEFT JOIN EntityUserUnlockedAudio ua ON ua.audioId = a.audioId AND ua.userId = :userId AND ua.isActive = true AND ua.isDeleted = false " +
            "LEFT JOIN EntityUserRedeemedAudio ura ON ura.audioId = a.audioId AND ura.userId = :userId AND ura.isActive = true AND ura.isDeleted = false " +
            "LEFT JOIN EntityAudioZodiacSign azs ON azs.entityAudio.audioId = a.audioId " +
            "WHERE lower(a.title) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND a.audioPlaylistType = :audioPlaylistType " +
            "AND (:isAudioPlayListFilter = false OR a.audioPlaylistId = :audioPlaylistId) " +
            "AND (:zodiacSign IS NULL OR azs.zodiacSign = :zodiacSign) " +
            "AND a.isActive = :isActive " +
            "AND a.isDeleted = :isDeleted")
    List<UserAudioListResDTO> findAllAudioForUser(String queryToSearch, Long userId, Pageable pageable, Long audioPlaylistId, ZodiacSignType zodiacSign, String audioFilePath, String thumbnailImagePath, boolean isActive, boolean isDeleted, AudioPlaylistType audioPlaylistType, boolean isAudioPlayListFilter);


    @Query("SELECT new com.mindbody.api.dto.RandomAudioResDTO(a.audioId ,a.title ,a.description ,a.audioPlaylistType ,CONCAT(:thumbnailImagePath, a.thumbnailImage) ,CONCAT(:audioFilePath, a.audioFile) ,a.audioPlaylistId ,a.entityAudioPlaylist.audioPlaylistName,a.createdAt ,a.updatedAt ,COALESCE(ufa.isFavorite, false),a.isDeleted ,a.isActive) " +
            "FROM EntityAudio a " +
            "LEFT JOIN EntityFavoriteAudio ufa ON ufa.audioId = a.audioId AND ufa.userId = :userId " +
            "WHERE a.isActive =:isActive " +
            "AND a.isDeleted =:isDeleted GROUP BY a.audioId")
    List<RandomAudioResDTO> findAllByIsActiveAndIsDeleted(Long userId, String audioFilePath, String thumbnailImagePath, boolean isActive, boolean isDeleted);

    List<EntityAudio> findAllByAudioPlaylistIdAndIsDeleted(Long audioPlaylistId, boolean isDeleted);

    EntityAudio findByAudioIdAndIsActiveAndIsDeleted(Long audioId, boolean isActive, boolean isDeleted);

    @Query("SELECT COUNT(DISTINCT fa.favoriteAudioId) FROM EntityFavoriteAudio fa " +
            "INNER JOIN EntityAudio a ON a.audioId = fa.audioId " +
            "where fa.userId =:userId " +
            "AND fa.isFavorite =:isFavorite " +
            "AND a.isActive =:isActive " +
            "AND a.isDeleted =:isDeleted")
    long countFavoriteAudio(Long userId, boolean isFavorite, boolean isActive, boolean isDeleted);

    @Query("SELECT new com.mindbody.api.dto.UserAudioListResDTO(" +
            "a.audioId, a.title, a.description, a.audioPlaylistType, " +
            "CONCAT(:thumbnailImagePath, a.thumbnailImage), " +
            "CONCAT(:audioFilePath, a.audioFile), " +
            "a.audioPlaylistId, a.entityAudioPlaylist.audioPlaylistName, " +
            "fa.createdAt, fa.updatedAt, fa.isFavorite, a.isDeleted, a.isActive, " +
            "a.isExclusive, CASE WHEN a.isExclusive = false THEN true ELSE COALESCE(ua.isUnlocked, false) END, " +
            "a.isRedeemable, CASE WHEN a.isRedeemable = false THEN false ELSE COALESCE(ura.isRedeemed, false) END) " +
            "FROM EntityFavoriteAudio fa " +
            "INNER JOIN EntityAudio a ON a.audioId = fa.audioId " +
            "LEFT JOIN EntityUserUnlockedAudio ua ON ua.audioId = a.audioId AND ua.userId = fa.userId AND ua.isActive = true AND ua.isDeleted = false " +
            "LEFT JOIN EntityUserRedeemedAudio ura ON ura.audioId = a.audioId AND ura.userId = fa.userId AND ura.isActive = true AND ura.isDeleted = false " +
            "WHERE fa.userId = :userId " +
            "AND fa.isFavorite = :isFavorite " +
            "AND a.isActive = :isActive " +
            "AND a.isDeleted = :isDeleted")
    List<UserAudioListResDTO> findAllFavoriteAudio(Long userId, Pageable pageable, String audioFilePath, String thumbnailImagePath, boolean isFavorite, boolean isActive, boolean isDeleted);

    /**
     * Find all exclusive audios
     *
     * @param isActive Active status
     * @param isDeleted Deleted status
     * @return List of EntityAudio
     */
    List<EntityAudio> findByIsExclusiveAndIsActiveAndIsDeleted(boolean isExclusive, boolean isActive, boolean isDeleted);

    /**
     * Count exclusive audios
     *
     * @param isExclusive Exclusive status
     * @param isActive Active status
     * @param isDeleted Deleted status
     * @return Count of exclusive audios
     */
    long countByIsExclusiveAndIsActiveAndIsDeleted(boolean isExclusive, boolean isActive, boolean isDeleted);

    /**
     * Count redeemable audios
     *
     * @param isRedeemable Redeemable status
     * @param isActive Active status
     * @param isDeleted Deleted status
     * @return Count of redeemable audios
     */
    long countByIsRedeemableAndIsActiveAndIsDeleted(boolean isRedeemable, boolean isActive, boolean isDeleted);

    /**
     * Find all exclusive audios with query parameters
     *
     * @param queryToSearch Search query
     * @param pageable Pageable
     * @param isExclusive Exclusive status
     * @param isActive Active status
     * @param isDeleted Deleted status
     * @return List of EntityAudio
     */
    @Query("SELECT a FROM EntityAudio a " +
            "LEFT JOIN FETCH a.entityAudioPlaylist " +
            "WHERE lower(a.title) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND a.isExclusive = :isExclusive " +
            "AND a.isActive = :isActive " +
            "AND a.isDeleted = :isDeleted")
    List<EntityAudio> findExclusiveAudios(
            @Param("queryToSearch") String queryToSearch,
            Pageable pageable,
            @Param("isExclusive") boolean isExclusive,
            @Param("isActive") boolean isActive,
            @Param("isDeleted") boolean isDeleted);

    /**
     * Find all redeemable audios with query parameters
     *
     * @param queryToSearch Search query
     * @param pageable Pageable
     * @param isRedeemable Redeemable status
     * @param isActive Active status
     * @param isDeleted Deleted status
     * @return List of EntityAudio
     */
    @Query("SELECT a FROM EntityAudio a " +
            "LEFT JOIN FETCH a.entityAudioPlaylist " +
            "WHERE lower(a.title) LIKE lower(concat('%', :queryToSearch, '%')) " +
            "AND a.isRedeemable = :isRedeemable " +
            "AND a.isActive = :isActive " +
            "AND a.isDeleted = :isDeleted")
    List<EntityAudio> findRedeemableAudios(
            @Param("queryToSearch") String queryToSearch,
            Pageable pageable,
            @Param("isRedeemable") boolean isRedeemable,
            @Param("isActive") boolean isActive,
            @Param("isDeleted") boolean isDeleted);

}
