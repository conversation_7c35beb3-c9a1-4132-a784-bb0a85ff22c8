package com.mindbody.api.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mindbody.api.base.ApiResponse;
import com.mindbody.api.util.Constant;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;


@Component
public class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private static final Logger logger = LoggerFactory.getLogger(CustomAuthenticationEntryPoint.class);

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException {
        logger.error("Responding with unauthorized error. Message - {}", authException.getMessage());
        sendResponse("User is not authorized", Constant.FAIL, response, HttpServletResponse.SC_UNAUTHORIZED);
    }

    private void sendResponse(String message, int fail, HttpServletResponse httpServletResponse, int scUnauthorized) throws IOException {
        ApiResponse errorResponse = new ApiResponse<>(objectMapper.createObjectNode(), message, fail);
        byte[] responseToSend = restResponseBytes(errorResponse);
        httpServletResponse.setHeader("Content-Type", "application/json");
        httpServletResponse.setStatus(scUnauthorized);
        httpServletResponse.getOutputStream().write(responseToSend);
    }

    private byte[] restResponseBytes(ApiResponse eErrorResponse) throws IOException {
        String serialized = objectMapper.writeValueAsString(eErrorResponse);
        return serialized.getBytes();
    }

}
