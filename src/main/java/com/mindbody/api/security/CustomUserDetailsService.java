package com.mindbody.api.security;

import com.mindbody.api.enums.RoleType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.exception.UnauthorizedException;
import com.mindbody.api.model.EntityAdmin;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.repository.AdminRepository;
import com.mindbody.api.repository.UserRepository;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.Objects;
import java.util.Optional;


@Service
public class CustomUserDetailsService implements UserDetailsService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AdminRepository adminRepository;


    @Override
    public UserDetails loadUserByUsername(String emailOrPhoneNumber) throws UsernameNotFoundException {
        RoleType roleType = ExecutionContextUtil.getContext().getRoleType();
        boolean emailProvided = ExecutionContextUtil.getContext().isEmailProvided();
        if (Objects.equals(roleType, RoleType.USER)) {
            EntityUser entityUser;
            if (emailProvided) {
                // Handle email login
                entityUser = userRepository.findUserByAllEmail(emailOrPhoneNumber,true,false);
            } else {
                // Handle phone number login
                String[] parts = emailOrPhoneNumber.split(":", 2); // Assuming format "countryCode:phoneNumber"
                if (parts.length != 2) {
                    throw new BusinessValidationException("invalid_phone_number_format");
                }
                String countryCode = parts[0];
                String phoneNumber = parts[1];
                entityUser = userRepository.findUserByCountryCodeAndPhoneNumber(countryCode,phoneNumber,true,false);
            }
            if (Objects.isNull(entityUser)) {
                throw new UnauthorizedException("user_not_found_with_email_or_phone_number_error");
            }
            return CustomUserDetails.buildNormalUser(entityUser);
        } else if (Objects.equals(roleType, RoleType.ADMIN)) {
            EntityAdmin entityAdmin = adminRepository.findAdminByEmail(emailOrPhoneNumber, true, false);

            if (Objects.isNull(entityAdmin)) {
                throw new UnauthorizedException("user_not_found_error");
            }
            return CustomUserDetails.buildCmsUser(entityAdmin);
        } else {
            throw new UnauthorizedException("user_type_not_found_error");
        }
    }

//    public EntityAdmin getUserAdminFromAccessToken(HttpServletRequest request) {
//        Long id = getId(request);
//        return findUserAdminByUserId(id);
//    }
//
//
//    protected Long getId(HttpServletRequest request) {
//        if (request != null && request.getAttribute("id") != null) {
//            return (Long) request.getAttribute("id");
//        }
//        throw new BusinessValidationException("error-internal-server");
//    }

}
