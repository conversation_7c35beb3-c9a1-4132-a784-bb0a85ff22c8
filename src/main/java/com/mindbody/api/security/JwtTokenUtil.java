package com.mindbody.api.security;

import com.mindbody.api.enums.RoleType;
import com.mindbody.api.exception.BusinessValidationException;
import com.mindbody.api.util.FieldConstant;
import com.mindbody.api.util.StringUtil;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SecurityException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.util.Date;
import java.util.Objects;

@Component
public class JwtTokenUtil {

    private static final Logger logger = LoggerFactory.getLogger(JwtTokenUtil.class);

    @Value("${app.jwt.secret}")
    private String jwtSecret;

    @Value("${app.jwt.expiration}")
    private Long jwtExpiration;

    private Key getSigningKey() {
        return Keys.hmacShaKeyFor(jwtSecret.getBytes());
    }

    public Claims getClaimsFromToken(String authToken) {
        /**
         * if X-ACCESS-TOKEN present means authorised api
         * else we check only secret key
         */
        if (StringUtil.nonNullNonEmpty(authToken)) {
            try {
                Claims claims = Jwts.parserBuilder()
                        .setSigningKey(getSigningKey())
                        .build()
                        .parseClaimsJws(authToken)
                        .getBody();

                Object role = claims.get(FieldConstant.ROLE);
                if (Objects.isNull(role) || StringUtil.nullOrEmpty(role.toString())) {
                    throw new BusinessValidationException("role_not_found_error");
                }

                String email = claims.get(FieldConstant.EMAIL, String.class);
                String countryCode = claims.get(FieldConstant.COUNTRY_CODE, String.class);
                String phoneNumber = claims.get(FieldConstant.PHONE_NUMBER, String.class);

                boolean hasEmail = StringUtil.nonNullNonEmpty(email);
                boolean hasPhoneNumber = StringUtil.nonNullNonEmpty(countryCode) && StringUtil.nonNullNonEmpty(phoneNumber);

                if (hasEmail && hasPhoneNumber) {
                    throw new BusinessValidationException("invalid_jwt_token_error");
                }
                if (hasEmail || hasPhoneNumber) {
                    return claims;
                } else {
                    throw new BusinessValidationException("token_not_present_error");
                }
            } catch (SecurityException | MalformedJwtException ex) {
                logger.error("Invalid JWT token");
                throw new BusinessValidationException("user_not_authorized_error");
            } catch (UnsupportedJwtException ex) {
                logger.error("Unsupported JWT token");
                throw new BusinessValidationException("user_not_authorized_error");
            } catch (IllegalArgumentException ex) {
                logger.error("JWT claims string is empty.");
                throw new BusinessValidationException("user_not_authorized_error");
            } catch (ExpiredJwtException ex) {
                logger.error("JWT Expired.");
                throw new BusinessValidationException("user_not_authorized_error");
            } catch (Exception e) {
                logger.error("JWT Error: {}", e.getLocalizedMessage());
                throw new BusinessValidationException("user_not_authorized_error");
            }
        } else {
            throw new BusinessValidationException("token_not_present_error");
        }
    }

    /** here we are generating the JWT token based on the email OR countryCode and phoneNumber
     *  at time either jwt has email or countryCode and phoneNumber
     *  if both things is present at same time jwt is invalid.
     */
    public String generateJwtForUser(String email, String countryCode, String phoneNumber, RoleType roleType, String registerType) {
        JwtBuilder jwtBuilder = Jwts.builder()
                .claim(FieldConstant.ROLE, roleType.name())
                .setIssuedAt(new Date())
                .setExpiration(new Date((new Date()).getTime() + jwtExpiration))
                .signWith(getSigningKey(), SignatureAlgorithm.HS512);

        if (email != null && !email.isEmpty()) {
            jwtBuilder.claim("email", email);
        }
        if (countryCode != null && phoneNumber != null && !countryCode.isEmpty() && !phoneNumber.isEmpty()) {
            jwtBuilder.claim(FieldConstant.COUNTRY_CODE, countryCode);
            jwtBuilder.claim(FieldConstant.PHONE_NUMBER, phoneNumber);
        }
        jwtBuilder.claim(FieldConstant.LOGIN_TYPE, registerType);

        return jwtBuilder.compact();
    }


}
