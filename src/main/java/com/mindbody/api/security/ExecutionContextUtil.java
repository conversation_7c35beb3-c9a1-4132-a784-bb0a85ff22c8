package com.mindbody.api.security;

import com.mindbody.api.enums.RegisterType;
import com.mindbody.api.enums.RoleType;
import com.mindbody.api.enums.UserType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

@Getter
@Setter
public class ExecutionContextUtil implements Serializable {

    @Serial
    private static final long serialVersionUID = -6751490154133933000L;
    // Atomic integer containing the next thread ID to be assigned
    private static final AtomicInteger nextId = new AtomicInteger(0);
    private static final ThreadLocal<ExecutionContextUtil> EXECUTION_CONTEXT = new ImprovedThreadLocal<ExecutionContextUtil>() {

        @Override
        protected ExecutionContextUtil initialValue() {
            return new ExecutionContextUtil(nextId.getAndIncrement());
        }
    };

    private final int threadId;
    private String password;
    private Long userId;
    private boolean multiRoleFound;
    private String emailId;
    private UserType userType;
    private RoleType roleType;
    private boolean isEmailProvided;
    private String loginType;

    private ExecutionContextUtil(int threadId) {
        this.threadId = threadId;
    }

    public static synchronized ExecutionContextUtil getContext() {
        if (Objects.isNull(EXECUTION_CONTEXT.get())) {
            EXECUTION_CONTEXT.set(new ExecutionContextUtil(nextId.getAndIncrement()));
        }
        return EXECUTION_CONTEXT.get();
    }

    public static void set(ExecutionContextUtil ExecutionContextUtil) {
        EXECUTION_CONTEXT.set(ExecutionContextUtil);
    }

    public void destroy() {
        EXECUTION_CONTEXT.remove();
    }

}
