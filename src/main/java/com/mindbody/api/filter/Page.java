package com.mindbody.api.filter;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;


public class Page implements PageFilter {

    @Min(value = 0)
    @NotNull
    private Integer pageId = 0;

    @Min(value = 0)
    private Integer limit = 100;

    @Override
    public Integer getPageId() {
        return pageId;
    }

    public void setPageId(Integer pageId) {
        this.pageId = pageId;
    }

    @Override
    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    /**
     * Convert this Page and the provided SortBy to a Spring Pageable object
     *
     * @param sortBy The sort information
     * @return A Spring Pageable object
     */
    public Pageable toPageable(SortBy sortBy) {
        Sort sort = null;
        if (sortBy != null && sortBy.getProperty() != null && !sortBy.getProperty().isEmpty()) {
            sort = Sort.by(sortBy.getDirection(), sortBy.getProperty());
        }

        return PageRequest.of(
            this.getPageId() != null ? this.getPageId() : 0,
            this.getLimit() != null ? this.getLimit() : 10,
            sort != null ? sort : Sort.unsorted()
        );
    }

}
