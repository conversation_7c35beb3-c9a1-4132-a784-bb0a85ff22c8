package com.mindbody.api.filter;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.domain.Sort;


public class SortBy implements SortProperty {

    @Schema(example = "createdAt")
    private String property = "_id";

    private Sort.Direction direction = Sort.Direction.ASC;

    @Override
    public String getProperty() {
        return this.property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    @Override
    public Sort.Direction getDirection() {
        return direction;
    }

    public void setDirection(Sort.Direction direction) {
        this.direction = direction;
    }
}
