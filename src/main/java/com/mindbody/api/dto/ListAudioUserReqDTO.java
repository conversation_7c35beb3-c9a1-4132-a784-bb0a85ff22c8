package com.mindbody.api.dto;

import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ListAudioUserReqDTO {

    @ValueOfEnum(enumClass = AudioPlaylistType.class, message = "invalid_audio_playlist_type")
    @NotNull(message = "audio_playlist_type_required")
    private String audioPlaylistType;

    @ValueOfEnum(enumClass = ZodiacSignType.class, message = "invalid_zodiac_sign")
    private String zodiacSign;

    @NotNull(message = "audio_playlist_id_required")
    private Long audioPlaylistId;

    @NotNull(message = "user_id_required")
    private Long userId;
}
