package com.mindbody.api.dto;

import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserStatsTimeSpentReqDTO {

    @NotNull(message = "user_id_required")
    private Long userId;

    private Long mindTimeSpent;

    private Long bodyTimeSpent;

    private Long wxpEarned;

    @ValueOfEnum(enumClass = AchievementCategoryType.class, message = "invalid_achievement_category_type")
    @NotNull(message = "achievement_category_type_required_leaderboard")
    private String achievementCategoryType;

}
