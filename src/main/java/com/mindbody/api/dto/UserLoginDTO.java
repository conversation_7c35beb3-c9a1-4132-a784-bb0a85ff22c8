package com.mindbody.api.dto;

import com.mindbody.api.enums.DeviceType;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
public class UserLoginDTO {

    @Email(message = "email_invalid_error", regexp = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}")
    private String email;

    @Pattern(message = "country_code_invalid_error", regexp = "\\+[0-9]{1,4}")
    private String countryCode;

    @Pattern(message = "phone_number_invalid_error", regexp = "[0-9]{10,15}")
    private String phoneNumber;

    @NotBlank(message = "password_blank_check_error")
    @NotNull(message = "password_blank_check_error")
    private String password;

    private String fcmToken;

    private String deviceUniqueId;

    private DeviceType deviceType;


}
