package com.mindbody.api.dto;

import com.mindbody.api.enums.AchievementCategoryType;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class UserBorderDTO {

    private Long achievementId;
    private String borderImage;
    private AchievementCategoryType achievementCategoryType;
    private String borderName;

    public UserBorderDTO(Long achievementId, String borderImage, AchievementCategoryType achievementCategoryType, String borderName){
        this.achievementId = achievementId;
        this.borderImage = borderImage;
        this.achievementCategoryType =achievementCategoryType;
        this.borderName = borderName;
    }

}
