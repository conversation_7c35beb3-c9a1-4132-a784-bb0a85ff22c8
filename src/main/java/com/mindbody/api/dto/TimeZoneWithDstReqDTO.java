package com.mindbody.api.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class TimeZoneWithDstReqDTO {

    @NotNull(message = "latitude_required")
    private double latitude;

    @NotNull(message = "longitude_required")
    private double longitude;

    @NotNull(message = "date_of_birth_required")
    private String date;

    public TimeZoneWithDstReqDTO(double latitude, double longitude, String date) {
        this.latitude = latitude;
        this.longitude = longitude;
        this.date = date;
    }
}

