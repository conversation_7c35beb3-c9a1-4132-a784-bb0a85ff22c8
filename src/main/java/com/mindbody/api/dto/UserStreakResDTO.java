package com.mindbody.api.dto;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class UserStreakResDTO {

    private Long userStreakId;

    private Integer streakCount;

    private Integer totalCount;

    private LocalDate firstStreakDate;

    private LocalDate lastStreakDate;

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
