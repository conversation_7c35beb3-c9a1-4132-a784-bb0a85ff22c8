package com.mindbody.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAchievementByIdDTO {
    private Long previousLevel;
    private Long previousWxp;
    private Long currentLevel;
    private Long currentLevelWxp;
    private Long nextLevel;
    private Long nextLevelWxp;
    private Long currentWxp;
    private Long currentPoint;
    private UserAchievementDTO userAchievementDTO;
}
