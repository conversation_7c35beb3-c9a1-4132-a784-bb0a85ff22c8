package com.mindbody.api.dto;

import com.mindbody.api.enums.DeviceType;
import com.mindbody.api.enums.OtpModuleName;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class VerifyOTPReqDTO {


    @NotBlank(message = "otp_required")
    @NotNull(message = "otp_required")
    private String otp;

    @NotNull(message = "user_id_required")
    private Long userId;

    private OtpModuleName moduleName;

    private String fcmToken;

    private String deviceUniqueId;

    private DeviceType deviceType;

}
