package com.mindbody.api.dto;

import com.mindbody.api.enums.AchievementCategoryType;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class UserIdAchievementIdDTO {

    private Long userId;
    private Long achievementId;

    public UserIdAchievementIdDTO(Long userId, Long achievementId){
        this.userId = userId;
        this.achievementId = achievementId;
    }

}
