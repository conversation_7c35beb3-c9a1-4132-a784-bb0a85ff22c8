package com.mindbody.api.dto;

import com.mindbody.api.enums.AchievementCategoryType;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class UserBadgeDTO {

    private Long achievementId;
    private String badgeImage;
    private AchievementCategoryType achievementCategoryType;

    public UserBadgeDTO(Long achievementId, String badgeImage, AchievementCategoryType achievementCategoryType){
        this.achievementId = achievementId;
        this.badgeImage = badgeImage;
        this.achievementCategoryType =achievementCategoryType;
    }

}
