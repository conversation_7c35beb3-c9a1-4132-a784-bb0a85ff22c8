package com.mindbody.api.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class UserDailyHoroscopeResDTO {
    private boolean status;
    private String sunSign;
    private String predictionDate;
    private Map<String, String> prediction; // Holds personal_life, profession, etc.

    public UserDailyHoroscopeResDTO(boolean status, String sunSign, String predictionDate, Map<String, String> prediction) {
        this.status = status;
        this.sunSign = sunSign;
        this.predictionDate = predictionDate;
        this.prediction = prediction;
    }
}
