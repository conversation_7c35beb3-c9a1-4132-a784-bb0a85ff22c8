package com.mindbody.api.dto.achievement;

import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AchievementCategoryListReqDTO {

    @ValueOfEnum(enumClass = AchievementCategoryType.class, message = "invalid_achievement_category_type")
    @NotNull(message = "achievement_category_type_required")
    private String achievementCategoryType;

    @NotNull(message = "user_id_required")
    private Long userId;
}
