package com.mindbody.api.dto.achievement;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AchievementSetResponse {
    @JsonIgnore
    private String audioPlaylistName;
    @JsonIgnore
    private String workoutPlaylistName;
    @JsonIgnore
    private String warriorSetName;
    private Integer totalMedals = 0;
    private Integer userCompletedMedals = 0;
    private String setName;
    private Long setId;
    private Boolean isNewAvailable;
}
