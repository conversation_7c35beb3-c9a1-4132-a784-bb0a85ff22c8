package com.mindbody.api.dto.achievement;

import com.mindbody.api.enums.AchievementCategoryType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AchievementCountForCategoryResDTO {
    private AchievementCategoryType achievementCategoryType;
    private Integer totalAchievementsCount=0;
    private Integer userCompletedAchievementsCount=0;
    private boolean isNewAvailable;

    public AchievementCountForCategoryResDTO(AchievementCategoryType achievementCategoryType, Integer totalAchievementsCount, Integer userCompletedAchievementsCount, boolean isNewAvailable) {
        this.achievementCategoryType = achievementCategoryType;
        this.totalAchievementsCount = totalAchievementsCount;
        this.userCompletedAchievementsCount = userCompletedAchievementsCount;
        this.isNewAvailable = isNewAvailable;
    }
}
