package com.mindbody.api.dto.achievement;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class AchievementSubCategoryResponse {
    @JsonIgnore
    private String mindSubCategoryType;
    @JsonIgnore
    private String bodySubCategoryType;
    @JsonIgnore
    private String warriorSubCategoryType;
    private String subCategoryType;
    private List<AchievementSetResponse> achievementSetResponseList;

}
