package com.mindbody.api.dto;

import com.mindbody.api.enums.ZodiacSignType;
import lombok.Builder;
import lombok.Data;

/**
 * DTO for pinned users in the "Me" section.
 */
@Data
@Builder
public class PinnedUserDTO {

    /**
     * The ID of the pinned user.
     */
    private Long userId;

    /**
     * The name of the pinned user.
     */
    private String name;

    /**
     * The zodiac sign of the pinned user.
     */
    private ZodiacSignType zodiacSign;

    /**
     * The current level of the pinned user.
     */
    private Long currentLevel;

    /**
     * The Google account name of the pinned user.
     */
    private String googleAccountName;

    /**
     * The Facebook account name of the pinned user.
     */
    private String facebookAccountName;

    /**
     * The Apple account name of the pinned user.
     */
    private String appleAccountName;

    /**
     * The title of the pinned user.
     */
    private String title;

    /**
     * The badge image of the pinned user.
     */
    private String badgeImage;

    private boolean isSubscribed;

}
