package com.mindbody.api.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UserAstrologyReqDTO {

    @NotNull(message = "day_required")
    private int day;

    @NotNull(message = "month_required")
    private int month;

    @NotNull(message = "year_required")
    private int year;

    @NotNull(message = "hour_required")
    private int hour;

    @NotNull(message = "minute_required")
    private int min;

    @NotNull(message = "latitude_required")
    private double latitude;

    @NotNull(message = "longitude_required")
    private double longitude;

    @NotNull(message = "time_zone_required")
    private int timezone;

    @NotNull(message = "house_type_required")
    private String houseType;


    public UserAstrologyReqDTO(int day, int month, int year, int hour, int min, double latitude, double longitude, int timezone, String houseType) {
        this.day = day;
        this.month = month;
        this.year = year;
        this.hour = hour;
        this.min = min;
        this.latitude = latitude;
        this.longitude = longitude;
        this.timezone = timezone;
        this.houseType = houseType;
    }
}
