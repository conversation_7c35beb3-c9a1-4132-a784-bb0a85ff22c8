package com.mindbody.api.dto;

import com.mindbody.api.enums.GenderType;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class EditProfileReqDTO {


    @NotNull(message = "user_id_required")
    private Long userId;

    private String name;

    private LocalDateTime dateOfBirth;

    private String placeOfBirth;

    @NotNull(message = "latitude_required")
    private double latitude;

    @NotNull(message = "longitude_required")
    private double longitude;

    private GenderType genderType;

    @Email(message = "email_invalid_error", regexp = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}")
    private String email;

    @Pattern(message = "country_code_invalid_error", regexp = "\\+[0-9]{1,4}")
    private String countryCode;

    @Pattern(message = "phone_number_invalid_error", regexp = "[0-9]{10,15}")
    private String phoneNumber;

//    private MultipartFile profileImage;
}
