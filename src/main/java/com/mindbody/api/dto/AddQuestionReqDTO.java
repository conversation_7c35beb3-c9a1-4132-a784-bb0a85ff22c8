package com.mindbody.api.dto;


import com.mindbody.api.dto.cms.AddOptionReqDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class AddQuestionReqDTO {

    @NotBlank(message = "question_name_required")
    @NotNull(message = "question_name_required")
    private String questionName;

    @NotBlank(message = "question_title_required")
    @NotNull(message = "question_title_required")
    private String questionTitle;

    @NotNull(message = "at_least_one_option_required_error")
    private List<@Valid AddOptionReqDTO> optionList;
}
