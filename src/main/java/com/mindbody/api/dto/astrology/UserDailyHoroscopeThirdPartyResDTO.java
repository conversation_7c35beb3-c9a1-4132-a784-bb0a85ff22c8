package com.mindbody.api.dto.astrology;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UserDailyHoroscopeThirdPartyResDTO {
    private boolean status;
    private String sun_sign;        // Matches "sun_sign"
    private String prediction_date; // Matches "prediction_date"

    private Prediction prediction;

    @Getter
    @Setter
    @NoArgsConstructor
    public static class Prediction {
        private String personal_life;
        private String profession;
        private String health;
        private String emotions;
        private String travel;
        private String luck;
    }
}
