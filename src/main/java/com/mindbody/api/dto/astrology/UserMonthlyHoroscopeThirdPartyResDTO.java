package com.mindbody.api.dto.astrology;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class UserMonthlyHoroscopeThirdPartyResDTO {
    private boolean status;
    private String sun_sign;
    private String prediction_month;
    private List<String> prediction;

    public UserMonthlyHoroscopeThirdPartyResDTO(boolean status, String sun_sign, String prediction_month, List<String> prediction) {
        this.status = status;
        this.sun_sign = sun_sign;
        this.prediction_month = prediction_month;
        this.prediction = prediction;
    }
}
