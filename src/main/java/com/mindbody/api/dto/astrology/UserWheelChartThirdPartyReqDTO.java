package com.mindbody.api.dto.astrology;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UserWheelChartThirdPartyReqDTO {

    private int day;

    private int month;

    private int year;

    private int hour;

    private int min;

    private double lat;

    private double lon;

    private int tzone;

    private String house_type;

    private String inner_circle_background;

    private String planet_icon_color;

    private String sign_icon_color;

    private String sign_background;

    private String chart_size;

    private String image_type;

}
