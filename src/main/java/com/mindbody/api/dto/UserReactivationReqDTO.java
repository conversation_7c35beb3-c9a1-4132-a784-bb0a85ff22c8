//package com.mindbody.api.dto;
//
//import jakarta.validation.constraints.NotNull;
//import lombok.Getter;
//import lombok.NoArgsConstructor;
//import lombok.Setter;
//
//@Getter
//@Setter
//@NoArgsConstructor
//public class UserReactivationReqDTO {
//
//    @NotNull(message = "reactivation_request_pending_required_error")
//    private boolean reactivationRequestPending;
//
//    @NotNull(message = "user_id_required")
//    private Long userId;
//
//}
