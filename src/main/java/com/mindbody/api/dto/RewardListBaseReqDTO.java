package com.mindbody.api.dto;

import com.mindbody.api.filter.Page;
import com.mindbody.api.filter.SortBy;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class RewardListBaseReqDTO extends CommonListDTO {

    @NotNull(message = "user_id_required")
    private Long userId;

    @Valid
    private Page page;

    private SortBy sortBy;

    private String queryToSearch;
} 