package com.mindbody.api.dto.notification;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
public class NotificationFilterDTO {
    private Boolean active;

    private String genderFilter;

    private String zodiacSignFilter;

    private Integer startAge;

    private Integer endAge;

    @JsonProperty(value = "active")
    public Boolean isActive() {
        return active;
    }


}
