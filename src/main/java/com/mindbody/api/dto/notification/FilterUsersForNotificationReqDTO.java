package com.mindbody.api.dto.notification;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mindbody.api.filter.CommonSearchPagination;
import com.mindbody.api.filter.Page;
import com.mindbody.api.filter.PageFilter;
import com.mindbody.api.filter.SortBy;
import com.mindbody.api.util.StringUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;


@Getter
@Setter
public class FilterUsersForNotificationReqDTO implements CommonSearchPagination {


    @JsonProperty("query")
    private String queryToSearch;

    @Getter
    private NotificationFilterDTO filters;

    private Page page;

    @Getter
    private SortBy sortBy;

    @Override
    public PageFilter getPage() {
        if (Objects.isNull(this.page)) {
            return new Page();
        }
        return page;
    }

    @Override
    public String getQueryToSearch() {
        if (queryToSearch == null) return "";
        return StringUtil.escapeData(queryToSearch.trim());
    }

}
