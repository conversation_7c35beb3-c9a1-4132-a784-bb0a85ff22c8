package com.mindbody.api.dto.notification;

import com.mindbody.api.enums.Category;
import com.mindbody.api.enums.NotificationType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
public class SendNotificationToUsersReqDTO {

    private Long[] userIds;

//    private String topicName;

    private String[] topicNames;

    @NotBlank(message = "title_required")
    private String title;

    @NotBlank(message = "message_required")
    private String message;

    @NotNull(message = "notification_type_required")
    @ValueOfEnum(enumClass = NotificationType.class, message = "invalid_notification_type")
    private String notificationType;

    private boolean isScheduled;

    private LocalDateTime scheduledTime;

    @NotNull(message = "category_required")
    @ValueOfEnum(enumClass = Category.class, message = "invalid_category_type")
    private String category;


}
