package com.mindbody.api.dto.notification;

import com.mindbody.api.enums.NotificationType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class NotificationDetailResDTO {
    private Long notificationId;
    private String title;
    private String message;
    private NotificationType notificationType;
    private String image;
    private LocalDateTime createdAt;

    public NotificationDetailResDTO(Long notificationId, String title, String message, NotificationType notificationType,String image,LocalDateTime createdAt) {
        this.notificationId = notificationId;
        this.title = title;
        this.message = message;
        this.notificationType = notificationType;
        this.image = image;
        this.createdAt = createdAt;
    }
}




