package com.mindbody.api.dto.notification;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class NotificationDTO {
    private String title;

    private String message;

    private String notificationType;

//    private String topicName;

    private String[] topicNames;

    private String imageUrl;

    private String imageKey;

    private Map<String, String> data = new HashMap<>();

    private String aiChatCategory;


}
