package com.mindbody.api.dto.notification;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SubscribeTopicReqDTO {
    @NotNull(message = "user_id_required")
    private Long userId;

    @NotEmpty(message = "topic_names_required")
    private List<String> topicNames;

}
