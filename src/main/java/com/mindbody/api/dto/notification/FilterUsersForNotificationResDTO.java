package com.mindbody.api.dto.notification;

import com.mindbody.api.enums.GenderType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class FilterUsersForNotificationResDTO {
    private Long userId;

    private String email;

    private String name;

    private GenderType genderType;

    private String zodiacSignType;

    private Integer age;

    private LocalDateTime createdAt;

}
