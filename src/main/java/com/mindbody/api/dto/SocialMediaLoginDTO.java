package com.mindbody.api.dto;

import com.mindbody.api.enums.DeviceType;
import com.mindbody.api.enums.RegisterType;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class SocialMediaLoginDTO {

    private Long userId;

    @NotBlank(message = "email_blank_check_error")
    @NotNull(message = "email_blank_check_error")
    @Email(message = "email_invalid_error", regexp = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}")
    private String email;

    @NotNull(message = "name_required")
    @NotBlank(message = "name_required")
    private String name;

    private String fcmToken;

    private String deviceUniqueId;

    private DeviceType deviceType;

    private RegisterType registerType;

    @NotBlank(message = "social_media_id_not_found_error")
    @NotNull(message = "social_media_id_not_found_error")
    private String socialMediaId;

}
