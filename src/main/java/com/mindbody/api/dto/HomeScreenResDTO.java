package com.mindbody.api.dto;


import com.mindbody.api.dto.cms.DailyTipsResSTO;
import com.mindbody.api.dto.cms.MagazineDetailResDTO;
import com.mindbody.api.enums.AchievementActivityType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class HomeScreenResDTO {

    private List<DailyTipsResSTO> dailyTipsResSTOList;

    private List<MagazineDetailResDTO> magazineDetailResDTOList;

    private List<UserWorkoutPlaylistResDTO> workoutPlaylistResDTOList;

    private UserAstrologyDetailResDTO userAstrologyDetailResDTO;

    private List<AchievementActivityType> achievementActivityType;

    private long unreadNotificationCount=0L;

}
