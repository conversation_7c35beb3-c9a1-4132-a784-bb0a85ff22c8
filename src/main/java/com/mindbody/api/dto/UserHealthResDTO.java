package com.mindbody.api.dto;

import com.mindbody.api.enums.ActivityFactorType;
import com.mindbody.api.enums.GenderType;
import com.mindbody.api.enums.MeasurementUnitType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UserHealthResDTO {

    private Long userHealthInfoId;

    private double bmi;

    private double bmr;

    private double protein;

    private double calories;

    private double carbs;

    private double fat;

    private double weight;

    private double convertedWeight;
    
    private MeasurementUnitType weightUnit;

    private double height;
    
    private double convertedHeight;
    
    private MeasurementUnitType heightUnit;

    private int age;

    private GenderType gender;

    private ActivityFactorType activityFactorType;
}
