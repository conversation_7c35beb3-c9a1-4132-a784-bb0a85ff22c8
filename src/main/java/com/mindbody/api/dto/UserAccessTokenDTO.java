package com.mindbody.api.dto;

import com.mindbody.api.enums.AccountStatus;
import com.mindbody.api.enums.RoleType;
import com.mindbody.api.enums.UserType;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class UserAccessTokenDTO  {

    private Long userId;

    private String email;

    private String countryCode;

    private String phoneNumber;

    private RoleType roleType;

    private UserType userType;

    private AccountStatus accountStatus;

    private String accessToken;

    private String secretToken;

    private boolean isDeleted;

    private boolean isActive ;

    public UserAccessTokenDTO(Long userId, String email, String countryCode, String phoneNumber, RoleType roleType, UserType userType, AccountStatus accountStatus, String accessToken,String secretToken, boolean isDeleted, boolean isActive) {
        this.userId = userId;
        this.email = email;
        this.countryCode = countryCode;
        this.phoneNumber = phoneNumber;
        this.roleType = roleType;
        this.userType = userType;
        this.accountStatus = accountStatus;
        this.accessToken = accessToken;
        this.secretToken=secretToken;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
    }
}
