package com.mindbody.api.dto;

import com.mindbody.api.enums.FavoriteModuleType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ReferralListReqDTO extends CommonListDTO {

    @NotNull(message = "user_id_required")
    private Long userId;

}
