package com.mindbody.api.dto;

import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.enums.RewardType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RedeemItemResDTO {
    // Basic redemption information
    private String itemTitle;
    private Integer remainingPoints;
    private String itemType;
    private String couponCode; // Only for store discount rewards
    private String rewardMetadata;
    private List<Map<String, String>> zodiacWallpapers; // List of zodiac wallpapers with their signs

    // Additional reward information
    private Long rewardId;
    private String description;
    private String thumbnailImage;
    private Integer pointsSpent;
    private RewardType rewardType;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Audio specific fields
    private Long audioId;
    private AudioPlaylistType audioPlaylistType;
    private String audioFile;
    private Long audioPlaylistId;
    private String audioPlaylistName;
    private List<String> zodiacSigns;

    // Constructor for backward compatibility
    public RedeemItemResDTO(String itemTitle, Integer remainingPoints, String itemType, String couponCode, String rewardMetadata, List<Map<String, String>> zodiacWallpapers) {
        this.itemTitle = itemTitle;
        this.remainingPoints = remainingPoints;
        this.itemType = itemType;
        this.couponCode = couponCode;
        this.rewardMetadata = rewardMetadata;
        this.zodiacWallpapers = zodiacWallpapers;
    }
} 