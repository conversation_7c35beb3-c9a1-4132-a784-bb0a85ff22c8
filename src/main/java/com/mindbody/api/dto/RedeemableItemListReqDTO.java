package com.mindbody.api.dto;

import com.mindbody.api.filter.Page;
import com.mindbody.api.filter.SortBy;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class RedeemableItemListReqDTO {
    @NotNull(message = "user_id_required")
    private Long userId;

    @Pattern(regexp = "^(AUDIO|REWARD)?$", message = "invalid_item_type")
    private String itemType; // Optional - if null, return both types

    @Valid
    private Page page;

    private SortBy sortBy;

    private String queryToSearch;
} 