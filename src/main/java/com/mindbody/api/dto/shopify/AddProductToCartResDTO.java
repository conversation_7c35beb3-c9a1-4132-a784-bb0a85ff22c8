package com.mindbody.api.dto.shopify;

import lombok.Data;

import java.util.List;

@Data
public class AddProductToCartResDTO {

    private CartLinesAddData data;

    @Data
    public static class CartLinesAddData {
        private CartLinesAdd cartLinesAdd;
    }

    @Data
    public static class CartLinesAdd {
        private Cart cart;
        private List<UserError> userErrors;
    }

    @Data
    public static class Cart {
        private String id;
        private Lines lines;
        private CostDTO cost;
    }

    @Data
    public static class Lines {
        private List<Edge> edges;
    }

    @Data
    public static class Edge {
        private Node node;
    }

    @Data
    public static class Node {
        private Integer quantity;
        private Merchandise merchandise;
    }

    @Data
    public static class Merchandise {
        private String id;
    }

}
