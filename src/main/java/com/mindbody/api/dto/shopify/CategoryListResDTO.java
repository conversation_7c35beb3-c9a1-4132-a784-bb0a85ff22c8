package com.mindbody.api.dto.shopify;

import lombok.Data;

import java.util.List;

@Data
public class CategoryListResDTO {

    private CategoryData data;

    @Data
    public static class CategoryData {
        private Collections collections;
    }

    @Data
    public static class Collections {
        private List<Edge> edges;
        private PageInfo pageInfo;
    }

    @Data
    public static class Edge {
        private Node node;
    }

    @Data
    public static class Node {
        private String id;
        private String title;
        private String descriptionHtml;
        private String handle;
        private Image image;
    }

    @Data
    public static class Image {
        private String src;
        private String altText;
    }


}
