package com.mindbody.api.dto.shopify;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class AddProductToCartReqDTO {

    @NotNull(message = "user_id_required")
    private Long userId;

    @NotNull(message = "cartId_required")
    @NotBlank(message = "cartId_required")
    private String cartId;

    @NotNull(message = "line_items_required")
    @NotEmpty(message = "line_items_required")
    private List<@Valid CartLine> lines;

    @Data
    public static class CartLine {

        @NotNull(message = "merchandiseId_required")
        @NotBlank(message = "merchandiseId_required")
        private String merchandiseId;

        @NotNull(message = "quantity_required")
        private int quantity;
    }
}



