package com.mindbody.api.dto.shopify;

import lombok.Data;

import java.util.List;

@Data
public class OrdersListGraphqlResDTO {
    private OrderData data;

    @Data
    public static class OrderData {
        private OrdersConnection orders;
    }

    @Data
    public static class OrdersConnection {
        private List<OrderEdge> edges;
        private PageInfo pageInfo;
    }

    @Data
    public static class OrderEdge {
        private OrderNode node;
    }

    @Data
    public static class OrderNode {
        private String id; // The ID of the order
        private String name;
        private String displayFinancialStatus;
        private String displayFulfillmentStatus;
        private boolean confirmed;
        private boolean closed;
        private String closedAt;
        private String createdAt;
        private String updatedAt;
        private List<Fulfillments> fulfillments;
        private LineItemsConnection lineItems;
        private ShippingLine shippingLine;
        private TotalPriceSet totalPriceSet;
    }

    @Data
    public static class LineItemsConnection {
        private List<LineItemEdge> edges;
        private PageInfo pageInfo;
    }

    @Data
    public static class LineItemEdge {
        private LineItemNode node;
    }

    @Data
    public static class LineItemNode {
        private String id;
        private String name;
        private String title;
        private Integer quantity;
        private String vendor;
        private VariantNode variant;
        private ImageNode image;
        private Product product;
    }

    @Data
    public static class ImageNode {
        private String id;
        private String url;
        private String altText;
        private int width;
        private int height;
    }


    @Data
    public static class VariantNode {
        private String id;
        private String title;
        private String sku;
        private String price;
    }

    @Data
    public static class ShippingLine {
        private String carrierIdentifier; // A reference to the carrier service
        private String code; // A reference to the shipping method
        private CurrentDiscountedPriceSet currentDiscountedPriceSet; // Current shipping price after discounts
    }

    @Data
    public static class CurrentDiscountedPriceSet{
        private MoneyV2 presentmentMoney; // Amount in presentment currency
        private MoneyV2 shopMoney;
    }

    @Data
    public static class TotalPriceSet {
        private MoneyV2 presentmentMoney; // Amount in presentment currency
        private MoneyV2 shopMoney; // Amount in shop currency
    }

    @Data
    public static class MoneyV2 {
        private String amount; // Decimal money amount
        private String currencyCode; // Currency of the money
    }

    @Data
    public static class Product{
        private String description;
        private String productType;
    }

    @Data
    public static class Fulfillments{
        private String createdAt;
        private String deliveredAt;
        private String displayStatus;
        private String estimatedDeliveryAt;
    }



}
