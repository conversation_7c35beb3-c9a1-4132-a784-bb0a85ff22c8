package com.mindbody.api.dto.shopify;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class CreateCartWithFirstProductReqDTO {

    @NotNull(message = "user_id_required")
    private Long userId;

    private CartInputDTO cartInput;

    @Data
    public static class CartInputDTO {

        @NotNull(message = "line_items_required")
        @NotEmpty(message = "line_items_required")
        private List<@Valid LineDTO> lines;

    }

    @Data
    public static class LineDTO {

        @NotNull(message = "quantity_required")
        private Integer quantity;

        @NotNull(message = "merchandiseId_required")
        @NotBlank(message = "merchandiseId_required")
        private String merchandiseId;
    }

}