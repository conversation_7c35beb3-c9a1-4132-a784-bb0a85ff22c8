package com.mindbody.api.dto.shopify;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProductByIdReqDTO {

    @NotNull(message = "product_id_required")
    @NotEmpty(message = "product_id_required")
    private String productId;
}
