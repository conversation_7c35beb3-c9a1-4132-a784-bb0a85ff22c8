package com.mindbody.api.dto.shopify;

import lombok.Data;

import java.util.List;

@Data
public class RemoveProductFromCartResDTO {

    private DataDTO data;

    @Data
    public static class DataDTO {
        private CartLinesRemoveDTO cartLinesRemove;
    }

    @Data
    public static class CartLinesRemoveDTO {
        private CartDTO cart;
        private List<UserError> userErrors;
    }

    @Data
    public static class CartDTO {
        private String id;
        private LinesDTO lines;
        private CostDTO cost;
    }

    @Data
    public static class LinesDTO {
        private List<EdgeDTO> edges;
    }

    @Data
    public static class EdgeDTO {
        private NodeDTO node;
    }

    @Data
    public static class NodeDTO {
        private Integer quantity;
        private MerchandiseDTO merchandise;
    }

    @Data
    public static class MerchandiseDTO {
        private String id;
    }


}
