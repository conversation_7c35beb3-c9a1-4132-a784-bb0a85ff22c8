package com.mindbody.api.dto.shopify;

import lombok.Data;

import java.util.List;

@Data
public class ProductResDTO {

    private DataDTO data;

    @Data
    public static class DataDTO {
        private ProductDTO product;
    }

    @Data
    public static class ProductDTO {
        private String id;
        private String title;
        private String descriptionHtml;
        private String vendor;
        private String productType;
        private String handle;
        private List<String> tags;
        private String createdAt;
        private String updatedAt;
        private String publishedAt;
        private ImagesDTO images;
        private VariantsDTO variants;
    }

    @Data
    public static class ImagesDTO {
        private List<ImageEdgeDTO> edges;
    }

    @Data
    public static class ImageEdgeDTO {
        private ImageNodeDTO node;
    }

    @Data
    public static class ImageNodeDTO {
        private String id;
        private String src;
        private String altText;
        private int width;
        private int height;
    }

    @Data
    public static class VariantsDTO {
        private List<VariantEdgeDTO> edges;
    }

    @Data
    public static class VariantEdgeDTO {
        private VariantNodeDTO node;
    }

    @Data
    public static class VariantNodeDTO {
        private String id;
        private String title;
        private String sku;
        private PriceV2DTO priceV2;
        private List<SelectedOptionDTO> selectedOptions;
    }

    @Data
    public static class PriceV2DTO {
        private String amount;
        private String currencyCode;
    }

    @Data
    public static class SelectedOptionDTO {
        private String name;
        private String value;
    }
}

