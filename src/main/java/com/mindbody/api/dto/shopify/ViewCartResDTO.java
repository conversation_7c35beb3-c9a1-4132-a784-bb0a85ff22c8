package com.mindbody.api.dto.shopify;

import lombok.Data;

import java.util.List;

@Data
public class ViewCartResDTO {

    private DataField data;

    @Data
    public static class DataField {
        private Cart cart;
        private List<UserError> userErrors;
    }

    @Data
    public static class Cart {
        private String id;
        private String createdAt;
        private String updatedAt;
        private String checkoutUrl;
        private Lines lines;
        private CostDTO cost;
        private BuyerIdentity buyerIdentity;
    }

    @Data
    public static class Lines {
        private List<Edge> edges;

        @Data
        public static class Edge {
            private Node node;

            @Data
            public static class Node {
                private String id;
                private int quantity;
                private Merchandise merchandise;
            }
        }
    }

    @Data
    public static class Merchandise {
        private String id;
        private ProductListResDTO.ProductNode product;
    }

    @Data
    public static class BuyerIdentity {
        private String email;
        private String phone;
        private Customer customer;
        private String countryCode;

        @Data
        public static class Customer {
            private String id;
        }
    }

}
