package com.mindbody.api.dto.shopify;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class TrackOrderResDTO {
    private List<Fulfillment> fulfillments;

    @Getter
    @Setter
    public static class Fulfillment {
        private long id;
        private long orderId;
        private String status;
        private String createdAt;
        private String service;
        private String updatedAt;
        private String trackingCompany;
        private String shipmentStatus;
//        private String originAddress;
        private List<LineItem> lineItems;
        private String trackingNumber;
        private List<String> trackingNumbers;
        private String trackingUrl;
        private List<String> trackingUrls;
        private Receipt receipt;
        private String name;
        private String adminGraphqlApiId;
    }

    @Getter
    @Setter
    public static class LineItem {
        private long id;
        private long variantId;
        private String title;
        private int quantity;
        private String sku;
        private String variantTitle;
        private String vendor;
        private String fulfillmentService;
        private long productId;
        private boolean requiresShipping;
        private boolean taxable;
        private boolean giftCard;
        private String name;
        private String variantInventoryManagement;
        private List<Object> properties;
        private boolean productExists;
        private int fulfillableQuantity;
        private int grams;
        private String price;
        private String totalDiscount;
        private String fulfillmentStatus;
        private PriceSet priceSet;
        private PriceSet totalDiscountSet;
        private List<Object> discountAllocations;
        private OriginLocation originLocation;
        private String adminGraphqlApiId;
        private List<Object> duties;
        private List<Object> taxLines;
        private long fulfillmentLineItemId;
    }

    @Getter
    @Setter
    public static class PriceSet {
        private Money shopMoney;
        private Money presentmentMoney;
    }

    @Getter
    @Setter
    public static class Money {
        private String amount;
        private String currencyCode;
    }

    @Getter
    @Setter
    public static class OriginLocation {
        private long id;
        private String countryCode;
        private String provinceCode;
        private String name;
        private String address1;
        private String address2;
        private String city;
        private String zip;
    }

    @Getter
    @Setter
    public static class Receipt {
        // Define fields if needed based on actual use
    }
}
