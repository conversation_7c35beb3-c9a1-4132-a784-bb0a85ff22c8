package com.mindbody.api.dto.shopify;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ViewCartReqDTO {

    @NotNull(message = "user_id_required")
    private Long userId;

    @NotNull(message = "cartId_required")
    @NotBlank(message = "cartId_required")
    private String cartId;
}
