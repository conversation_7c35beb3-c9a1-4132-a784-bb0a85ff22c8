package com.mindbody.api.dto.shopify;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class RemoveProductFromCartReqDTO {

    @NotNull(message = "user_id_required")
    private Long userId;

    @NotNull(message = "cartId_required")
    @NotBlank(message = "cartId_required")
    private String cartId;

    @NotNull(message = "line_id_required")
    @NotEmpty(message = "line_id_required")
    private List<String> linesId;
}
