package com.mindbody.api.dto.shopify;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class UpdateBuyerIdentityReqDTO {

    @NotNull(message = "user_id_required")
    private Long userId;

    @NotNull(message = "cartId_required")
    @NotBlank(message = "cartId_required")
    private String cartId;
    private BuyerIdentity buyerIdentity;

    @Data
    public static class BuyerIdentity {
        private String companyLocationId;
        private String countryCode;
//        private String customerAccessToken;
        private Customer customer;
        private List<DeliveryAddressPreferences> deliveryAddressPreferences;
        private String email;
        private String phone;
//        private Preferences preferences;

    }

    @Data
    public static class Customer {
        private String id;
        private String displayName;
        private DefaultAddress defaultAddress;

    }

    @Data
    public static class DeliveryAddressPreferences {
        private String customerAddressId;
        private DeliveryAddress deliveryAddress;
        private String deliveryAddressValidationStrategy;
        private boolean oneTimeUse;

    }

    @Data
    public static class DeliveryAddress {
        private String address1;
        private String address2;
        private String city;
        private String company;
        private String country;
        private String firstName;
        private String lastName;
        private String phone;
        private String zip;

    }

    @Data
    public static class DefaultAddress {
        private String address1;
        private String address2;
        private String city;
        private String company;
        private String country;
        private String firstName;
        private String lastName;
        private String phone;
        private String zip;

    }




}
