package com.mindbody.api.dto.shopify;


import lombok.Data;

import java.util.List;

@Data
public class UpdateCartResDTO {

    private DataDTO data;

    @Data
    public static class DataDTO {
        private CartLinesUpdateDTO cartLinesUpdate;
    }

    @Data
    public static class CartLinesUpdateDTO {
        private CartDTO cart;
        private List<UserError> userErrors;
    }

    @Data
    public static class CartDTO {
        private String id;
        private LinesDTO lines;
        private CostDTO cost;
    }

    @Data
    public static class LinesDTO {
        private List<EdgeDTO> edges;
    }

    @Data
    public static class EdgeDTO {
        private NodeDTO node;
    }

    @Data
    public static class NodeDTO {
        private String id;
        private Integer quantity;
        private MerchandiseDTO merchandise;
    }

    @Data
    public static class MerchandiseDTO {
        private String id;
    }


}

