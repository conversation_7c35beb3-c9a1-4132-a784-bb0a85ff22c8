package com.mindbody.api.dto.shopify;

import lombok.Data;

import java.util.List;

@Data
public class ProductListResDTO {

    private ProductData data;

    @Data
    public static class ProductData {
        private Products products;
    }

    @Data
    public static class Products {
        private List<ProductEdge> edges;
        private PageInfo pageInfo;
    }

    @Data
    public static class ProductEdge {
        private ProductNode node;
    }

    @Data
    public static class ProductNode {
        private String id;
        private String title;
        private String descriptionHtml;
        private String vendor;
        private String productType;
        private String handle;
        private List<String> tags;
        private String createdAt;
        private String updatedAt;
        private String publishedAt;
        private Images images;
        private Variants variants;
    }

    @Data
    public static class Images {
        private List<ImageEdge> edges;
    }

    @Data
    public static class ImageEdge {
        private ImageNode node;
    }

    @Data
    public static class ImageNode {
        private String id;
        private String src;
        private String altText;
        private int width;
        private int height;
    }

    @Data
    public static class Variants {
        private List<VariantEdge> edges;
    }

    @Data
    public static class VariantEdge {
        private VariantNode node;
    }

    @Data
    public static class VariantNode {
        private String id;
        private String title;
        private String sku;
        private PriceV2 priceV2;
        private List<SelectedOption> selectedOptions;
    }

    @Data
    public static class PriceV2 {
        private String amount;
        private String currencyCode;
    }

    @Data
    public static class SelectedOption {
        private String name;
        private String value;
    }



}
