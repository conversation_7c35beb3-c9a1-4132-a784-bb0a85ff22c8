package com.mindbody.api.dto.shopify;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class OrderListResDTO {
    private List<OrderDTO> orders;


    @Data
    public static class OrderDTO {
        private String id;
        private String adminGraphqlApiId;
        private String name;
        private String financialStatus = "";
        private String fulfillmentStatus = "";
        private String currency;
        private double totalPrice = 0.0;
        private String orderNumber;
        private String createdAt;
        private String updatedAt;
        private AddressDTO billingAddress;
        private AddressDTO shippingAddress;
        private double totalDiscounts = 0.0;
        private List<LineItemDTO> lineItems = new ArrayList<>();
        private CustomerDTO customer;
        private List<FulfillmentDTO> fulfillments = new ArrayList<>();
        private boolean confirmed;
        private List<ShippingLinesDTO> shippingLines = new ArrayList<>();

    }


    @Data
    public static class MoneyDTO {
        private double amount;
        private String currencyCode;
    }

    @Data
    public static class TaxLineDTO {
        private double price;
        private String title;
    }

    @Data
    public static class AddressDTO {
        private String province;
        private String country;
        private String countryCode;
        private String provinceCode;
    }

    @Data
    public static class CustomerDTO {
        private String id;
        private String email;
        private boolean verifiedEmail;
        private String currency;
        private AddressDTO defaultAddress;
    }

    @Data
    public static class FulfillmentDTO {
        private String id;
        private String status;
        private String trackingCompany;
        private String trackingNumber;
        private List<LineItemDTO> lineItems;
    }

    @Data
    public static class LineItemDTO {
        private String id;
        private String productId;
        private String title;
        private String name;
        private double price = 0.0;
        private double totalDiscount = 0.0;
        private int quantity;
        private boolean taxable;
        private String variantId;
        private String vendor;
        private List<String> images=new ArrayList<>();
    }

    @Data
    public static class ShippingLinesDTO {
        private double price = 0.0;
        private double shopMoneyAmount;
        private String shopMoneyCurrency;
        private double presentmentMoneyAmount;
        private String presentmentMoneyCurrency;

    }




}