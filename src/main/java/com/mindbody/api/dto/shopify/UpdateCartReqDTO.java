package com.mindbody.api.dto.shopify;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class UpdateCartReqDTO {

    @NotNull(message = "user_id_required")
    private Long userId;

    @NotNull(message = "cartId_required")
    @NotBlank(message = "cartId_required")
    private String cartId;

    @NotNull(message = "line_items_required")
    @NotEmpty(message = "line_items_required")
    private List<CartLine> lines;


    @Data
    public static class CartLine {

        @NotNull(message = "line_items_id_required")
        @NotEmpty(message = "line_items_id_required")
        private String id;

        @NotNull(message = "quantity_required")
        private int quantity;

    }
}
