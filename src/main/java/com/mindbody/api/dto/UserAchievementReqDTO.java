package com.mindbody.api.dto;

import com.mindbody.api.enums.AchievementActivityType;
import com.mindbody.api.enums.FileUploadModuleName;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class UserAchievementReqDTO {

    @NotNull(message = "achievement_activity_required_error")
    private AchievementActivityType achievementActivityType;

    private Long userId;

    public UserAchievementReqDTO(AchievementActivityType achievementActivityType, Long userId) {
        this.achievementActivityType = achievementActivityType;
        this.userId = userId;
    }
}
