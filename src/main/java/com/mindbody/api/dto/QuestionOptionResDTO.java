package com.mindbody.api.dto;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class QuestionOptionResDTO {

    private Long questionId;

    private String questionName;

    private String questionTitle;

    private Integer orderNo;

    private boolean isActive;

    private boolean isDeleted;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private List<OptionResDTO> optionList = new ArrayList<>();


    public QuestionOptionResDTO(Long questionId, String questionName,String questionTitle,Integer orderNo, boolean isActive, boolean isDeleted, LocalDateTime createdAt, LocalDateTime updatedAt,Long optionId, String optionName, String optionImage) {
        this.questionId = questionId;
        this.questionName = questionName;
        this.questionTitle = questionTitle;
        this.orderNo = orderNo;
        this.isActive = isActive;
        this.isDeleted = isDeleted;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        if (optionId != null) {
            this.optionList.add(new OptionResDTO(optionId, optionName, optionImage));
        }
    }
    public QuestionOptionResDTO(Long questionId, String questionName,Integer orderNo, boolean isActive, boolean isDeleted, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.questionId = questionId;
        this.questionName = questionName;
        this.questionTitle = questionTitle;
        this.orderNo = orderNo;
        this.isActive = isActive;
        this.isDeleted = isDeleted;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
}
