package com.mindbody.api.dto;

import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.enums.UserType;
import com.mindbody.api.enums.ZodiacSignType;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * DTO for the "Me" section of the leaderboard.
 */
@Data
@Builder
public class MeSectionResponseDTO {

    /**
     * The ID of the current user.
     */
    private Long userId;

    /**
     * The name of the current user.
     */
    private String name;

    /**
     * The zodiac sign of the current user.
     */
    private ZodiacSignType zodiacSign;

    /**
     * The current level of the user.
     */
    private Long currentLevel;

    /**
     * The title of the user.
     */
    private String title;

    /**
     * The border image URL for the user.
     */
    private String borderImage;

    /**
     * The badge image URL for the user.
     */
    private String badgeImage;

    /**
     * Indicates whether the user is subscribed.
     */
    private boolean isSubscribed;

    /**
     * The most achieved category for the user.
     */
    private AchievementCategoryType mostAchievedCategory;

    /**
     * The total number of achievements for the user.
     */
    private Integer totalAchievements;

    /**
     * The title of the most recent achievement.
     */
    private String recentAchievementTitle;

    /**
     * The badge image of the most recent achievement.
     */
    private String recentAchievementBadge;

    /**
     * The total time spent in the "Mind" category.
     */
    private Long totalMindCategoryTimeSpent;

    /**
     * The total time spent in the "Body" category.
     */
    private Long totalBodyCategoryTimeSpent;

    /**
     * The total WXP earned by the user.
     */
    private Long totalWxpEarned;

    /**
     * The worldwide level rank of the user.
     */
    private Long levelRankWorldWide;

    /**
     * The Apple account name of the user.
     */
    private String appleAccountName;

    /**
     * The Google account name of the user.
     */
    private String googleAccountName;

    /**
     * The Facebook account name of the user.
     */
    private String facebookAccountName;

    /**
     * The country code of the user.
     */
    private String countryCode;

    /**
     * The list of pinned users for the current user.
     */
    private List<PinnedUserDTO> pinnedUsers;

    /**
     * Returns that if user is pinned or not.
     */
    private boolean isPinned;

    /**
     * The user type of the current user.
     */
    private UserType userType;
}
