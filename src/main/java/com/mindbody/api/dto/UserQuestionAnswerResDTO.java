package com.mindbody.api.dto;

import com.mindbody.api.enums.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class UserQuestionAnswerResDTO {

    private Long userId;

    private RoleType roleType;

    private UserType userType;

    private AccountStatus accountStatus;

    private LocalDateTime dateOfBirth;

    private String placeOfBirth;

    private GenderType genderType;

    private String secretToken;

    private String timezone;

    private ZodiacSignType zodiacSign;

    private boolean isProfileCompleted;

    private RegisterType registerType;

    private List<UserAstrologyDetailResDTO> userAstrologyDetails;

}
