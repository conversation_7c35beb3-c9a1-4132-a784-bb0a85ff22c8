package com.mindbody.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.NoArgsConstructor;


@NoArgsConstructor
public class CommonServiceFilterDTO {

    private Boolean active;

    private String searchFilter;

    public String getSearchFilter() {
        return searchFilter;
    }

    public void setSearchFilter(String searchFilter) {
        this.searchFilter = searchFilter;
    }

    @JsonProperty(value = "active")
    public Boolean isActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public CommonServiceFilterDTO(Boolean active) {
        this.active = active;
    }
}
