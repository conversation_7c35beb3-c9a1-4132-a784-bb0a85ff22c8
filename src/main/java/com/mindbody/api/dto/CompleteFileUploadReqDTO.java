package com.mindbody.api.dto;

import com.mindbody.api.enums.FileUploadModuleName;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class CompleteFileUploadReqDTO {

    @NotNull(message = "filename_required")
    @NotBlank(message = "filename_required")
    private String fileName;

    @ValueOfEnum(enumClass = FileUploadModuleName.class, message = "invalid_file_upload_module")
    @NotNull(message = "file_upload_module_required")
    private String moduleName;

    @NotNull(message = "uploadId_required")
    @NotBlank(message = "uploadId_required")
    private String uploadId;

    @NotNull(message = "part_etag_is_required")
    @NotEmpty(message = "part_etag_is_required")
    private List<PartEtagDTO> partETags;
}
