package com.mindbody.api.dto;


import com.mindbody.api.dto.cms.WorkoutPlanlistDetailResDTO;

import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class FavoriteListResDTO {

    private List<UserAudioListResDTO> favoriteAudioList;

    private List<WorkoutPlanlistDetailResDTO> favoriteWorkoutPlanList;

    public FavoriteListResDTO(List<UserAudioListResDTO> favoriteAudioList, List<WorkoutPlanlistDetailResDTO> favoriteWorkoutPlanList) {
        this.favoriteAudioList = favoriteAudioList;
        this.favoriteWorkoutPlanList = favoriteWorkoutPlanList;
    }
}
