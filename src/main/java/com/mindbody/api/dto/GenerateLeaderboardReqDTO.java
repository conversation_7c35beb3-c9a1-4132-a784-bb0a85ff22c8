package com.mindbody.api.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class GenerateLeaderboardReqDTO {

    @NotBlank(message = "category_required_error")
    private String category;

    @NotBlank(message = "timeframe_required_error")
    private String timeframe;

    @NotNull(message = "latitude_required_error")
    private Double latitude;

    @NotNull(message = "longitude_required_error")
    private Double longitude;

    @NotNull(message = "current_user_id_required_error")
    private Long currentUserId;

    @NotBlank(message = "leaderboard_type_required_error")
    private String leaderboardType;

    @NotNull(message = "start_date_time_required_error")
    private LocalDateTime startDateTime;

    @NotNull(message = "end_date_time_required_error")
    private LocalDateTime endDateTime;
}
