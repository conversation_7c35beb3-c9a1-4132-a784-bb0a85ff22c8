package com.mindbody.api.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class EditQuestionReqDTO {

    @NotNull(message = "question_id_required")
    private Long questionId;

    private String questionName;

    private String questionTitle;

    private List<@Valid EditOptionReqDTO> optionList;

    private List<DeleteOptionReqDTO> deleteOptionList;
}
