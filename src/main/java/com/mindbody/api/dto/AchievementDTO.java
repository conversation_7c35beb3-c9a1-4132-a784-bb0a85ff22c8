package com.mindbody.api.dto;

import com.mindbody.api.enums.AchievementActivityType;
import com.mindbody.api.enums.AchievementCategoryType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class AchievementDTO {
    private Long achievementsId;
    private AchievementCategoryType achievementCategoryType;
    private String subCategoryType;
    private String activityDetails;
    private AchievementActivityType achievementActivityType;
    private String title;
    private Boolean isDeleted;
    private Boolean isActive;

    private Long userAchievementsId;
    private Long userId;
    private Integer pointsEarned;
    private Integer wxpEarned;
    private Date acquiredDate;
    private Boolean isClaimed;
    private String entityAchievementMedals;


    public AchievementDTO(Long achievementsId,
                          AchievementCategoryType achievementCategoryType,
                          String subCategoryType,
                          String activityDetails,
                          AchievementActivityType achievementActivityType,
                          String title,
                          Boolean isDeleted,
                          Boolean isActive,
                          Long userAchievementsId,
                          Long userId,
                          Integer pointsEarned,
                          Integer wxpEarned,
                          Date acquiredDate,
                          Boolean isClaimed,
                          String entityAchievementMedals) {
        this.achievementsId = achievementsId;
        this.achievementCategoryType = achievementCategoryType;
        this.subCategoryType = subCategoryType;
        this.activityDetails = activityDetails;
        this.achievementActivityType = achievementActivityType;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.userAchievementsId = userAchievementsId;
        this.userId = userId;
        this.pointsEarned = pointsEarned;
        this.wxpEarned = wxpEarned;
        this.acquiredDate = acquiredDate;
        this.title = title;
        this.isClaimed = isClaimed;
        this.entityAchievementMedals = entityAchievementMedals;
    }
}
