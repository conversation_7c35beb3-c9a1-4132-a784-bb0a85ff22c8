package com.mindbody.api.dto;

import com.mindbody.api.enums.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class VerifyOTPResDTO {

    private Long userId;

    private String email;

    private String countryCode;

    private String phoneNumber;

    private RoleType roleType;

    private UserType userType;

    private AccountStatus accountStatus;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private boolean isDeleted;

    private boolean isActive;

    private String name;

    private LocalDateTime dateOfBirth;

    private String placeOfBirth;

    private String profileImage;

    private GenderType genderType;

    private ZodiacSignType zodiacSign;

    private String timezone;

    private String accessToken;
    private String resetPasswordToken;

    private String themeMode;

    public VerifyOTPResDTO(Long userId, String email, String countryCode, String phoneNumber, RoleType roleType, UserType userType, AccountStatus accountStatus, LocalDateTime createdAt, LocalDateTime updatedAt, boolean isDeleted, boolean isActive, String name, LocalDateTime dateOfBirth, String placeOfBirth, String profileImage, GenderType genderType, ZodiacSignType zodiacSign, String timezone,String accessToken, String resetPasswordToken, String themeMode) {
        this.userId = userId;
        this.email = email;
        this.countryCode = countryCode;
        this.phoneNumber = phoneNumber;
        this.roleType = roleType;
        this.userType = userType;
        this.accountStatus = accountStatus;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.name = name;
        this.dateOfBirth = dateOfBirth;
        this.placeOfBirth = placeOfBirth;
        this.profileImage = profileImage;
        this.genderType = genderType;
        this.zodiacSign = zodiacSign;
        this.timezone = timezone;
        this.accessToken = accessToken;
        this.resetPasswordToken = resetPasswordToken;
        this.themeMode = themeMode;
    }
}
