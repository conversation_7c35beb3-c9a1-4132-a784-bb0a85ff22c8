package com.mindbody.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class LeaderboardResDTO {

    private String name;
    private String facebookAccountName;
    private String googleAccountName;
    private String appleAccountName;
    private String title;
    private String badgeImage;
    private String borderImage;
    private Long userId;
    private String zodiacSignType;
    private Long currentLevel;
    private Long totalPoints;
    private Long totalWxp;

}
