package com.mindbody.api.dto;

import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubmitCategoryTimeSpentReqDTO {

    @ValueOfEnum(enumClass = AchievementCategoryType.class, message = "invalid_achievement_category_type")
    @NotNull(message = "achievement_category_type_required")
    private String category;

    @NotNull(message ="category_time_spent_required")
    private Long timeSpent;

    @NotNull(message = "user_id_required")
    private Long userId;

}
