package com.mindbody.api.dto;


import com.mindbody.api.enums.ActivityFactorType;
import com.mindbody.api.enums.GenderType;
import com.mindbody.api.enums.MeasurementUnitType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UserHealthReqDTO {

    @NotNull(message = "user_id_required")
    private Long userId;

    @NotNull(message = "weight_required")
    private double weight;

    @ValueOfEnum(enumClass = MeasurementUnitType.class, message = "invalid_weight_unit")
    @NotNull(message = "weight_unit_required")
    private MeasurementUnitType weightUnit;

    @NotNull(message = "height_required")
    private double height;

    @ValueOfEnum(enumClass = MeasurementUnitType.class, message = "invalid_height_unit")
    @NotNull(message = "height_unit_required")
    private MeasurementUnitType heightUnit;

    @NotNull(message = "age_required")
    private int age;

    @ValueOfEnum(enumClass = GenderType.class, message = "invalid_gender_type")
    @NotNull(message = "gender_required")
    private GenderType gender;

    @ValueOfEnum(enumClass = ActivityFactorType.class, message = "invalid_activity_factor_type")
    @NotNull(message = "activityFactor_required")
    private ActivityFactorType activityFactorType;

}
