package com.mindbody.api.dto;

import com.mindbody.api.enums.AchievementCategoryType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class UserTitlesDTO {

    private Long achievementId;
    private String titleName;
    private AchievementCategoryType achievementCategoryType;

    public UserTitlesDTO(String titleName, Long achievementId, AchievementCategoryType achievementCategoryType){
        this.titleName = titleName;
        this.achievementId = achievementId;
        this.achievementCategoryType = achievementCategoryType;
    }

}
