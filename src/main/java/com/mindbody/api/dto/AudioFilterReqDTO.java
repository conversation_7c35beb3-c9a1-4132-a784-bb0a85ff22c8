package com.mindbody.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotNull;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class AudioFilterReqDTO {

    private Boolean active;

    @ValueOfEnum(enumClass = AudioPlaylistType.class, message = "invalid_audio_playlist_type")
    @NotNull(message = "audio_playlist_type_required")
    private String audioPlaylistType;

    private Long audioPlaylistId;

    public @NotNull(message = "audio_playlist_type_required") String getAudioPlaylistType() {
        return audioPlaylistType;
    }

    public void setAudioPlaylistType(@NotNull(message = "audio_playlist_type_required") String audioPlaylistType) {
        this.audioPlaylistType = audioPlaylistType;
    }

    public Long getAudioPlaylistId() {
        return audioPlaylistId;
    }

    public void setAudioPlaylistId(Long audioPlaylistId) {
        this.audioPlaylistId = audioPlaylistId;
    }

    @JsonProperty(value = "active")
    public Boolean isActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

}
