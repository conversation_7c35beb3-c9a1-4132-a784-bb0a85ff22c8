package com.mindbody.api.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class UserMonthlyHoroscopeResDTO {
    private boolean status;
    private String sunSign;
    private String predictionMonth;
    private List<String> prediction;

    public UserMonthlyHoroscopeResDTO(boolean status, String sunSign, String predictionMonth, List<String> prediction) {
        this.status = status;
        this.sunSign = sunSign;
        this.predictionMonth = predictionMonth;
        this.prediction = prediction;
    }
}
