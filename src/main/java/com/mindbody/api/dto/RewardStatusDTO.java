package com.mindbody.api.dto;

import com.mindbody.api.enums.RewardType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class RewardStatusDTO {
    private Long rewardId;
    private String title;
    private String description;
    private String thumbnailImage;
    private Integer pointsRequired;
    private RewardType rewardType;
    private String rewardMetadata;
    private boolean isActive;
    private boolean isRedeemed;
    private boolean canRedeem;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
} 