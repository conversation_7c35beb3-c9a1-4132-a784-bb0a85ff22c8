package com.mindbody.api.dto;


import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.enums.RegisterType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AddCurrentConnectionReqDTO {

    @NotBlank(message = "email_id_required")
    @NotNull(message = "email_id_required")
    private String emailId;

    @NotBlank(message = "social_media_id_required")
    @NotNull(message = "social_media_id_required")
    private String socialMediaId;

    @NotBlank(message = "account_name_required")
    @NotNull(message = "account_name_required")
    private String accountName;

    @NotNull(message = "register_type_required")
    @ValueOfEnum(enumClass = RegisterType.class, message = "invalid_value_register_type")
    private String registerType;

    @NotNull(message = "user_id_required")
    private Long userId;

}