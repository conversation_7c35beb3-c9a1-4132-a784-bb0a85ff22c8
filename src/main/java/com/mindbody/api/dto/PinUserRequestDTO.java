package com.mindbody.api.dto;


import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for pinning a user on the leaderboard.
 */
@Data
public class PinUserRequestDTO {

    /**
     * The ID of the user to be pinned.
     */
    @NotNull(message = "pinned_user_id_required")
    private Long pinnedUserId;

    /**
     * The ID of the user who is pinning the other user.
     */
    @NotNull(message = "for_user_id_required")
    private Long forUserId;
}
