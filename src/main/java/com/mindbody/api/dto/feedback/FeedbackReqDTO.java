package com.mindbody.api.dto.feedback;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackReqDTO {

    private Long userId;

    private String name;

    @NotBlank(message = "feedback_not_empty")
    private String feedback;

    private String contactDetail;
} 