package com.mindbody.api.dto;

import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.enums.UserType;
import com.mindbody.api.enums.ZodiacSignCategoryType;
import com.mindbody.api.enums.ZodiacSignType;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class LeaderboardUserDTO {
    private Long rank;
    private String name;
    private ZodiacSignType zodiacSign;
    private Long mindCategoryTimeSpent;
    private Long bodyCategoryTimeSpent;
    private Long wxpEarned;
    private Long currentLevel;
    private String title;
    private String borderImage;
    private String badgeImage;
    private boolean isSubscribed;
    private Long userId;
    private AchievementCategoryType mostAchievedCategory;
    private Integer totalAchievements;
    private String recentAchievementTitle;
    private String recentAchievementBadge;
    private Long totalMindCategoryTimeSpent;
    private Long totalBodyCategoryTimeSpent;
    private Long totalWxpEarned;
    private Long levelRankWorldWide;
    private String appleAccountName;
    private String googleAccountName;
    private String facebookAccountName;
    private String countryCode;
    private ZodiacSignCategoryType zodiacSignCategory;
    private UserType userType;
}
