package com.mindbody.api.dto;

import com.mindbody.api.enums.AchievementActivityType;
import com.mindbody.api.enums.AchievementCategoryType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserAchievementDTO {
    private Long userAchievementId;
    private Long userId;
    private int pointsEarned;
    private int wxpEarned;
    private Date acquiredDate;
    private String title;
    private boolean isClaimed;
    private String medalName;
    private AchievementActivityType achievementActivityType;
    private AchievementCategoryType achievementCategoryType;
    private String setName;
    private String activityDetails;
    private String subCategoryType;

    public UserAchievementDTO(Long userAchievementId,
                              AchievementActivityType achievementActivityType,
                              AchievementCategoryType achievementCategoryType,
                              Date acquiredDate,
                              String activityDetails,
                              Boolean isClaimed,
                              Integer pointsEarned,
                              Long userId,
                              Integer wxpEarned,
                              String title,
                              String medalName,
                              String subCategoryType,
                              String setName){

        this.userAchievementId = userAchievementId;
        this.achievementActivityType = achievementActivityType;
        this.achievementCategoryType = achievementCategoryType;
        this.acquiredDate = acquiredDate;
        this.activityDetails = activityDetails;
        this.isClaimed = isClaimed;
        this.pointsEarned = pointsEarned;
        this.userId = userId;
        this.wxpEarned = wxpEarned;
        this.title = title;
        this.medalName = medalName;
        this.subCategoryType = subCategoryType;
        this.setName = setName;

    }
}