package com.mindbody.api.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserAudioPlaylistResDTO {
    private Long audioPlaylistId;

    private String audioPlaylistName;

    private String audioPlaylistImage;

    private boolean isAudioPlaylistLocked;

    public UserAudioPlaylistResDTO(Long audioPlaylistId, String audioPlaylistName, String audioPlaylistImage) {
        this.audioPlaylistId = audioPlaylistId;
        this.audioPlaylistName = audioPlaylistName;
        this.audioPlaylistImage = audioPlaylistImage;
        this.isAudioPlaylistLocked = false; // Default value
    }

    public UserAudioPlaylistResDTO(Long audioPlaylistId, String audioPlaylistName, String audioPlaylistImage, boolean isAudioPlaylistLocked) {
        this.audioPlaylistId = audioPlaylistId;
        this.audioPlaylistName = audioPlaylistName;
        this.audioPlaylistImage = audioPlaylistImage;
        this.isAudioPlaylistLocked = isAudioPlaylistLocked;
    }
}
