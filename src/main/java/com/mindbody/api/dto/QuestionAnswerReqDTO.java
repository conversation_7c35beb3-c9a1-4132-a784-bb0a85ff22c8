package com.mindbody.api.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class QuestionAnswerReqDTO {

    @NotNull(message = "question_id_required")
    private Long questionId;

    @NotNull(message = "answer_required")
    @NotEmpty(message = "answer_required")
    private List<Long> answerList;

}
