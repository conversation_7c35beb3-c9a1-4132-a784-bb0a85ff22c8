package com.mindbody.api.dto;

import com.mindbody.api.enums.GenderType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class UserQuestionAnswerReqDTO {

    @NotNull(message = "question_answer_is_required")
    @NotEmpty(message = "question_answer_is_required")
    private List<@Valid QuestionAnswerReqDTO> questionList;

    @NotNull(message = "date_of_birth_required")
    private LocalDateTime dateOfBirth;

    @NotNull(message = "birth_place_required")
    private String placeOfBirth;

    private GenderType genderType;

    @NotNull(message = "time_zone_required")
    @NotEmpty(message = "time_zone_required")
    private String timezone;

    private Long userId;

    @NotNull(message = "latitude_required")
    private double latitude;

    @NotNull(message = "longitude_required")
    private double longitude;

}
