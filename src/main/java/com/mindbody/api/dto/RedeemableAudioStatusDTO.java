package com.mindbody.api.dto;

import com.mindbody.api.enums.AudioPlaylistType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RedeemableAudioStatusDTO {
    private Long audioId;
    private String title;
    private String description;
    private AudioPlaylistType audioPlaylistType;
    private String thumbnailImage;
    private String audioFile;
    private Long audioPlaylistId;
    private String audioPlaylistName;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private boolean isDeleted;
    private boolean isActive;
    private boolean isRedeemable;
    private Integer points;
    private boolean canRedeem;
    private boolean isRedeemed;
} 