package com.mindbody.api.dto;

import com.mindbody.api.enums.FavoriteModuleType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class FavoriteListReqDTO extends CommonListDTO {

    @NotNull(message = "user_id_required")
    private Long userId;

    @ValueOfEnum(enumClass = FavoriteModuleType.class, message = "invalid_favorite_module_type")
    @NotNull(message = "favorite_module_required")
    private String favoriteModuleType;

}
