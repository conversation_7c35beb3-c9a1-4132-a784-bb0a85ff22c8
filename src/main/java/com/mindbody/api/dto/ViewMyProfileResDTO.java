package com.mindbody.api.dto;

import com.mindbody.api.enums.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class ViewMyProfileResDTO {
    private Long userId;

    private String email;

    private String countryCode;

    private String phoneNumber;

    private RoleType roleType;

    private UserType userType;

    private AccountStatus accountStatus;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private boolean isDeleted;

    private boolean isActive;

    private String name;

    private LocalDateTime dateOfBirth;

    private String placeOfBirth;

    private String profileImage;

    private GenderType genderType;

    private ZodiacSignType zodiacSign;

    private String timezone;

    private Integer streakCount;

    private boolean isProfileCompleted;

    private RegisterType registerType;

    private boolean isEmailRegistered;

    private String borderImage;

    private String badgeImage;

    private String title;

    private Long currentLevel;

    private Long currentPoint;

    private Long currentWxp;

    private Long nextLevel;

    private Long nextLevelWxp;

    private String referralCode;

    private String loginType;

    private String googleAccountName;

    private String appleAccountName;

    private String facebookAccountName;

    private boolean referralCodeEntered;

    private String themeMode;


    public ViewMyProfileResDTO(Long userId, String email, String countryCode, String phoneNumber, RoleType roleType, UserType userType, AccountStatus accountStatus, LocalDateTime createdAt, LocalDateTime updatedAt, boolean isDeleted, boolean isActive, String name, LocalDateTime dateOfBirth, String placeOfBirth, String profileImage, GenderType genderType, ZodiacSignType zodiacSign, String timezone, Integer streakCount, boolean isProfileCompleted, RegisterType registerType,boolean isEmailRegistered,String borderImage, String badgeImage, String title, Long currentLevel, Long currentPoint, Long currentWxp, Long nextLevel, Long nextLevelWxp, String referralCode, String loginType, String googleAccountName, String appleAccountName, String facebookAccountName, boolean referralCodeEntered, String themeMode) {
        this.userId = userId;
        this.email = email;
        this.countryCode = countryCode;
        this.phoneNumber = phoneNumber;
        this.roleType = roleType;
        this.userType = userType;
        this.accountStatus = accountStatus;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.name = name;
        this.dateOfBirth = dateOfBirth;
        this.placeOfBirth = placeOfBirth;
        this.profileImage = profileImage;
        this.genderType = genderType;
        this.zodiacSign = zodiacSign;
        this.timezone = timezone;
        this.streakCount = streakCount;
        this.isProfileCompleted = isProfileCompleted;
        this.registerType = registerType;
        this.isEmailRegistered = isEmailRegistered;
        this.borderImage = borderImage;
        this.badgeImage = badgeImage;
        this.title = title;
        this.currentLevel = currentLevel;
        this.currentPoint = currentPoint;
        this.currentWxp = currentWxp;
        this.nextLevel = nextLevel;
        this.nextLevelWxp = nextLevelWxp;
        this.referralCode = referralCode;
        this.loginType = loginType;
        this.googleAccountName = googleAccountName;
        this.appleAccountName = appleAccountName;
        this.facebookAccountName = facebookAccountName;
        this.referralCodeEntered = referralCodeEntered;
        this.themeMode = themeMode;
    }

}
