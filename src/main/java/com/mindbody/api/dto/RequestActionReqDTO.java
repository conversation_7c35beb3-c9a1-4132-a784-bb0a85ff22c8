//package com.mindbody.api.dto;
//
//import com.mindbody.api.enums.RequestActions;
//import com.mindbody.api.validator.ValueOfEnum;
//import jakarta.validation.constraints.NotNull;
//import lombok.Getter;
//import lombok.NoArgsConstructor;
//import lombok.Setter;
//
//@Getter
//@Setter
//@NoArgsConstructor
//public class RequestActionReqDTO {
//
//    @ValueOfEnum(enumClass = RequestActions.class, message = "reactivation_request_action_invalid_error")
//    @NotNull(message = "reactivation_request_action_required_error")
//    private String requestAction;
//
//    @NotNull(message = "user_id_required")
//    private Long userId;
//
//
//}
