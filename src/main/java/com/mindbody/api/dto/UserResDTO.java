package com.mindbody.api.dto;

import com.mindbody.api.enums.AccountStatus;
import com.mindbody.api.enums.RegisterType;
import com.mindbody.api.enums.RoleType;
import com.mindbody.api.enums.UserType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserResDTO {

    private Long userId;

    private String email;

    private String countryCode;

    private String phoneNumber;

    private RoleType roleType;

    private UserType userType;

    private AccountStatus accountStatus;

    private RegisterType registerType;

    private String socialMediaId;

    private boolean isDeleted;

    private boolean isActive;

}
