package com.mindbody.api.dto;

import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class HomeScreenReqDTO {

    @ValueOfEnum(enumClass = ZodiacSignType.class, message = "invalid_zodiac_sign")
    @NotNull(message = "zodiac_sign_required")
    private String zodiacSign;

    @NotNull(message = "user_id_required")
    private Long userId;
}
