package com.mindbody.api.dto;

import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.enums.ZodiacSignType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class UserAudioListResDTO {

    private Long audioId;

    private String title;

    private String description;

    private AudioPlaylistType audioPlaylistType;

    private String thumbnailImage;

    private String audioFile;

    private Long audioPlaylistId;

    private String audioPlaylistName;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private boolean isFavorite;

    private boolean isDeleted;

    private boolean isActive;

    private boolean isExclusive;

    private boolean isUnlocked;

    private boolean isRedeemable;

    private boolean isRedeemed;

    private List<ZodiacSignType> zodiacSigns;

    private String audioFileUrl;

    public UserAudioListResDTO(Long audioId, String title, String description, AudioPlaylistType audioPlaylistType,
                              String thumbnailImage, String audioFile, Long audioPlaylistId, String audioPlaylistName,
                              LocalDateTime createdAt, LocalDateTime updatedAt, boolean isFavorite, boolean isDeleted,
                              boolean isActive, boolean isExclusive, boolean isUnlocked, boolean isRedeemable, boolean isRedeemed) {
        this.audioId = audioId;
        this.title = title;
        this.description = description;
        this.audioPlaylistType = audioPlaylistType;
        this.thumbnailImage = thumbnailImage;
        this.audioFile = audioFile;
        this.audioPlaylistId = audioPlaylistId;
        this.audioPlaylistName = audioPlaylistName;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.isFavorite = isFavorite;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.isExclusive = isExclusive;
        this.isUnlocked = isUnlocked;
        this.isRedeemable = isRedeemable;
        this.isRedeemed = isRedeemed;
    }

    public UserAudioListResDTO(Long audioId, String title, String description, AudioPlaylistType audioPlaylistType,String thumbnailImage, String audioFile, Long audioPlaylistId, String audioPlaylistName, LocalDateTime createdAt, LocalDateTime updatedAt, boolean isFavorite, boolean isDeleted, boolean isActive, String audioFileUrl) {
        this.audioId = audioId;
        this.title = title;
        this.description = description;
        this.audioPlaylistType = audioPlaylistType;
        this.thumbnailImage = thumbnailImage;
        this.audioFile = audioFile;
        this.audioPlaylistId = audioPlaylistId;
        this.audioPlaylistName = audioPlaylistName;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.isFavorite = isFavorite;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.audioFileUrl = audioFileUrl;
    }

    public UserAudioListResDTO(Long audioId, String title, String description, AudioPlaylistType audioPlaylistType,String thumbnailImage, String audioFile, Long audioPlaylistId, String audioPlaylistName, LocalDateTime createdAt, LocalDateTime updatedAt, boolean isFavorite, boolean isDeleted, boolean isActive) {
        this.audioId = audioId;
        this.title = title;
        this.description = description;
        this.audioPlaylistType = audioPlaylistType;
        this.thumbnailImage = thumbnailImage;
        this.audioFile = audioFile;
        this.audioPlaylistId = audioPlaylistId;
        this.audioPlaylistName = audioPlaylistName;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.isFavorite = isFavorite;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.isExclusive = false;
    }

}
