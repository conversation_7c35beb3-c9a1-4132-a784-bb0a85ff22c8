package com.mindbody.api.dto;

import com.mindbody.api.enums.FileUploadModuleName;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class GeneratePreSignedUrlReqDTO {

    @NotNull(message = "filename_required")
    @NotBlank(message = "filename_required")
    private String fileName;

    @NotNull(message = "content_type_required")
    @NotBlank(message = "content_type_required")
    private String contentType;

    @NotNull(message = "part_count_required")
    private int partCount;

    @ValueOfEnum(enumClass = FileUploadModuleName.class, message = "invalid_file_upload_module")
    @NotNull(message = "file_upload_module_required")
    private String moduleName;

}
