package com.mindbody.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChatMessageDTO {

    private boolean is_sender;
    private String intent;
    private Object music;
    private Map<?,?> exercise;
    private Object greetings;
    private Object normal_response;
    private ChatMessageHoroscopeDTO horoscope;
    private Object zodiac_sign;
    private Object partner_compatibility;
    private String notification_type;
    private String notification_title;

}
