package com.mindbody.api.dto;

import com.mindbody.api.enums.RegisterType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class RemoveCurrentConnectionReqDTO {

    @NotNull(message = "user_id_required")
    private Long userId;

    @NotNull(message = "register_type_required")
    @ValueOfEnum(enumClass = RegisterType.class, message = "invalid_value_register_type")
    private String registerType;

}
