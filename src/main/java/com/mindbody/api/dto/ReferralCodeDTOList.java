package com.mindbody.api.dto;

import com.mindbody.api.enums.ZodiacSignType;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ReferralCodeDTOList {
    private String referralCode;
    private String userName;
    private String genderType;
    private ZodiacSignType zodiacSign;
    private String title;
    private String borderImage;
    private String badgeImage;
    private String facebookAccountName;
    private String googleAccountName;
    private String appleAccountName;
    private long usageCount;

    // Constructor that matches the query in UserReferralRepository
    public ReferralCodeDTOList(String code, String name, String genderType, ZodiacSignType zodiacSign,
                              String title, String borderImage, String badgeImage,
                              String facebookAccountName, String googleAccountName, String appleAccountName,
                              long count) {
        this.referralCode = code;
        this.userName = name;
        this.genderType = genderType;
        this.zodiacSign = zodiacSign;
        this.title = title;
        this.borderImage = borderImage;
        this.badgeImage = badgeImage;
        this.facebookAccountName = facebookAccountName;
        this.googleAccountName = googleAccountName;
        this.appleAccountName = appleAccountName;
        this.usageCount = count;
    }

    // Constructor without usageCount for the child class
    public ReferralCodeDTOList(String code, String name, String genderType, ZodiacSignType zodiacSign,
                              String title, String borderImage, String badgeImage,
                              String facebookAccountName, String googleAccountName, String appleAccountName) {
        this.referralCode = code;
        this.userName = name;
        this.genderType = genderType;
        this.zodiacSign = zodiacSign;
        this.title = title;
        this.borderImage = borderImage;
        this.badgeImage = badgeImage;
        this.facebookAccountName = facebookAccountName;
        this.googleAccountName = googleAccountName;
        this.appleAccountName = appleAccountName;
    }
}
