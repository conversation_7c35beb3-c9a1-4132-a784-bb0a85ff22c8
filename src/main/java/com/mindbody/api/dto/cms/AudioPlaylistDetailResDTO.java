package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.AudioPlaylistType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class AudioPlaylistDetailResDTO {

    private Long audioPlaylistId;

    private AudioPlaylistType audioPlaylistType;

    private String audioPlaylistName;

    private boolean isDeleted;

    private boolean isActive;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private String audioPlaylistImage;

    public AudioPlaylistDetailResDTO(Long audioPlaylistId, AudioPlaylistType audioPlaylistType, String audioPlaylistName, boolean isDeleted, boolean isActive, LocalDateTime createdAt, LocalDateTime updatedAt, String audioPlaylistImage) {
        this.audioPlaylistId = audioPlaylistId;
        this.audioPlaylistType = audioPlaylistType;
        this.audioPlaylistName = audioPlaylistName;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.audioPlaylistImage = audioPlaylistImage;
    }
}
