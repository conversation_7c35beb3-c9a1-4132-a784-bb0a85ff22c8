package com.mindbody.api.dto.cms;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class ProductCategoryResDTO {

    private Long productCategoryId;

    private String productCategoryName;

    private String productCategoryImage;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private boolean isDeleted;

    private boolean isActive;


    public ProductCategoryResDTO(Long productCategoryId, String productCategoryName, String productCategoryImage, LocalDateTime createdAt, LocalDateTime updatedAt, boolean isDeleted, boolean isActive) {
        this.productCategoryId = productCategoryId;
        this.productCategoryName = productCategoryName;
        this.productCategoryImage = productCategoryImage;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
    }
}
