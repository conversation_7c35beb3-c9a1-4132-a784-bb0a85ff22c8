package com.mindbody.api.dto.cms;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mindbody.api.filter.CommonSearchPagination;
import com.mindbody.api.filter.Page;
import com.mindbody.api.filter.PageFilter;
import com.mindbody.api.filter.SortBy;
import com.mindbody.api.util.StringUtil;
import jakarta.validation.Valid;

import java.util.Objects;

public class AchievementListReqDTO implements CommonSearchPagination {
    @JsonProperty("query")
    private String queryToSearch;

    @Valid
    private AchievementFilterReqDTO filters;

    private Page page;

    private SortBy sortBy;


    @Override
    public PageFilter getPage() {
        if (Objects.isNull(this.page)) {
            return new Page();
        }
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    @Override
    public String getQueryToSearch() {
        if (queryToSearch == null) return "";
        return StringUtil.escapeData(queryToSearch.trim());
    }

    public void setQueryToSearch(String queryToSearch) {
        this.queryToSearch = queryToSearch;
    }

    public SortBy getSortBy() {
        return sortBy;
    }

    public void setSortBy(SortBy sortBy) {
        this.sortBy = sortBy;
    }

    public AchievementFilterReqDTO getFilters() {
        return filters;
    }

    public void setFilters(AchievementFilterReqDTO filters) {
        this.filters = filters;
    }
}
