package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.DifficultyLevel;
import com.mindbody.api.enums.WorkoutPlanType;
import com.mindbody.api.enums.WorkoutPlaylistType;
import com.mindbody.api.enums.ZodiacSignType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class WorkoutPlanlistDetailResDTO {

    private Long workoutPlanId;

    private String workoutPlanTitle;

    private String workoutPlanDescription;

    private WorkoutPlanType workoutPlanType;

    private WorkoutPlaylistType workoutPlaylistType;

    private Long workoutPlaylistId;

    private String workoutPlaylistName;

    private String equipment;

    private Integer workoutPlanDuration;

    private DifficultyLevel difficultyLevel;

    private String workoutVideo;

    private String workoutVideoThumbnailImage;

    private boolean isDeleted;

    private boolean isActive;

    private boolean isFavorite;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private List<ExerciseDetailResDTO> exerciseList = new ArrayList<>();

    private List<ZodiacSignType> zodiacSigns;

    public WorkoutPlanlistDetailResDTO(Long workoutPlanId, String workoutPlanTitle, String workoutPlanDescription, WorkoutPlanType workoutPlanType,WorkoutPlaylistType workoutPlaylistType,Long workoutPlaylistId, String workoutPlaylistName, String equipment, Integer workoutPlanDuration, DifficultyLevel difficultyLevel, String workoutVideo, String workoutVideoThumbnailImage, boolean isFavorite,boolean isDeleted, boolean isActive, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.workoutPlanId = workoutPlanId;
        this.workoutPlanTitle = workoutPlanTitle;
        this.workoutPlanDescription = workoutPlanDescription;
        this.workoutPlanType = workoutPlanType;
        this.workoutPlaylistType = workoutPlaylistType;
        this.workoutPlaylistId = workoutPlaylistId;
        this.workoutPlaylistName = workoutPlaylistName;
        this.equipment = equipment;
        this.workoutPlanDuration = workoutPlanDuration;
        this.difficultyLevel = difficultyLevel;
        this.workoutVideo = workoutVideo;
        this.workoutVideoThumbnailImage = workoutVideoThumbnailImage;
        this.isFavorite = isFavorite;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
}
