package com.mindbody.api.dto.cms;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ExerciseListDTO {
    @NotNull(message = "exercise_id_required")
    private Long exerciseId;

    @NotNull(message = "exercise_duration_required")
    private Integer exerciseDuration;

    public ExerciseListDTO(Long exerciseId, Integer exerciseDuration) {
        this.exerciseId = exerciseId;
        this.exerciseDuration = exerciseDuration;
    }
}
