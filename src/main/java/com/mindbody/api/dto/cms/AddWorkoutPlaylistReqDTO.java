package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.WorkoutPlaylistType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AddWorkoutPlaylistReqDTO {
    @NotNull(message = "workout_playlist_name_required")
    @NotBlank(message = "workout_playlist_name_required")
    private String workoutPlaylistName;

    @ValueOfEnum(enumClass = WorkoutPlaylistType.class, message = "invalid_workout_playlist_type")
    @NotNull(message = "workout_playlist_type_required")
    private String workoutPlaylistType;
}
