package com.mindbody.api.dto.cms;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.enums.BodySubCategoryType;
import com.mindbody.api.enums.MindSubCategoryType;
import com.mindbody.api.enums.WarriorSubCategoryType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AchievementResDTO {
    private Long achievementsId;
    private AchievementCategoryType achievementCategoryType;

    @JsonIgnore
    private MindSubCategoryType mindSubCategoryType;

    @JsonIgnore
    private BodySubCategoryType bodySubCategoryType;

    @JsonIgnore
    private WarriorSubCategoryType warriorSubCategoryType;

    private String subCategoryType;

    private String activityDetails;
    private String title;
    private String border;
    private String medalName;
    @JsonIgnore
    private String audioPlaylistName;
    @JsonIgnore
    private String workoutPlaylistName;
    @JsonIgnore
    private String warriorSetName;
    private String setName;
    private Long achievementBorderId;
    private Long achievementMedalId;
    private Long achievementTitleId;
    private Integer points;
    private Integer wxp;

    public AchievementResDTO(Long achievementsId, AchievementCategoryType achievementCategoryType, String mindSubCategoryType, String bodySubCategoryType, String warriorSubCategoryType, String subCategoryType, String activityDetails, String title, String border, String medalName, String audioPlaylistName, String workoutPlaylistName, String warriorSetName, String playlistName, Long achievementBorderId, Long achievementMedalId, Long achievementTitleId, int points, int wxp) {
        this.achievementsId = achievementsId;
        this.achievementCategoryType = achievementCategoryType;
        this.mindSubCategoryType = convertToEnum(MindSubCategoryType.class, mindSubCategoryType, MindSubCategoryType.DEFAULT);
        this.bodySubCategoryType = convertToEnum(BodySubCategoryType.class, bodySubCategoryType, BodySubCategoryType.DEFAULT);
        this.warriorSubCategoryType = convertToEnum(WarriorSubCategoryType.class, warriorSubCategoryType, WarriorSubCategoryType.DEFAULT);
        this.subCategoryType = subCategoryType;
        this.activityDetails = activityDetails;
        this.title = title;
        this.border = border;
        this.medalName = medalName;
        this.audioPlaylistName = audioPlaylistName;
        this.workoutPlaylistName = workoutPlaylistName;
        this.warriorSetName = warriorSetName;
        this.setName = playlistName;
        this.achievementBorderId = achievementBorderId;
        this.achievementMedalId = achievementMedalId;
        this.achievementTitleId = achievementTitleId;
        this.points=points;
        this.wxp=wxp;
    }

    private <T extends Enum<T>> T convertToEnum(Class<T> enumType, String value, T defaultValue) {
        if (value == null || value.isEmpty()) {
            return defaultValue;
        }
        try {
            return Enum.valueOf(enumType, value);
        } catch (IllegalArgumentException e) {
            return defaultValue;
        }
    }
}
