package com.mindbody.api.dto.cms;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EditAchievementRewardReqDTO {

    @NotNull(message = "achievement_rewards_id_required")
    private Long achievementRewardsId;

    @NotNull(message = "wxp_required")
    private Integer wxp;

    @NotNull(message = "points_required")
    private Integer points;
}
