package com.mindbody.api.dto.cms;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EditStaticPageReqDTO {

    @NotNull(message = "static_page_id_required")
    private Long staticPageId;

    @NotBlank(message = "static_page_content_required")
    private String staticPageContent;
}
