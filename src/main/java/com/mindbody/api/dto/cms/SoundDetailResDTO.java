package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.SoundType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SoundDetailResDTO {
    private Long soundId;

    private String title;

    private SoundType soundType;

    private boolean isActive;

    private boolean isDeleted;

    private String soundFile;

    public SoundDetailResDTO(Long soundId, String title, SoundType soundType, boolean isActive, boolean isDeleted, String soundFile) {
        this.soundId = soundId;
        this.title = title;
        this.soundType = soundType;
        this.isActive = isActive;
        this.isDeleted = isDeleted;
        this.soundFile = soundFile;
    }
}
