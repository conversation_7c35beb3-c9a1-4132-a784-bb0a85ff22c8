package com.mindbody.api.dto.cms;


import com.mindbody.api.enums.WorkoutPlaylistType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class WorkoutPlaylistDetailResDTO {

    private Long workoutPlaylistId;

    private WorkoutPlaylistType workoutPlaylistType;

    private String workoutPlaylistName;

    private boolean isDeleted;

    private boolean isActive;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private String workoutPlaylistImage;

    public WorkoutPlaylistDetailResDTO(Long workoutPlaylistId, WorkoutPlaylistType workoutPlaylistType, String workoutPlaylistName, boolean isDeleted, boolean isActive, LocalDateTime createdAt, LocalDateTime updatedAt, String workoutPlaylistImage) {
        this.workoutPlaylistId = workoutPlaylistId;
        this.workoutPlaylistType = workoutPlaylistType;
        this.workoutPlaylistName = workoutPlaylistName;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.workoutPlaylistImage = workoutPlaylistImage;
    }
}
