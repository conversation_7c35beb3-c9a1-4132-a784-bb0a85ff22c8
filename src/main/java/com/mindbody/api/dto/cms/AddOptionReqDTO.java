package com.mindbody.api.dto.cms;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

@Getter
@Setter
@NoArgsConstructor
public class AddOptionReqDTO {

    @NotBlank(message = "option_name_required")
    @NotNull(message = "option_name_required")
    private String optionName;

    private MultipartFile optionImage;
}
