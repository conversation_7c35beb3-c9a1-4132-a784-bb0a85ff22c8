package com.mindbody.api.dto.cms;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class EditWorkoutPlaylistReqDTO {
    @NotNull(message = "workout_playlist_id_required")
    private Long workoutPlaylistId;

    @NotBlank(message = "workout_playlist_name_required")
    private String workoutPlaylistName;

    private String workoutPlaylistType;
}
