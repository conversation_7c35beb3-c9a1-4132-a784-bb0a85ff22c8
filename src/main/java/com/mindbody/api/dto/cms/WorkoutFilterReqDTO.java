package com.mindbody.api.dto.cms;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mindbody.api.enums.DifficultyLevel;
import com.mindbody.api.enums.WorkoutPlaylistType;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.validator.ValueOfEnum;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
public class WorkoutFilterReqDTO {
    private Boolean active;

    @ValueOfEnum(enumClass = WorkoutPlaylistType.class, message = "invalid_workout_playlist_type")
    private String workoutPlaylistType;

    private Long workoutPlaylistId;

    @ValueOfEnum(enumClass = DifficultyLevel.class, message = "invalid_difficulty_level")
    private String difficultyLevel;

    @ValueOfEnum(enumClass = ZodiacSignType.class, message = "invalid_zodiac_sign")
    private String zodiacSign;

    public String getWorkoutPlaylistType() {
        return workoutPlaylistType;
    }

    public void setWorkoutPlaylistType(String workoutPlaylistType) {
        this.workoutPlaylistType = workoutPlaylistType;
    }

    private Long userId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getWorkoutPlaylistId() {
        return workoutPlaylistId;
    }

    public void setWorkoutPlaylistId(Long workoutPlaylistId) {
        this.workoutPlaylistId = workoutPlaylistId;
    }

    @JsonProperty(value = "active")
    public Boolean isActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public String getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(String difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }

    public String getZodiacSign() {
        return zodiacSign;
    }

    public void setZodiacSign(String zodiacSign) {
        this.zodiacSign = zodiacSign;
    }
}
