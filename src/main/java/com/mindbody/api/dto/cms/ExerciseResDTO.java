package com.mindbody.api.dto.cms;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class ExerciseResDTO {

    private Long exerciseId;

    private String title;

    private String description;

    private String exerciseVideo;

    private String exerciseThumbnailImage;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private boolean isDeleted;

    private boolean isActive;


    public ExerciseResDTO(Long exerciseId, String title, String description, String exerciseVideo, String exerciseThumbnailImage, LocalDateTime createdAt, LocalDateTime updatedAt, boolean isDeleted, boolean isActive) {
        this.exerciseId = exerciseId;
        this.title = title;
        this.description = description;
        this.exerciseVideo = exerciseVideo;
        this.exerciseThumbnailImage = exerciseThumbnailImage;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
    }

}
