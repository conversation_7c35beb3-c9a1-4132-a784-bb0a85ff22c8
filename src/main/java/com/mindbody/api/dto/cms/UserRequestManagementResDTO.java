package com.mindbody.api.dto.cms;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class UserRequestManagementResDTO {

    private Long userId;

    private String name;

    private String email;

    private String countryCode;

    private String phoneNumber;

//    private boolean isReactivationRequestPending;

    private boolean isDeleted;

    private boolean isActive;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public UserRequestManagementResDTO(Long userId, String email, String countryCode, String phoneNumber, String name, boolean isDeleted, boolean isActive , LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.userId = userId;
        this.email = email;
        this.countryCode = countryCode;
        this.phoneNumber = phoneNumber;
//        this.isReactivationRequestPending = isReactivationRequestPending;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.name = name;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }



}
