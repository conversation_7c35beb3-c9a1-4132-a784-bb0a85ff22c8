package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.enums.ZodiacSignType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class AudioResDTO {


    private Long audioId;

    private String title;

    private String description;

    private AudioPlaylistType audioPlaylistType;

    private String thumbnailImage;

    private String audioFile;

    private Long audioPlaylistId;

    private String audioPlaylistName;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private boolean isDeleted;

    private boolean isActive;

    private boolean isExclusive;

    private boolean isRedeemable;

    private List<ZodiacSignType> zodiacSigns;

    public AudioResDTO(Long audioId, String title, String description, AudioPlaylistType audioPlaylistType,String thumbnailImage, String audioFile,Long audioPlaylistId, String audioPlaylistName, LocalDateTime createdAt, LocalDateTime updatedAt, boolean isDeleted, boolean isActive, boolean isExclusive, boolean isRedeemable) {
        this.audioId = audioId;
        this.title = title;
        this.description = description;
        this.audioPlaylistType = audioPlaylistType;
        this.thumbnailImage = thumbnailImage;
        this.audioFile = audioFile;
        this.audioPlaylistId = audioPlaylistId;
        this.audioPlaylistName = audioPlaylistName;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.isExclusive = isExclusive;
        this.isRedeemable = isRedeemable;
    }


}
