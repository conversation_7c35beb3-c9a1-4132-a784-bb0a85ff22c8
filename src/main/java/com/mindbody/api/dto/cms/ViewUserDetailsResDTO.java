package com.mindbody.api.dto.cms;

import com.mindbody.api.dto.UserAstrologyDetailResDTO;
import com.mindbody.api.dto.UserCurrentConnectionListDTO;
import com.mindbody.api.enums.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
public class ViewUserDetailsResDTO {
    private Long userId;
    private String email;
    private String countryCode;
    private String phoneNumber;
    private RoleType roleType;
    private UserType userType;
    private AccountStatus accountStatus;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private boolean isDeleted;
    private boolean isActive;
    private String referralCode;
    private String name;
    private LocalDateTime dateOfBirth;
    private String placeOfBirth;
    private String profileImage;
    private GenderType genderType;
    private ZodiacSignType zodiacSign;
    private String timezone;
    private boolean isProfileCompleted;
    private RegisterType registerType;
    private boolean isEmailRegistered;
    private Long totalPoints;
    private Long totalWxp;
    private Long currentLevel;
    private List<UserQuestionAnswerDetails> userQuestionAnswerDetails;
    private List<UserAstrologyDetailResDTO> userAstrologyDetailResDTOList;
    private List<UserCurrentConnectionListDTO> userCurrentConnectionList;

    public ViewUserDetailsResDTO(Long userId, String email, String countryCode, String phoneNumber, RoleType roleType, UserType userType, AccountStatus accountStatus,RegisterType registerType,boolean isEmailRegistered, boolean isProfileCompleted, LocalDateTime createdAt, LocalDateTime updatedAt, boolean isDeleted, boolean isActive,String referralCode, String name, LocalDateTime dateOfBirth, String placeOfBirth, String profileImage, GenderType genderType,ZodiacSignType zodiacSign, String timezone,Long totalPoints,Long totalWxp,Long currentLevel) {
        this.userId = userId;
        this.email = email;
        this.countryCode = countryCode;
        this.phoneNumber = phoneNumber;
        this.roleType = roleType;
        this.userType = userType;
        this.accountStatus = accountStatus;
        this.registerType = registerType;
        this.isEmailRegistered =  isEmailRegistered;
        this.isProfileCompleted = isProfileCompleted;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.referralCode = referralCode;
        this.name = name;
        this.dateOfBirth = dateOfBirth;
        this.placeOfBirth = placeOfBirth;
        this.profileImage = profileImage;
        this.genderType = genderType;
        this.zodiacSign = zodiacSign;
        this.timezone = timezone;
        this.totalPoints = (totalPoints != null) ? totalPoints : 0;
        this.totalWxp = (totalWxp != null) ? totalWxp : 0;
        this.currentLevel = (currentLevel != null) ? currentLevel : 0;
    }
}
