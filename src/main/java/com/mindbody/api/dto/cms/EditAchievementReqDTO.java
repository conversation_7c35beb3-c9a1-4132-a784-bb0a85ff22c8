package com.mindbody.api.dto.cms;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EditAchievementReqDTO {
    @NotNull(message = "achievement_id_required")
    private Long achievementId;

    @NotNull(message = "achievement_title_id_required")
    private Long achievementTitleId;

//    private String title;

//    @NotNull(message = "achievement_border_id_required")
    private Long achievementBorderId;

    @NotNull(message = "achievement_medal_id_required")
    private Long achievementMedalId;

    private Boolean removeBadgeImage;
    private Boolean removeBorderImage;
}
