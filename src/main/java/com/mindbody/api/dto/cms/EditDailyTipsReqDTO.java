package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class EditDailyTipsReqDTO {

    @NotNull(message = "daily_tips_id_required")
    private Long dailyTipsId;

    @ValueOfEnum(enumClass = ZodiacSignType.class, message = "invalid_zodiac_sign")
    @NotNull(message = "zodiac_sign_required")
    private String zodiacSign;

    @NotBlank(message = "tips_title_required")
    @NotNull(message = "tips_title_required")
    private String title;
}
