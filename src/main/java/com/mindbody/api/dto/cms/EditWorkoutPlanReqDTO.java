package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.DifficultyLevel;
import com.mindbody.api.enums.WorkoutPlaylistType;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class EditWorkoutPlanReqDTO {

    @NotNull(message = "workout_plan_id_required")
    private Long workoutPlanId;

    @NotNull(message = "workout_plan_title_required")
    @NotBlank(message = "workout_plan_title_required")
    private String workoutPlanTitle;

    @NotNull(message = "workout_plan_description_required")
    @NotBlank(message = "workout_plan_description_required")
    private String workoutPlanDescription;

    private String equipment;

    private String workoutVideo;

    private MultipartFile workoutVideoThumbnailImage;

    @NotNull(message = "workout_plan_duration_required")
    private Integer workoutPlanDuration;

    @ValueOfEnum(enumClass = DifficultyLevel.class, message = "invalid_difficulty_level")
    @NotNull(message = "difficulty_level_required")
    private String difficultyLevel;

    private List<String> zodiacSigns;

    private List<String> deleteZodiacSigns;

    @ValueOfEnum(enumClass = WorkoutPlaylistType.class, message = "invalid_workout_playlist_type")
    @NotNull(message = "workout_playlist_type_required")
    private String workoutPlaylistType;

    @NotNull(message = "workout_playlist_id_required")
    private Long workoutPlaylistId;

    private List<Long> deleteExerciseVideoIds;

    private List<@Valid ExerciseListDTO> exerciseList;




}
