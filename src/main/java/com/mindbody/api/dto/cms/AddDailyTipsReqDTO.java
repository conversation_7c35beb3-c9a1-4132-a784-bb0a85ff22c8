package com.mindbody.api.dto.cms;


import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AddDailyTipsReqDTO {

    @ValueOfEnum(enumClass = ZodiacSignType.class, message = "invalid_zodiac_sign")
    @NotNull(message = "zodiac_sign_required")
    private String zodiacSign;
}
