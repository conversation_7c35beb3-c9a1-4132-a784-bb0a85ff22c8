package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class AddAudioReqDTO {

    @NotNull(message = "audio_playlist_id_required")
    private Long audioPlaylistId;

    @NotNull(message = "audio_title_required")
    @NotEmpty(message = "audio_title_required")
    private String title;

    @NotNull(message = "audio_description_required")
    @NotEmpty(message = "audio_description_required")
    private String description;

    @ValueOfEnum(enumClass = AudioPlaylistType.class, message = "invalid_audio_playlist_type")
    @NotNull(message = "audio_playlist_type_required")
    private String audioPlaylistType;

    @NotNull(message = "zodiac_sign_required")
    @NotEmpty(message = "zodiac_sign_required")
    private List<String> zodiacSigns;

    private MultipartFile thumbnailImage;

    private String audioFile;
    
    private boolean isExclusive;
    
    private boolean isRedeemable;

    public boolean isRedeemable() {
        return isRedeemable;
    }

    public void setRedeemable(boolean redeemable) {
        isRedeemable = redeemable;
    }
}
