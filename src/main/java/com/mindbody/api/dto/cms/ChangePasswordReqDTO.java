package com.mindbody.api.dto.cms;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ChangePasswordReqDTO {

    @NotBlank(message = "old_password_not_empty")
    private String oldPassword;

    @NotBlank(message = "new_password_not_empty")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9!@#$%^&*]{5,}$", message = "password_pattern_check_error")
    private String newPassword;

    @NotBlank(message = "confirm_password_not_empty")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9!@#$%^&*]{5,}$", message = "password_pattern_check_error")
    private String confirmPassword;
}
