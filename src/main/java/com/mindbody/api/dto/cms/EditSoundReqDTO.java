package com.mindbody.api.dto.cms;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

@Getter
@Setter
public class EditSoundReqDTO {
    @NotNull(message = "sound_id_required")
    private Long soundId;

    @NotBlank(message = "title_required")
    private String title;

    private String soundType;

    private MultipartFile soundFile;


}
