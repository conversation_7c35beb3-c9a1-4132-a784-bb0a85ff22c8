package com.mindbody.api.dto.cms;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
public class AdminAccessTokenDTO  {

    private Long adminId;

    private String firstName;

    private String lastName;

    private String email;

    private String profileImage="";

    private boolean isDeleted;

    private boolean isActive;

    private String accessToken;

    public AdminAccessTokenDTO(Long adminId, String firstName, String lastName, String email, String profileImage, boolean isDeleted, boolean isActive, String accessToken) {
        this.adminId = adminId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.profileImage = profileImage;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.accessToken = accessToken;
    }
}