package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.*;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class AddWorkoutPlanReqDTO {

    @NotNull(message = "workout_plan_title_required")
    @NotBlank(message = "workout_plan_title_required")
    private String workoutPlanTitle;

    @NotNull(message = "workout_plan_description_required")
    @NotBlank(message = "workout_plan_description_required")
    private String workoutPlanDescription;

    private String equipment;

    @ValueOfEnum(enumClass = WorkoutPlanType.class, message = "invalid_workout_plan_type")
    @NotNull(message = "workout_plan_type_required")
    private String workoutPlanType;

    @NotNull(message = "workout_plan_duration_required")
    private Integer workoutPlanDuration;

    @ValueOfEnum(enumClass = DifficultyLevel.class, message = "invalid_difficulty_level")
    @NotNull(message = "difficulty_level_required")
    private String difficultyLevel;

    @NotNull(message = "zodiac_sign_required")
    @NotEmpty(message = "zodiac_sign_required")
    private List<String> zodiacSigns;

    @ValueOfEnum(enumClass = WorkoutPlaylistType.class, message = "invalid_workout_playlist_type")
    @NotNull(message = "workout_playlist_type_required")
    private String workoutPlaylistType;

    @NotNull(message = "workout_playlist_id_required")
    private Long workoutPlaylistId;

    private String workoutVideo;

    private MultipartFile workoutVideoThumbnailImage;

    private List<@Valid ExerciseListDTO> exerciseList;


}
