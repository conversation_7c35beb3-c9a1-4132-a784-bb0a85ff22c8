package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.SoundType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

@Getter
@Setter
public class AddSoundReqDTO {
    @NotBlank(message = "title_required")
    private String title;

    @ValueOfEnum(enumClass = SoundType.class, message = "invalid_sound_type")
    @NotNull(message = "sound_type_required")
    private String soundType;

    private MultipartFile soundFile;


}
