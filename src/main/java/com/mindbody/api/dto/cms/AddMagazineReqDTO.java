package com.mindbody.api.dto.cms;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

@Getter
@Setter
@NoArgsConstructor
public class AddMagazineReqDTO {
    @NotBlank(message = "title_required")
    private String title;

    private String subTitle;

    @NotBlank(message = "description_required")
    private String description;

    @NotBlank(message = "magazine_link_required")
    private String magazineLink;

    private MultipartFile magazineImage;

    private MultipartFile magazineThumbnailImage;

}
