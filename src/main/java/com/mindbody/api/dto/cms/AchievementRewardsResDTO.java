package com.mindbody.api.dto.cms;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AchievementRewardsResDTO {

    private Long achievementRewardsId;

    private Integer points;

    private Integer wxp;

    private String medalName;

    public AchievementRewardsResDTO(Long achievementRewardsId, Integer points, Integer wxp, String medalName) {
        this.achievementRewardsId = achievementRewardsId;
        this.points = points;
        this.wxp = wxp;
        this.medalName = medalName;
    }
}
