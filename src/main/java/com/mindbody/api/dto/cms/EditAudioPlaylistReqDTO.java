package com.mindbody.api.dto.cms;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class EditAudioPlaylistReqDTO {
    @NotNull(message = "audio_playlist_id_required")
    private Long audioPlaylistId;

    @NotBlank(message = "audio_playlist_name_required")
    private String audioPlaylistName;

    private String audioPlaylistType;
}
