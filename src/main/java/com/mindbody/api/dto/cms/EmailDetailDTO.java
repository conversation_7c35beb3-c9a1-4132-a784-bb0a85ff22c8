package com.mindbody.api.dto.cms;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor

public class EmailDetailDTO {

    private String recipient;

    private String msgBody;

    private String subject;

    private String attachment;

    public EmailDetailDTO(String recipient, String msgBody, String subject) {
        this.recipient = recipient;
        this.msgBody = msgBody;
        this.subject = subject;
    }

}
