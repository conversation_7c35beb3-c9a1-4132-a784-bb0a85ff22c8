package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class UserManagementResDTO {
    private Long userId;

    private String email;

    private String countryCode;

    private String phoneNumber;

    private RoleType roleType;

    private UserType userType;

    private AccountStatus accountStatus;

    private boolean isDeleted;

    private boolean isActive;

    private String name;

    private LocalDateTime dateOfBirth;

    private String placeOfBirth;

    private String profileImage;

    private GenderType genderType;

    private ZodiacSignType zodiacSign;

    private String timezone;

    private boolean isProfileCompleted;

    private RegisterType registerType;

    private boolean isEmailRegistered;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public UserManagementResDTO(Long userId, String email, String countryCode, String phoneNumber, RoleType roleType, UserType userType, AccountStatus accountStatus, RegisterType registerType,boolean isEmailRegistered, boolean isProfileCompleted, boolean isDeleted, boolean isActive, String name, LocalDateTime dateOfBirth, String placeOfBirth, String profileImage, GenderType genderType,ZodiacSignType zodiacSign, String timezone,LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.userId = userId;
        this.email = email;
        this.countryCode = countryCode;
        this.phoneNumber = phoneNumber;
        this.roleType = roleType;
        this.userType = userType;
        this.accountStatus = accountStatus;
        this.registerType = registerType;
        this.isEmailRegistered =  isEmailRegistered;
        this.isProfileCompleted = isProfileCompleted;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.name = name;
        this.dateOfBirth = dateOfBirth;
        this.placeOfBirth = placeOfBirth;
        this.profileImage = profileImage;
        this.genderType = genderType;
        this.zodiacSign = zodiacSign;
        this.timezone = timezone;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }



}
