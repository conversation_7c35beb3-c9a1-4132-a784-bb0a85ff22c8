package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AddAudioPlaylistReqDTO {
    @NotNull(message = "audio_playlist_name_required")
    @NotBlank(message = "audio_playlist_name_required")
    private String audioPlaylistName;

    @ValueOfEnum(enumClass = AudioPlaylistType.class, message = "invalid_audio_playlist_type")
    @NotNull(message = "audio_playlist_type_required")
    private String audioPlaylistType;
}
