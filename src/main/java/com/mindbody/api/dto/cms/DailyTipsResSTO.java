package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.ZodiacSignType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class DailyTipsResSTO {

    private Long dailyTipsId;

    private ZodiacSignType zodiacSign;

    private String title;

    private boolean isDeleted;

    private boolean isActive;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DailyTipsResSTO(Long dailyTipsId, ZodiacSignType zodiacSign, String title, boolean isDeleted, boolean isActive, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.dailyTipsId = dailyTipsId;
        this.zodiacSign = zodiacSign;
        this.title = title;
        this.isDeleted = isDeleted;
        this.isActive = isActive;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

}
