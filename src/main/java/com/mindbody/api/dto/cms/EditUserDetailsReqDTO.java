package com.mindbody.api.dto.cms;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EditUserDetailsReqDTO {
    @NotNull(message = "user_id_required")
    private Long userId;

    @NotNull(message = "name_required")
    @NotBlank(message = "name_required")
    private String name;

    @Email(message = "email_invalid_error", regexp = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}")
    private String email;

    @Pattern(message = "country_code_invalid_error", regexp = "\\+[0-9]{1,4}")
    private String countryCode;

    @Pattern(message = "phone_number_invalid_error", regexp = "[0-9]{10,15}")
    private String phoneNumber;
}
