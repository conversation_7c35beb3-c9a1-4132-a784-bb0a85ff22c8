package com.mindbody.api.dto.cms;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class DashboardResDTO {
    private long audioCount = 0L;
    private long strengthWorkoutPlanCount = 0L;
    private long flexibilityWorkoutPlanCount = 0L;
    private long enduranceWorkoutPlanCount = 0L;
    private List<UserStatsDTO> registeredAndAnonymousUsersCount = new ArrayList<>();
    private List<UserLevelStatsDTO> usersCountByAchievementLevel = new ArrayList<>();

    @Getter
    @Setter
    @AllArgsConstructor
    public static class UserStatsDTO {
        private String name;
        private long registered;
        private long anonymous;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public static class UserLevelStatsDTO {
        private String levelRange; // e.g., "1-10", "11-20"
        private long userCount;
    }
}
