package com.mindbody.api.dto.cms;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

@Getter
@Setter
@NoArgsConstructor
public class AddExerciseDTO {

    @NotBlank(message = "exercise_title_required")
    @NotNull(message = "exercise_title_required")
    private String title;

    @NotBlank(message = "exercise_description_required")
    @NotNull(message = "exercise_description_required")
    private String description;

    private String exerciseVideo;

    private MultipartFile exerciseThumbnailImage;

}
