package com.mindbody.api.dto.cms;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class MagazineDetailResDTO {
    private Long magazineId;

    private String title;

    private String subTitle;

    private String description;

    private String magazineLink;

    private boolean isActive;

    private boolean isDeleted;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private String magazineImage;

    private String magazineThumbnailImage;

    public MagazineDetailResDTO(Long magazineId, String title, String subTitle, String description,String magazineLink, boolean isActive, boolean isDeleted, LocalDateTime createdAt, LocalDateTime updatedAt, String magazineImage, String magazineThumbnailImage) {
        this.magazineId = magazineId;
        this.title = title;
        this.subTitle = subTitle;
        this.description = description;
        this.magazineLink = magazineLink;
        this.isActive = isActive;
        this.isDeleted = isDeleted;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.magazineImage = magazineImage;
        this.magazineThumbnailImage = magazineThumbnailImage;
    }

}
