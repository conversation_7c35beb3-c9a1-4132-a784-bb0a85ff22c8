package com.mindbody.api.dto.cms;

import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.validator.ValueOfEnum;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class AchievementFilterReqDTO {

    @ValueOfEnum(enumClass = AchievementCategoryType.class, message = "invalid_achievement_category_type")
    private String achievementCategoryType;

    public String getAchievementCategoryType() {
        return achievementCategoryType;
    }

    public void setAchievementCategoryType(String achievementCategoryType) {
        this.achievementCategoryType = achievementCategoryType;
    }
}
