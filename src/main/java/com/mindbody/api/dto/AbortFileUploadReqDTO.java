package com.mindbody.api.dto;

import com.mindbody.api.enums.FileUploadModuleName;
import com.mindbody.api.validator.ValueOfEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AbortFileUploadReqDTO {

    @NotNull(message = "filename_required")
    @NotBlank(message = "filename_required")
    private String fileName;

    @NotNull(message = "uploadId_required")
    @NotBlank(message = "uploadId_required")
    private String uploadId;

    @ValueOfEnum(enumClass = FileUploadModuleName.class, message = "invalid_file_upload_module")
    @NotNull(message = "file_upload_module_required")
    private String moduleName;

}
