package com.mindbody.api.dto;

import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.enums.RewardType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class RedeemableItemDTO {
    private Long itemId;
    private String title;
    private String description;
    private String thumbnailImage;
    private Integer pointsRequired;
    private boolean isActive;
    private boolean isRedeemed;
    private boolean canRedeem;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String itemType; // "AUDIO" or "REWARD"
    
    // Audio specific fields (will be null for rewards)
    private AudioPlaylistType audioPlaylistType;
    private String audioFile;
    private Long audioPlaylistId;
    private String audioPlaylistName;
    
    // Reward specific fields (will be null for audios)
    private RewardType rewardType;
    private String rewardMetadata;
    private List<Map<String, String>> zodiacWallpapers; // List of zodiac wallpapers with their signs
} 