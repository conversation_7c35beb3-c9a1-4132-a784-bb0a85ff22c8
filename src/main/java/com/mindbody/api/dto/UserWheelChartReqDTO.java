package com.mindbody.api.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UserWheelChartReqDTO {

    @NotNull(message = "day_required")
    private int day;

    @NotNull(message = "month_required")
    private int month;

    @NotNull(message = "year_required")
    private int year;

    @NotNull(message = "hour_required")
    private int hour;

    @NotNull(message = "minute_required")
    private int min;

    @NotNull(message = "latitude_required")
    private double latitude;

    @NotNull(message = "longitude_required")
    private double longitude;

    @NotNull(message = "time_zone_required")
    private int timezone;

    @NotNull(message = "house_type_required")
    private String houseType;

    @NotNull(message = "inner_circle_background_required")
    private String innerCircleBackground;

    @NotNull(message = "planet_icon_required")
    private String planetIconColor;

    @NotNull(message = "sign_icon_required")
    private String signIconColor;

    @NotNull(message = "sign_background_required")
    private String signBackground;

    @NotNull(message = "chart_size_required")
    private String chartSize;

    @NotNull(message = "image_type_required")
    private String imageType;

    public UserWheelChartReqDTO(int day, int month, int year, int hour, int min, double latitude, double longitude, int timezone, String houseType, String innerCircleBackground, String planetIconColor, String signIconColor, String signBackground, String chartSize, String imageType) {
        this.day = day;
        this.month = month;
        this.year = year;
        this.hour = hour;
        this.min = min;
        this.latitude = latitude;
        this.longitude = longitude;
        this.timezone = timezone;
        this.houseType = houseType;
        this.innerCircleBackground = innerCircleBackground;
        this.planetIconColor = planetIconColor;
        this.signIconColor = signIconColor;
        this.signBackground = signBackground;
        this.chartSize = chartSize;
        this.imageType = imageType;
    }
}
