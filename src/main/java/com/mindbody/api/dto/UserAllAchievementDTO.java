package com.mindbody.api.dto;

import com.mindbody.api.enums.AchievementCategoryType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserAllAchievementDTO {
    private Long userAchievementId;
    private String setName;
    private String medalName;
    private String title;
    private String activityDetails;
    private String border;
    private int pointsEarned;
    private int wxpEarned;
    private AchievementCategoryType achievementCategoryType;
    private Long userId;
    private boolean isClaimed;
    private String subCategoryType;
    private Long achievementId;

    public UserAllAchievementDTO(Long userAchievementId,
                                 String setName,
                                 AchievementCategoryType achievementCategoryType,
                                 String subCategoryType,
                                 String medalName,
                                 String title,
                                 String activityDetails,
                                 String border,
                                 int pointsEarned,
                                 int wxpEarned,
                                 boolean isClaimed,
                                 Long userId,
                                 Long achievementId
    ) {
        this.userAchievementId = userAchievementId;
        this.setName = setName;
        this.medalName = medalName;
        this.title = title;
        this.activityDetails = activityDetails;
        this.border = border;
        this.pointsEarned = pointsEarned;
        this.wxpEarned = wxpEarned;
        this.achievementCategoryType = achievementCategoryType;
        this.userId = userId;
        this.isClaimed = isClaimed;
        this.subCategoryType = subCategoryType;
        this.achievementId = achievementId;
    }
}