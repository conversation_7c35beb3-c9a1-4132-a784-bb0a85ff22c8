package com.mindbody.api.dto;

import com.mindbody.api.dto.achievement.AchievementCategoryListReqDTO;
import com.mindbody.api.enums.AchievementActivityType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SubmitUserAchievementReqDTO {
    private AchievementActivityType achievementActivityType;
    private Long userId;
}
