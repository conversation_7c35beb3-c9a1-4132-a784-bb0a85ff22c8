package com.mindbody.api.dto;

import com.mindbody.api.filter.Page;
import com.mindbody.api.filter.SortBy;
import com.mindbody.api.validator.ValueOfEnum;
import com.mindbody.api.enums.RewardType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class RewardListReqDTO extends RewardListBaseReqDTO {

    @NotNull(message = "user_id_required")
    private Long userId;

    @ValueOfEnum(enumClass = RewardType.class, message = "invalid_reward_type")
    private String rewardType;

    @Valid
    private Page page;

    private SortBy sortBy;

    private String queryToSearch;
} 