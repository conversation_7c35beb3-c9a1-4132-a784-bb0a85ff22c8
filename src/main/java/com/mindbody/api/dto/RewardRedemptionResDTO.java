package com.mindbody.api.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RewardRedemptionResDTO {
    private String rewardTitle;
    private Integer remainingPoints;
    private String couponCode; // Will be populated only for TEN_PERCENT_DISCOUNT_MBW_STORE rewards
} 