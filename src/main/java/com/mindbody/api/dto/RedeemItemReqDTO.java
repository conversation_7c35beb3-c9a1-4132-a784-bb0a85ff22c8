package com.mindbody.api.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class RedeemItemReqDTO {
    @NotNull(message = "user_id_required")
    private Long userId;

    @NotNull(message = "item_id_required")
    private Long itemId;

    @NotNull(message = "item_type_required")
    @Pattern(regexp = "^(AUDIO|REWARD)$", message = "invalid_item_type")
    private String itemType;
} 