package com.mindbody.api.validator;


import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

public class ValueOfEnumValidator implements ConstraintValidator<ValueOfEnum, String> {

    private Set<String> acceptedValues;

    @Override
    public void initialize(ValueOfEnum annotation) {
        // Collect all the enum values in lowercase (to handle case-insensitive match)
        acceptedValues = Arrays.stream(annotation.enumClass().getEnumConstants())
                .map(e -> e.name().toLowerCase())  // Normalize the names to lowercase
                .collect(Collectors.toSet());
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // Can be valid if null, depending on your requirements
        }
        // Check if the provided value (converted to lowercase) is in the set of accepted enum values
        return acceptedValues.contains(value.toLowerCase());
    }
}
