package com.mindbody.api.enums;

public enum NotificationType {

    NONE,

    <PERSON><PERSON><PERSON><PERSON>,

    S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,

    MIND_ACHIEVEMENT,

    BODY_ACHIEVEMENT,

    WAR<PERSON>OR_ACHIEVEMENT_EMPOWERMENT,

    WAR<PERSON><PERSON>_ACHIEVEMENT_CONNECTION,

    <PERSON><PERSON><PERSON><PERSON>_<PERSON>HIEVEMENT_ASTROLOGY,

    B<PERSON><PERSON><PERSON><PERSON>,

    <PERSON><PERSON><PERSON><PERSON>_READING,

    NEW_WORKOUT_PLAN,

    MIND_SLEEP,

    MIND_MEDITATION,

    MIND_R<PERSON>EWA<PERSON>,

    BODY_STRENGTH,

    BODY_FLEXIBILITY,

    BODY_ENDURANCE,

    WARRIOR_ASTROLOGY,

    WAR<PERSON><PERSON>_EMPOWERMENT,

    WAR<PERSON><PERSON>_CONNECTION,

    <PERSON><PERSON><PERSON><PERSON>OA<PERSON>,

    MONTHLY_READING,

    EXCLUSIVE_CONTENT_UNLOCKED,

    REDEE<PERSON>_ITEM,
    REFERRAL_USED

}
