package com.mindbody.api.model;

import com.mindbody.api.enums.DeviceType;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Entity to track user devices and IP addresses for referral abuse prevention
 */
@Entity
@Table(name = FieldConstant.CollectionName.USER_DEVICE)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EntityUserDevice extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userDeviceId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", insertable = false, updatable = false)
    private EntityUser entityUser;

    @Column(name = "userId")
    private Long userId;

    private String deviceUniqueId;

    private String ipAddress;

    @Enumerated(EnumType.STRING)
    private DeviceType deviceType;

    @Builder.Default
    private boolean isDeleted = DefaultValue.FALSE;

    @Builder.Default
    private boolean isActive = DefaultValue.TRUE;

    // Flag to indicate if this device was associated with a deleted account
    @Builder.Default
    private boolean wasDeletedAccount = DefaultValue.FALSE;
}
