package com.mindbody.api.model;

import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.WORKOUT_PLAN_EXERCISE)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityWorkoutPlanExercise extends DateAudit{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long workoutPlanExerciseId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workoutPlanId", unique = true)
    private EntityWorkoutPlan entityWorkoutPlan;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "exerciseId", unique = true)
    private EntityExercise entityExercise;

    @Column(name = "workoutPlanId", insertable = false, updatable = false)
    private Long workoutPlanId;

    @Column(name = "exerciseId", insertable = false, updatable = false)
    private Long exerciseId;

    @Column(name = "exerciseDuration")
    private Integer exerciseDuration;

}
