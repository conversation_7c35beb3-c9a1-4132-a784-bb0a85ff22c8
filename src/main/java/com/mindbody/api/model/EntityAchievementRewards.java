package com.mindbody.api.model;

import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.ACHIEVEMENT_REWARDS)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityAchievementRewards extends DateAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long achievementRewardsId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "achievementMedalId")
    private EntityAchievementMedals entityAchievementMedals;

    private Integer points;

    private Integer wxp;

}
