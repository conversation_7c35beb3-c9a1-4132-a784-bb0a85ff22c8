package com.mindbody.api.model;

import com.mindbody.api.enums.MediaType;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.RANDOM_MEDIA_TRACKER)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityRandomMediaTracker extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", unique = true)
    private EntityUser entityUser;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;

    @Enumerated(EnumType.STRING)
    @Column(name = "media_type", columnDefinition = "VARCHAR(255)")
    private MediaType mediaType;

    private int mediaIndex;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;

}
