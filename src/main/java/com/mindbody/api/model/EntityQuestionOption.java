package com.mindbody.api.model;


import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.COLLECTION_QUESTION_OPTION)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityQuestionOption extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long optionId;

    private String optionName;

    private String optionImage;

    private Integer orderNo;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "questionId", unique = true)
    private EntityQuestion entityQuestion;

    @Column(name = "questionId", insertable = false, updatable = false)
    private Long questionId;

}
