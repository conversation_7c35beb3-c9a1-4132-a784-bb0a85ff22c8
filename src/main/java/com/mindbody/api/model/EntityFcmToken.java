package com.mindbody.api.model;


import com.mindbody.api.enums.DeviceType;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> on 02/08/2023
 */
@Entity
@Table(name = FieldConstant.CollectionName.FCM_TOKEN)
@Getter
@Setter
@NoArgsConstructor
public class EntityFcmToken extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long fcmTokenId;

    @Enumerated(EnumType.STRING)
    @Column(name = "device_type", columnDefinition = "VARCHAR(255)")
    private DeviceType deviceType;

    private String deviceUniqueId;

    private String fcmToken;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", unique = true)
    private EntityUser entityUser;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;


    public EntityFcmToken(DeviceType deviceType, String deviceUniqueId, String fcmToken, EntityUser entityUser, Long userId) {
        this.deviceType = deviceType;
        this.deviceUniqueId = deviceUniqueId;
        this.fcmToken = fcmToken;
        this.entityUser = entityUser;
        this.userId = userId;
    }
}
