package com.mindbody.api.model;


import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = FieldConstant.CollectionName.AUDIO)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityAudio extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long audioId;

    private String title;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "audio_playlist_type", columnDefinition = "VARCHAR(255)")
    private AudioPlaylistType audioPlaylistType;

    private String thumbnailImage;

    private String audioFile;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audioPlaylistId", unique = true)
    private EntityAudioPlaylist entityAudioPlaylist;

    @Column(name = "audioPlaylistId", insertable = false, updatable = false)
    private Long audioPlaylistId;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;

    @OneToMany(mappedBy = "entityAudio", cascade = CascadeType.ALL)
    private List<EntityAudioZodiacSign> entityAudioZodiacSigns = new ArrayList<>();

    private boolean isExclusive = DefaultValue.FALSE;

    private boolean isRedeemable = DefaultValue.FALSE;

    private Integer points = 0;

}
