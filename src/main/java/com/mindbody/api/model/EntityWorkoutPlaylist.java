package com.mindbody.api.model;

import com.mindbody.api.enums.WorkoutPlaylistType;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.WORKOUT_PLAYLIST)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityWorkoutPlaylist extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long workoutPlaylistId;

    private String workoutPlaylistName;

    @Enumerated(EnumType.STRING)
    @Column(name = "workout_playlist_type", columnDefinition = "VARCHAR(255)")
    private WorkoutPlaylistType workoutPlaylistType;

    private String workoutPlaylistImage;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;



}
