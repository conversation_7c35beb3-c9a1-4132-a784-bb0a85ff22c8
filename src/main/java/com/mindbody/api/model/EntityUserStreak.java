package com.mindbody.api.model;


import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Entity
@Table(name = FieldConstant.CollectionName.COLLECTION_USER_STREAK)
@Getter
@Setter
public class EntityUserStreak extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userStreakId;

    private Integer streakCount = 0;

    private Integer totalCount = 0;

    private LocalDate firstStreakDate;

    private LocalDate lastStreakDate;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", unique = true)
    private EntityUser entityUser;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;
}
