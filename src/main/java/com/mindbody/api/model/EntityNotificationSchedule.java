package com.mindbody.api.model;

import com.mindbody.api.enums.NotificationType;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.*;
import java.time.LocalDateTime;

@Entity
@Table(name = FieldConstant.CollectionName.NOTIFICATION_SCHEDULE)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EntityNotificationSchedule extends DateAudit{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String title;
    private String content;
    private Long userId;
    private LocalDateTime scheduledTime;
    private boolean sent;
    @Enumerated(EnumType.STRING)
    @Column(name = "notification_type", columnDefinition = "VARCHAR(255)")
    private NotificationType notificationType;
    private String topicName;

    public EntityNotificationSchedule(String title, String content, Long userId, LocalDateTime scheduledTime, boolean sent, NotificationType notificationType, String topicName) {
        this.title = title;
        this.content = content;
        this.userId = userId;
        this.scheduledTime = scheduledTime;
        this.sent = sent;
        this.notificationType = notificationType;
        this.topicName = topicName;
    }
}
