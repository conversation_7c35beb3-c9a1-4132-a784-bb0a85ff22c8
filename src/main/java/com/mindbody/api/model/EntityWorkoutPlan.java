package com.mindbody.api.model;

import com.mindbody.api.enums.DifficultyLevel;
import com.mindbody.api.enums.WorkoutPlanType;
import com.mindbody.api.enums.WorkoutPlaylistType;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = FieldConstant.CollectionName.WORKOUT_PLAN)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityWorkoutPlan extends DateAudit{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long workoutPlanId;

    private String workoutPlanTitle;

    @Column(name = "workout_plan_description", columnDefinition = "TEXT")
    private String workoutPlanDescription;

    private String equipment;

    @Enumerated(EnumType.STRING)
    @Column(name = "workout_plan_type", columnDefinition = "VARCHAR(255)")
    private WorkoutPlanType workoutPlanType;

    @Column(name = "workoutPlanDuration")
    private Integer workoutPlanDuration;

    @Enumerated(EnumType.STRING)
    @Column(name = "workout_playlist_type", columnDefinition = "VARCHAR(255)")
    private WorkoutPlaylistType workoutPlaylistType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workoutPlaylistId", unique = true)
    private EntityWorkoutPlaylist entityWorkoutPlaylist;

    @Column(name = "workoutPlaylistId", insertable = false, updatable = false)
    private Long workoutPlaylistId;

    @Enumerated(EnumType.STRING)
    @Column(name = "difficulty_level", columnDefinition = "VARCHAR(255)")
    private DifficultyLevel difficultyLevel;

    private String workoutVideo;

    private String workoutVideoThumbnailImage;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;

    @OneToMany(mappedBy = "entityWorkoutPlan", cascade = CascadeType.ALL)
    private List<EntityWorkoutPlanExercise> entityWorkoutPlanExerciseList;

    @OneToMany(mappedBy = "entityWorkoutPlan", cascade = CascadeType.ALL)
    private List<EntityWorkoutPlanZodiacSign> entityWorkoutPlanZodiacSigns = new ArrayList<>();

}
