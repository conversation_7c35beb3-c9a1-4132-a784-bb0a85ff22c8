package com.mindbody.api.model;

import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.COLLECTION_FAVORITE_WORKOUT_PLAN)
@Getter
@Setter
public class EntityFavoriteWorkoutPlan extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long favoriteWorkoutPlanId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", unique = true)
    private EntityUser entityUser;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workoutPlanId", unique = true)
    private EntityWorkoutPlan entityWorkoutPlan;

    @Column(name = "workoutPlanId", insertable = false, updatable = false)
    private Long workoutPlanId;

    private boolean isFavorite = DefaultValue.FALSE;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;
}
