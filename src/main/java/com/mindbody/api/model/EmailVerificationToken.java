package com.mindbody.api.model;

import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Entity
@Table(name = FieldConstant.CollectionName.COLLECTION_EMAIL_VERIFICATION_TOKEN)
@Getter
@Setter
public class EmailVerificationToken extends DateAudit{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long emailVerificationId;

    private String verificationToken;

    private Date expirationTime;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "admin_id", unique = true)
    private EntityAdmin entityAdmin;

    @Column(name = "admin_id", insertable = false, updatable = false)
    private Long adminId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", unique = true)
    private EntityUser entityUser;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;


}
