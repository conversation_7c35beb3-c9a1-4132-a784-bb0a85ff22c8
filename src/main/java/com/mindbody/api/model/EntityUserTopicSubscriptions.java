package com.mindbody.api.model;

import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.USER_TOPIC_SUBSCRIPTIONS)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityUserTopicSubscriptions extends DateAudit{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userTopicSubscriptionsId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId")
    private EntityUser entityUser;

    private String topicName;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;


}
