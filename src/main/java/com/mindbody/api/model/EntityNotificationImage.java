package com.mindbody.api.model;

import com.mindbody.api.enums.NotificationType;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.NOTIFICATION_IMAGE)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityNotificationImage extends DateAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long notificationImageId;

    @Column(name = "image")
    private String image;

    @Enumerated(EnumType.STRING)
    @Column(name = "notification_type", columnDefinition = "VARCHAR(255)")
    private NotificationType notificationType;

}
