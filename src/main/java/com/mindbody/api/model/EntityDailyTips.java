package com.mindbody.api.model;

import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.DAILY_TIPS)
@Getter
@Setter
@NoArgsConstructor
public class EntityDailyTips extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long dailyTipsId;

    @Enumerated(EnumType.STRING)
    private ZodiacSignType zodiacSign;

    @Column(name = "title", columnDefinition = "TEXT")
    private String title;

    private boolean isRead = DefaultValue.FALSE;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;

    public EntityDailyTips(ZodiacSignType zodiacSign, String title) {
        this.zodiacSign = zodiacSign;
        this.title = title;
    }
}
