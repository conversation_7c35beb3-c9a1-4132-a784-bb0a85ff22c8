package com.mindbody.api.model;

import com.mindbody.api.enums.AudioPlaylistType;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.AUDIO_PLAYLIST)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityAudioPlaylist extends DateAudit{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long audioPlaylistId;

    private String audioPlaylistName;

    private String audioPlaylistImage;

    @Enumerated(EnumType.STRING)
    @Column(name = "audio_playlist_type", columnDefinition = "VARCHAR(255)")
    private AudioPlaylistType audioPlaylistType;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;
}
