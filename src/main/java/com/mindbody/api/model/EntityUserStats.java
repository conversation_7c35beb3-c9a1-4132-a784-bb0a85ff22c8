package com.mindbody.api.model;

import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = FieldConstant.CollectionName.CATEGORY_USER_STATS)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityUserStats extends DateAudit{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long categoryUserStatsId;
    private Long userId;
    private Long mindCategoryTimeSpent;
    private Long bodyCategoryTimeSpent;
    private Long wxpEarned;
    private LocalDateTime date;
    private boolean isDeleted = DefaultValue.FALSE;
    private boolean isActive = DefaultValue.TRUE;

}
