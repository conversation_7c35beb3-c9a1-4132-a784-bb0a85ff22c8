package com.mindbody.api.model;

import com.mindbody.api.enums.AchievementActivityType;
import com.mindbody.api.enums.AchievementCategoryType;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Entity
@Table(name = FieldConstant.CollectionName.USER_ACHIEVEMENTS)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityUserAchievements extends DateAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userAchievementsId;

    @Enumerated(EnumType.STRING)
    @Column(name = "achievement_category_type", columnDefinition = "VARCHAR(255)")
    private AchievementCategoryType achievementCategoryType;

    private String mindSubCategoryType;

    private String bodySubCategoryType;

    private String warriorSubCategoryType;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "achievementTitleId")
    private EntityAchievementTitle achievementTitleId;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "achievementBorderId")
    private EntityAchievementBorder achievementBorderId;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "achievementMedalId")
    private EntityAchievementMedals achievementMedalId;

    private Long userId;

    private String activityDetails;

    @Enumerated(EnumType.STRING)
    @Column(name = "achievement_activity_type", columnDefinition = "VARCHAR(255)")
    private AchievementActivityType achievementActivityType;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "achievementsId")
    private EntityAchievements entityAchievements;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audioPlaylistId")
    private EntityAudioPlaylist entityAudioPlaylist;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workoutPlaylistId")
    private EntityWorkoutPlaylist entityWorkoutPlaylist;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "warriorSetId")
    private EntityWarriorSet entityWarriorSet;

    private Integer pointsEarned;

    private Integer wxpEarned;

    private Date acquiredDate;

    private boolean isClaimed = DefaultValue.FALSE;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;


}
