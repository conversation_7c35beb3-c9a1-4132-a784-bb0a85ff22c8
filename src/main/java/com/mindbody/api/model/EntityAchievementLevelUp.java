package com.mindbody.api.model;

import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.ACHIEVEMENT_LEVEL_UP)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityAchievementLevelUp extends DateAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long achievementLevelUpId;

    private Integer levelUp;

    private Integer wxpNeeded;

}
