package com.mindbody.api.model;

import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Entity to track audios unlocked by users
 */
@Entity
@Table(name = FieldConstant.CollectionName.USER_UNLOCKED_AUDIO)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityUserUnlockedAudio extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userUnlockedAudioId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId")
    private EntityUser entityUser;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audioId")
    private EntityAudio entityAudio;

    @Column(name = "audioId", insertable = false, updatable = false)
    private Long audioId;

    private boolean isUnlocked = DefaultValue.TRUE;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;
}
