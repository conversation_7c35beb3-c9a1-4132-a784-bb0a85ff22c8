package com.mindbody.api.model;

import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.WORKOUT_PLAN_ZODIAC_SIGN)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityWorkoutPlanZodiacSign extends DateAudit{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long workoutPlanZodiacSignId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workoutPlanId")
    private EntityWorkoutPlan entityWorkoutPlan;

    @Enumerated(EnumType.STRING)
    @Column(name = "zodiac_sign", columnDefinition = "VARCHAR(255)")
    private ZodiacSignType zodiacSign;
}
