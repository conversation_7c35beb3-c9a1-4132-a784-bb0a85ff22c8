package com.mindbody.api.model;


import com.mindbody.api.enums.GenderType;
import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Fetch;

import java.time.LocalDateTime;

@Entity
@Table(name = FieldConstant.CollectionName.COLLECTION_USER_INFO)
@Getter
@Setter
@FieldNameConstants
public class EntityUserInfo extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userInfoId;

    private String name;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", unique = true)
    private EntityUser entityUser;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;

    private LocalDateTime dateOfBirth;

    private String placeOfBirth;

    private String profileImage;

    @Enumerated(EnumType.STRING)
    private GenderType genderType;

    @Enumerated(EnumType.STRING)
    private ZodiacSignType zodiacSign;

    private String timezone;

    private Double latitude;

    private Double longitude;

    private String wheelChartUrl;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "titleAchievementId")
    private EntityAchievements titleAchievementId;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "borderAchievementId")
    private EntityAchievements borderAchievementId;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "medalAchievementId")
    private EntityAchievements medalAchievementId;

    private String facebookAccountName;

    private String googleAccountName;

    private String appleAccountName;

    private String themeMode = DefaultValue.DEFAULT_THEME_MODE;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;
}
