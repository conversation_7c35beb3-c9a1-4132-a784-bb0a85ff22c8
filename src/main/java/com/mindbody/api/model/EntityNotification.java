package com.mindbody.api.model;

import com.mindbody.api.enums.NotificationType;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.NOTIFICATION)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityNotification extends DateAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long notificationId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId")
    private EntityUser entityUser;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;

    @Column(name = "title")
    private String title;

    @Column(columnDefinition = "LONGTEXT")
    private String message;

    @Column(name = "is_notification_read", columnDefinition = "boolean default false")
    private Boolean isNotificationRead = false;

    @Enumerated(EnumType.STRING)
    @Column(name = "notification_type", columnDefinition = "VARCHAR(255)")
    private NotificationType notificationType;

    @Column(name = "topic_name")
    private String topicName;

    @Column(name = "image")
    private String image;
}
