package com.mindbody.api.model;

import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Entity to track audios redeemed by users
 */
@Entity
@Table(name = FieldConstant.CollectionName.USER_REDEEMED_AUDIO)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityUserRedeemedAudio extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userRedeemedAudioId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private EntityUser entityUser;

    @Column(name = "user_id", insertable = false, updatable = false)
    private Long userId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audioId")
    private EntityAudio entityAudio;

    @Column(name = "audioId", insertable = false, updatable = false)
    private Long audioId;

    private boolean isRedeemed = DefaultValue.TRUE;

    private Integer pointsSpent;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;
} 