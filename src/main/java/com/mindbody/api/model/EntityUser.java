package com.mindbody.api.model;


import com.mindbody.api.dto.notification.FilterUsersForNotificationResDTO;
import com.mindbody.api.enums.*;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = FieldConstant.CollectionName.COLLECTION_USER)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@NamedNativeQuery(
        name = "EntityUser.listUsersBasedOnGenderAndAgeFilters",
        query = """
                SELECT u.user_id AS userId, 
                       u.email, 
                       ui.name, 
                       ui.gender_type AS genderType,
                       ui.zodiac_sign As zodiacSignType,
                       TIMESTAMPDIFF(YEAR, DATE(ui.date_of_birth), :currentDate) AS age,
                       u.created_at AS createdAt
                FROM `user` u
                LEFT JOIN user_info ui ON u.user_id = ui.user_id
                WHERE (:checkIsGenderType = false OR ui.gender_type = :genderType)
                AND TIMESTAMPDIFF(YEAR, DATE(ui.date_of_birth), :currentDate) BETWEEN :startAge AND :endAge
                AND (LOWER(u.email) LIKE LOWER(CONCAT('%', :queryToSearch, '%')) 
                     OR LOWER(ui.name) LIKE LOWER(CONCAT('%', :queryToSearch, '%')))
                AND u.user_type = 'NORMAL' and u.is_deleted = false
                """,
        resultSetMapping = "FilterUsersForNotificationMapping"
)

@NamedNativeQuery(
        name = "EntityUser.listUsersBasedOnGenderAndZodiacSignFilters",
        query = """
                SELECT u.user_id AS userId, 
                       u.email, 
                       ui.name, 
                       ui.gender_type AS genderType,
                       ui.zodiac_sign As zodiacSignType,
                       TIMESTAMPDIFF(YEAR, DATE(ui.date_of_birth), :currentDate) AS age,
                       u.created_at AS createdAt
                FROM `user` u
                LEFT JOIN user_info ui ON u.user_id = ui.user_id
                WHERE (:checkIsGenderType = false OR ui.gender_type = :genderType)
                AND (:checkIsZodiacSignType = false OR ui.zodiac_sign = :zodiacSignType)
                AND (LOWER(u.email) LIKE LOWER(CONCAT('%', :queryToSearch, '%')) 
                     OR LOWER(ui.name) LIKE LOWER(CONCAT('%', :queryToSearch, '%')))
                AND u.user_type = 'NORMAL' and u.is_deleted = false
                """,
        resultSetMapping = "FilterUsersForNotificationMapping"
)

@NamedNativeQuery(
        name = "EntityUser.listUsersBasedOnZodiacSignAndAgeFilters",
        query = """
                SELECT u.user_id AS userId, 
                       u.email, 
                       ui.name, 
                       ui.gender_type AS genderType,
                       ui.zodiac_sign As zodiacSignType,
                       TIMESTAMPDIFF(YEAR, DATE(ui.date_of_birth), :currentDate) AS age,
                       u.created_at AS createdAt
                FROM `user` u
                LEFT JOIN user_info ui ON u.user_id = ui.user_id
                WHERE (:checkIsZodiacSignType = false OR ui.zodiac_sign = :zodiacSignType)
                AND TIMESTAMPDIFF(YEAR, DATE(ui.date_of_birth), :currentDate) BETWEEN :startAge AND :endAge
                AND (LOWER(u.email) LIKE LOWER(CONCAT('%', :queryToSearch, '%')) 
                     OR LOWER(ui.name) LIKE LOWER(CONCAT('%', :queryToSearch, '%')))
                AND u.user_type = 'NORMAL' and u.is_deleted = false
                """,
        resultSetMapping = "FilterUsersForNotificationMapping"
)

@NamedNativeQuery(
        name = "EntityUser.listUsersBasedOnGenderAndAgeAndZodiacSignFilters",
        query = """
                SELECT u.user_id AS userId, 
                       u.email, 
                       ui.name, 
                       ui.gender_type AS genderType,
                       ui.zodiac_sign As zodiacSignType,
                       TIMESTAMPDIFF(YEAR, DATE(ui.date_of_birth), :currentDate) AS age,
                       u.created_at AS createdAt
                FROM `user` u
                LEFT JOIN user_info ui ON u.user_id = ui.user_id
                WHERE (:checkIsGenderType = false OR ui.gender_type = :genderType)
                AND (:checkIsZodiacSignType = false OR ui.zodiac_sign = :zodiacSignType)
                AND TIMESTAMPDIFF(YEAR, DATE(ui.date_of_birth), :currentDate) BETWEEN :startAge AND :endAge
                AND (LOWER(u.email) LIKE LOWER(CONCAT('%', :queryToSearch, '%')) 
                     OR LOWER(ui.name) LIKE LOWER(CONCAT('%', :queryToSearch, '%')))
                AND u.user_type = 'NORMAL' and u.is_deleted = false
                """,
        resultSetMapping = "FilterUsersForNotificationMapping"
)

@NamedNativeQuery(
        name = "EntityUser.listUsersBasedOnGenderFilter",
        query = """
                SELECT u.user_id AS userId, 
                       u.email, 
                       ui.name, 
                       ui.gender_type AS genderType,
                       ui.zodiac_sign As zodiacSignType,
                       TIMESTAMPDIFF(YEAR, DATE(ui.date_of_birth), :currentDate) AS age,
                       u.created_at AS createdAt
                FROM `user` u
                LEFT JOIN user_info ui ON u.user_id = ui.user_id
                WHERE u.user_type = 'NORMAL' and u.is_deleted = false
                AND ui.gender_type=:genderType
                AND (
                    (:queryToSearch IS NULL OR :queryToSearch = '')
                    OR
                    (LOWER(u.email) LIKE LOWER(CONCAT('%', :queryToSearch, '%'))
                    OR LOWER(ui.name) LIKE LOWER(CONCAT('%', :queryToSearch, '%')))
                )
                """,
        resultSetMapping = "FilterUsersForNotificationMapping"
)

@NamedNativeQuery(
        name = "EntityUser.listUsersBasedOnAgeFilter",
        query = """
                SELECT u.user_id AS userId, 
                       u.email, 
                       ui.name, 
                       ui.gender_type AS genderType,
                       ui.zodiac_sign As zodiacSignType,
                       TIMESTAMPDIFF(YEAR, DATE(ui.date_of_birth), :currentDate) AS age,
                       u.created_at AS createdAt
                FROM `user` u
                LEFT JOIN user_info ui ON u.user_id = ui.user_id
                WHERE TIMESTAMPDIFF(YEAR, DATE(ui.date_of_birth), :currentDate) BETWEEN :startAge AND :endAge
                AND (LOWER(u.email) LIKE LOWER(CONCAT('%', :queryToSearch, '%')) 
                     OR LOWER(ui.name) LIKE LOWER(CONCAT('%', :queryToSearch, '%')))
                AND u.user_type = 'NORMAL' and u.is_deleted = false
                """,
        resultSetMapping = "FilterUsersForNotificationMapping"
)

@NamedNativeQuery(
        name = "EntityUser.listUsersBasedOnZodiacSignFilter",
        query = """
                SELECT u.user_id AS userId, 
                       u.email, 
                       ui.name, 
                       ui.gender_type AS genderType,
                       ui.zodiac_sign As zodiacSignType,
                       TIMESTAMPDIFF(YEAR, DATE(ui.date_of_birth), :currentDate) AS age,
                       u.created_at AS createdAt
                FROM `user` u
                LEFT JOIN user_info ui ON u.user_id = ui.user_id
                WHERE (:checkIsZodiacSignType = false OR ui.zodiac_sign = :zodiacSignType)
                AND (LOWER(u.email) LIKE LOWER(CONCAT('%', :queryToSearch, '%')) 
                OR LOWER(ui.name) LIKE LOWER(CONCAT('%', :queryToSearch, '%')))
                AND u.user_type = 'NORMAL' and u.is_deleted = false
                """,
        resultSetMapping = "FilterUsersForNotificationMapping"
)

@NamedNativeQuery(
        name = "EntityUser.listAllUsersForNotifications",
        query = """
                SELECT u.user_id AS userId, 
                       u.email, 
                       ui.name, 
                       ui.gender_type AS genderType,
                       ui.zodiac_sign As zodiacSignType,
                       TIMESTAMPDIFF(YEAR, DATE(ui.date_of_birth), :currentDate) AS age,
                       u.created_at AS createdAt
                FROM `user` u
                LEFT JOIN user_info ui ON u.user_id = ui.user_id
                WHERE 
                (LOWER(u.email) LIKE LOWER(CONCAT('%', :queryToSearch, '%')) 
                OR LOWER(ui.name) LIKE LOWER(CONCAT('%', :queryToSearch, '%')))
                AND u.user_type = 'NORMAL' and u.is_deleted = false
                """,
        resultSetMapping = "FilterUsersForNotificationMapping"
)
@SqlResultSetMapping(
        name = "FilterUsersForNotificationMapping",
        classes = @ConstructorResult(
                targetClass = FilterUsersForNotificationResDTO.class,
                columns = {
                        @ColumnResult(name = "userId", type = Long.class),
                        @ColumnResult(name = "email", type = String.class),
                        @ColumnResult(name = "name", type = String.class),
                        @ColumnResult(name = "genderType", type = GenderType.class),
                        @ColumnResult(name = "zodiacSignType", type = String.class),
                        @ColumnResult(name = "age", type = Integer.class),
                        @ColumnResult(name = "createdAt", type= LocalDateTime.class)
                }
        )
)
public class EntityUser extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userId;

    private String email;

    private String password;

    private String countryCode;

    private String phoneNumber;

    @Enumerated(EnumType.STRING)
    private RoleType roleType;

    @Enumerated(EnumType.STRING)
    private UserType userType;

    @Enumerated(EnumType.STRING)
    private AccountStatus accountStatus;

    @Enumerated(EnumType.STRING)
    private RegisterType registerType;

    private String referralCode;

    private boolean isEmailRegistered = Boolean.FALSE;

    private boolean isProfileCompleted = Boolean.FALSE;

    private String facebookEmailId;

    private String facebookSocialMediaId;

    private String googleEmailId;

    private String googleSocialMediaId;

    private String appleEmailId;

    private String appleSocialMediaId;

//    private String socialMediaId;

//    private boolean isReactivationRequestPending = Boolean.FALSE;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;


}
