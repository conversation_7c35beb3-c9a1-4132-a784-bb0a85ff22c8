package com.mindbody.api.model;

import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.FEEDBACK)
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EntityFeedback extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "feedback_id")
    private Long feedbackId;

    @Column(name = "name")
    private String name;

    @Column(name = "userId")
    private Long userId;

    @Column(name = "feedback", nullable = false)
    private String feedback;

    @Column(name = "contact_detail")
    private String contactDetail;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", referencedColumnName = "userId", insertable = false, updatable = false)
    private EntityUser entityUser;
} 