package com.mindbody.api.model;


import com.mindbody.api.enums.ActivityFactorType;
import com.mindbody.api.enums.GenderType;
import com.mindbody.api.enums.MeasurementUnitType;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.COLLECTION_USER_HEALTH_INFO)
@Getter
@Setter
public class EntityUserHealthInfo extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userHealthInfoId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", unique = true)
    private EntityUser entityUser;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;

    private double weight;
    
    private double convertedWeight;
    
    @Enumerated(EnumType.STRING)
    private MeasurementUnitType weightUnit;

    private double height;
    
    private double convertedHeight;
    
    @Enumerated(EnumType.STRING)
    private MeasurementUnitType heightUnit;

    private int age;

    @Enumerated(EnumType.STRING)
    @Column(name = "gender", columnDefinition = "VARCHAR(255)")
    private GenderType gender;

    private double bmi;

    private double bmr;

    private double protein;

    private double calories;

    private double carbs;

    private double fat;

    @Enumerated(EnumType.STRING)
    @Column(name = "activity_factor_type", columnDefinition = "VARCHAR(255)")
    private ActivityFactorType activityFactorType;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;
}
