package com.mindbody.api.model;


import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Entity
@Table(name = FieldConstant.CollectionName.EXERCISE)
@Getter
@Setter
@NoArgsConstructor
public class EntityExercise extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long exerciseId;

    private String title;

    @Column(columnDefinition = "TEXT")
    private String description;

    private String exerciseVideo;

    private String exerciseThumbnailImage;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;

    @OneToMany(mappedBy = "entityExercise", cascade = CascadeType.ALL)
    private List<EntityWorkoutPlanExercise> entityWorkoutPlanExerciseList;

}
