package com.mindbody.api.model;

import com.mindbody.api.enums.ZodiacSignType;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.AUDIO_ZODIAC_SIGN)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityAudioZodiacSign extends DateAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long audioZodiacSignId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audioId")
    private EntityAudio entityAudio;

    @Enumerated(EnumType.STRING)
    @Column(name = "zodiac_sign", columnDefinition = "VARCHAR(255)")
    private ZodiacSignType zodiacSign;
}
