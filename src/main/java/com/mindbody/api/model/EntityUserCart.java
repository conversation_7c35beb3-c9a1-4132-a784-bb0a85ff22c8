package com.mindbody.api.model;


import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.COLLECTION_USER_CART)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityUserCart extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userCartId;

    private String cartId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", unique = true)
    private EntityUser entityUser;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;
}
