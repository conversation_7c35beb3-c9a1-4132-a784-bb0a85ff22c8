package com.mindbody.api.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "user_redeemed_rewards")
@Getter
@Setter
@NoArgsConstructor
public class EntityUserRedeemedReward {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "redeemed_reward_id")
    private Long redeemedRewardId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "reward_id", nullable = false)
    private Long rewardId;

    @Column(name = "points_spent", nullable = false)
    private Integer pointsSpent;

    @Column(name = "redemption_status", nullable = false)
    private String redemptionStatus = "REDEEMED"; // REDEEMED, CANCELLED, FULFILLED, etc.

    @Column(name = "reward_metadata", columnDefinition = "TEXT")
    private String rewardMetadata;

    @Column(name = "is_active", nullable = false)
    private boolean isActive = true;

    @Column(name = "is_deleted", nullable = false)
    private boolean isDeleted = false;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "userId", insertable = false, updatable = false)
    private EntityUser entityUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reward_id", referencedColumnName = "reward_id", insertable = false, updatable = false)
    private EntityReward entityReward;
} 