package com.mindbody.api.model;


import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.COLLECTION_FAVORITE_AUDIO)
@Getter
@Setter
public class EntityFavoriteAudio extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long favoriteAudioId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", unique = true)
    private EntityUser entityUser;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audioId", unique = true)
    private EntityAudio entityAudio;

    @Column(name = "audioId", insertable = false, updatable = false)
    private Long audioId;

    private boolean isFavorite = DefaultValue.FALSE;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;

}
