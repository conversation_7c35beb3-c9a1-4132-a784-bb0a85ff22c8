package com.mindbody.api.model;

import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.COLLECTION_MAGAZINE)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityMagazine extends DateAudit{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long magazineId;

    private String title;

    private String subTitle;

    @Column(columnDefinition = "TEXT")
    private String description;

    private String magazineImage;

    private String magazineThumbnailImage;

    private String magazineLink;

    private boolean isActive = DefaultValue.TRUE;

    private boolean isDeleted = DefaultValue.FALSE;


}
