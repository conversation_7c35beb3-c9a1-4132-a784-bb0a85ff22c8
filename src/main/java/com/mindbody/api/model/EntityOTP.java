package com.mindbody.api.model;


import com.mindbody.api.enums.OtpModuleName;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Entity
@Table(name = FieldConstant.CollectionName.OTP)
@Getter
@Setter
@NoArgsConstructor
public class EntityOTP extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long otpId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", unique = true)
    private EntityUser entityUser;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;

    private String otp;

    private Date expiryDate;

    @Enumerated(EnumType.STRING)
    private OtpModuleName moduleName;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;


    public EntityOTP(EntityUser entityUser, Long userId, String otp, Date expiryDate, OtpModuleName moduleName) {
        this.entityUser = entityUser;
        this.userId = userId;
        this.otp = otp;
        this.expiryDate = expiryDate;
        this.moduleName = moduleName;
    }
}
