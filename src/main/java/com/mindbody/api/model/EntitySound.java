package com.mindbody.api.model;

import com.mindbody.api.enums.SoundType;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.SOUND)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntitySound extends DateAudit{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long soundId;

    private String title;

    @Enumerated(EnumType.STRING)
    @Column(name = "sound_type", columnDefinition = "VARCHAR(255)")
    private SoundType soundType;

    private String soundFile;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;
}
