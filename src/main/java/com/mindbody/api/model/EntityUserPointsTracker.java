package com.mindbody.api.model;

import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.USER_POINTS_TRACKER)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityUserPointsTracker extends DateAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userPointsTrackerId;
    private Long totalPoints;
    private Long totalWxp;
    private Long currentLevel;
    private Long userId;
    private boolean isDeleted = DefaultValue.FALSE;
    private boolean isActive = DefaultValue.TRUE;

}
