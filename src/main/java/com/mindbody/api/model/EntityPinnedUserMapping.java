package com.mindbody.api.model;

import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.PINNED_USER_MAPPING)
@Getter
@Setter
public class EntityPinnedUserMapping extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long pinnedUserMappingId;

    @Column(name = "pinned_user_id", nullable = false)
    private Long pinnedUserId;

    @Column(name = "for_user_id", nullable = false)
    private Long forUserId;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;

}
