package com.mindbody.api.model;

import com.mindbody.api.enums.*;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.ACHIEVEMENTS)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityAchievements extends DateAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long achievementsId;

    @Enumerated(EnumType.STRING)
    @Column(name = "achievement_category_type", columnDefinition = "VARCHAR(255)")
    private AchievementCategoryType achievementCategoryType;

    @Enumerated(EnumType.STRING)
    @Column(name = "mind_sub_category_type", columnDefinition = "VARCHAR(255)")
    private MindSubCategoryType mindSubCategoryType;

    @Enumerated(EnumType.STRING)
    @Column(name = "body_sub_category_type", columnDefinition = "VARCHAR(255)")
    private BodySubCategoryType bodySubCategoryType;

    @Enumerated(EnumType.STRING)
    @Column(name = "warrior_sub_category_type", columnDefinition = "VARCHAR(255)")
    private WarriorSubCategoryType warriorSubCategoryType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audioPlaylistId")
    private EntityAudioPlaylist entityAudioPlaylist;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workoutPlaylistId")
    private EntityWorkoutPlaylist entityWorkoutPlaylist;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "warriorSetId")
    private EntityWarriorSet entityWarriorSet;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "achievementTitleId")
    private EntityAchievementTitle entityAchievementTitle;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "achievementBorderId")
    private EntityAchievementBorder entityAchievementBorder;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "achievementMedalId",nullable = false)
    private EntityAchievementMedals entityAchievementMedals;

    private String activityDetails;

    @Enumerated(EnumType.STRING)
    @Column(name = "achievement_activity_type", columnDefinition = "LONGTEXT")
    private AchievementActivityType achievementActivityType;

    private String borderImage;

    private String badgeImage;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;
}
