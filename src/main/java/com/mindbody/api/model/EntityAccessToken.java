package com.mindbody.api.model;


import com.mindbody.api.enums.RoleType;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Entity
@Table(name = FieldConstant.CollectionName.COLLECTION_ACCESS_TOKEN)
@Getter
@Setter
@NoArgsConstructor
public class EntityAccessToken extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long tokenId;

    private Long userId;

    @Enumerated(EnumType.STRING)
    private RoleType roleType;

    @Column(name = "access_token", columnDefinition = "TEXT")
    private String accessToken;

    private String secretToken;


    public EntityAccessToken(Long userId, RoleType roleType, String accessToken, String secretToken) {
        this.userId = userId;
        this.roleType = roleType;
        this.accessToken = accessToken;
        this.secretToken = secretToken;
    }
}
