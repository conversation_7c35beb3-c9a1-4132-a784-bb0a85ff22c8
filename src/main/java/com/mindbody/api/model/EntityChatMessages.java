package com.mindbody.api.model;

import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = FieldConstant.CollectionName.CHAT_MESSAGES)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EntityChatMessages extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long messageId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId")
    private EntityUser entityUser;

    @Column(name = "userId", insertable = false, updatable = false)
    private Long userId;

    @Column(columnDefinition = "LONGTEXT")
    private String message;

    @Column(name = "category")
    private String category;

    @Column(name = "is_notification")
    private Boolean isNotification;

    @Column(name = "message_from", columnDefinition = "VARCHAR(255)")
    private String messageFrom;

    @Column(columnDefinition = "LONGTEXT")
    private String historyMessage;

    @Column(name = "image")
    private String image;

    @Column(name = "is_read")
    private boolean isRead;


}
