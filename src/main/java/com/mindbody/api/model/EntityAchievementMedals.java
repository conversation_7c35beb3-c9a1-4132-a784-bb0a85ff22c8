package com.mindbody.api.model;

import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.ACHIEVEMENT_MEDALS)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityAchievementMedals extends DateAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long achievementMedalId;

    private String medalName;

    private boolean isDeleted = DefaultValue.FALSE;

    private boolean isActive = DefaultValue.TRUE;

    @OneToOne(mappedBy = "entityAchievementMedals", fetch = FetchType.EAGER)
    private EntityAchievementRewards entityAchievementRewards;

}
