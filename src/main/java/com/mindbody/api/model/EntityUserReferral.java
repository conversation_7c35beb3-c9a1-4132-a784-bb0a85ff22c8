package com.mindbody.api.model;

import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = FieldConstant.CollectionName.COLLECTION_USER_REFERRAL)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EntityUserReferral extends DateAudit{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userReferralId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fromUserId", insertable=false, updatable=false)
    private EntityUser fromEntityUser;

    @Column(name = "fromUserId")
    private Long fromUserId;

    private String code;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "toUserId", insertable=false, updatable=false)
    private EntityUser toEntityUser;

    @Column(name = "toUserId")
    private Long toUserId;

    @Builder.Default
    private boolean isActive = DefaultValue.TRUE;

    @Builder.Default
    private boolean isDeleted = DefaultValue.FALSE;

}
