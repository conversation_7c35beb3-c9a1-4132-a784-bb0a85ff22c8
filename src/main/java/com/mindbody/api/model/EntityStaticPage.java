package com.mindbody.api.model;

import com.mindbody.api.enums.StaticPageType;
import com.mindbody.api.util.DefaultValue;
import com.mindbody.api.util.FieldConstant;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = FieldConstant.CollectionName.STATIC_PAGE)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EntityStaticPage extends DateAudit{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long staticPageId;

    @Enumerated(EnumType.STRING)
    @Column(name = "static_page_type", columnDefinition = "VARCHAR(255)")
    private StaticPageType staticPageType;

    @Column(columnDefinition = "LONGTEXT")
    private String staticPageContent;

    private boolean isDeleted = DefaultValue.FALSE;
}
