package com.mindbody.api.exception;

import org.springframework.util.StringUtils;


public class EntityNotFoundException extends EndUserException {

    public EntityNotFoundException(Class<?> clazz, String formattedParam) {
        super(EntityNotFoundException.generateMessage(clazz.getSimpleName(), formattedParam));
    }

    private static String generateMessage(String entity, String formattedParam) {
        return String.format("%s was not found for parameter(s) %s", StringUtils.capitalize(entity), formattedParam);
    }

    public EntityNotFoundException(String msg) {
        super(msg);
    }

}
