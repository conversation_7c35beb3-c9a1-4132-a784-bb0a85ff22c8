package com.mindbody.api.exception;

import org.springframework.util.StringUtils;

public class CommonException extends RuntimeException {

    public CommonException(Class<?> clazz, String formattedParam) {
        super(CommonException.generateMessage(clazz.getSimpleName(), formattedParam));
    }
    public CommonException(String msg) {
        super(msg);
    }

    public CommonException(Throwable t) {
        super(t);
    }

    private static String generateMessage(String entity, String formattedParam) {
        return String.format("%s was not found for parameter(s) %s", StringUtils.capitalize(entity), formattedParam);
    }

}
