package com.mindbody.api.exception;

import java.io.Serial;
import java.util.List;

public class ForbiddenException extends RuntimeException{

    @Serial
    private static final long serialVersionUID = 1L;

    private List<String> errors;

    private String[] params;


    public ForbiddenException(String message) {
        super(message);
    }

    public ForbiddenException(String message, String... params) {
        super(message);
        this.params = params;
    }

    public ForbiddenException(Exception e) {
        super(e);
    }


    /**
     * @return the errors
     */
    public List<String> getErrors() {
        return errors;
    }

    public Object[] getParams() {
        return params;
    }
}
