package com.mindbody.api.exception.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mindbody.api.base.ApiResponse;
import com.mindbody.api.exception.*;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.util.Constant;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.convert.ConversionFailedException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.security.InvalidParameterException;
import java.sql.SQLException;
import java.util.NoSuchElementException;


@Order(Ordered.HIGHEST_PRECEDENCE)
@ControllerAdvice()
public class RestExceptionHandler extends ResponseEntityExceptionHandler {

    static final Logger LOG = LoggerFactory.getLogger(RestExceptionHandler.class);
    static final String INTERNAL_SERVER_ERROR_MSG = "Something went wrong, Please try after sometime.";
    @Autowired
    protected MessageService messageService;
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        String error = "Malformed JSON request";
        return buildResponseEntity(HttpStatus.BAD_REQUEST, error);
    }

    private ResponseEntity<Object> buildResponseEntity(HttpStatus badRequest, String errorMessage) {
        LOG.error("apiError " + errorMessage);
        return new ResponseEntity<>(new ApiResponse<>(objectMapper.createObjectNode(), errorMessage, Constant.FAIL), badRequest);
    }

    @ExceptionHandler({BusinessValidationException.class})
    protected ResponseEntity<Object> handleBusinessValidation(BusinessValidationException ex, HttpServletRequest request) {
//        return buildResponseEntity(HttpStatus.OK, getMessage(ex.getMessage(), ex.getParams()));
        LOG.error("apiError " + getMessage(ex.getMessage(), ex.getParams()));
        return new ResponseEntity<>(new ApiResponse<>(null, getMessage(ex.getMessage(), ex.getParams()), Constant.FAIL), HttpStatus.OK);
    }

    @ExceptionHandler({ForbiddenException.class})
    protected ResponseEntity<Object> handleForbiddenException(ForbiddenException ex) {
        LOG.error("apiError " + getMessage(ex.getMessage(), ex.getParams()));
        return new ResponseEntity<>(new ApiResponse<>(null, getMessage(ex.getMessage(), ex.getParams()), Constant.FAIL), HttpStatus.FORBIDDEN);
    }

    private String getMessage(String key, Object[] params) {
        try {
            return messageService.getMessage(key, params);
        } catch (Exception e) {
            return key;
        }
    }

    @ExceptionHandler({BaseConversionException.class})
    protected ResponseEntity<Object> handleBaseModelConversion(BaseConversionException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.OK, ex.getMessage().split("\\(")[0]);
    }

    @ExceptionHandler({InvalidParameterException.class})
    protected ResponseEntity<Object> handleInvalidParameter(InvalidParameterException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.INTERNAL_SERVER_ERROR, INTERNAL_SERVER_ERROR_MSG);
    }

    @ExceptionHandler(EntityNotFoundException.class)
    protected ResponseEntity<Object> handleEntityNotFound(EntityNotFoundException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.OK, messageService.getMessage(ex.getMessage()));
    }

    @ExceptionHandler(ConvertorException.class)
    protected ResponseEntity<Object> handleEntityNotFound(ConvertorException ex, WebRequest request) {
        return buildResponseEntity(HttpStatus.OK, messageService.getMessage(ex.getMessage()));
    }

    @ExceptionHandler(NoSuchElementException.class)
    protected ResponseEntity<Object> handleNoSuchElementException(NoSuchElementException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.NOT_FOUND, messageService.getMessage(ex.getMessage()));
    }

    @ExceptionHandler(InvalidOldPasswordException.class)
    protected ResponseEntity<Object> handleInvalidOldPasswordException(InvalidOldPasswordException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.OK, messageService.getMessage(ex.getMessage()));
    }

    @ExceptionHandler(InvalidPasswordException.class)
    protected ResponseEntity<Object> handleInvalidPasswordException(InvalidPasswordException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.OK, messageService.getMessage(ex.getMessage()));
    }
    @ExceptionHandler({EndUserException.class})
    protected ResponseEntity<Object> handleEndUserException(EndUserException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.INTERNAL_SERVER_ERROR, messageService.getMessage(ex.getMessage()));
    }

    @ExceptionHandler({DataIntegrityViolationException.class})
    protected ResponseEntity<Object> handleEndUserException(DataIntegrityViolationException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.OK, messageService.getMessage(ex.getMessage()));
    }

    @ExceptionHandler({UnauthorizedException.class})
    protected ResponseEntity<Object> handleUnauthorizedException(UnauthorizedException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.UNAUTHORIZED, messageService.getMessage(ex.getMessage()));
    }

    @ExceptionHandler({InternalAuthenticationServiceException.class})
    protected ResponseEntity<Object> handleInternalAuthenticationServiceException(InternalAuthenticationServiceException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.UNAUTHORIZED, messageService.getMessage(ex.getMessage()));
    }

    @ExceptionHandler({SQLException.class})
    protected ResponseEntity<Object> handleSQLException(SQLException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.INTERNAL_SERVER_ERROR, messageService.getMessage(ex.getMessage()));
    }

    @ExceptionHandler({AuthenticationException.class})
    protected ResponseEntity<Object> handleAuthenticationException(AuthenticationException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.UNAUTHORIZED, messageService.getMessage("bad_credentials_error"));
    }

    @ExceptionHandler({ClassCastException.class})
    protected ResponseEntity<Object> handleClassCastException(ClassCastException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.INTERNAL_SERVER_ERROR, getMessage(ex.getMessage()));
    }

    private String getMessage(String key) {
        try {
            return messageService.getMessage(key);
        } catch (Exception e) {
            return key;
        }
    }





    @ExceptionHandler({AccessDeniedException.class})
    protected ResponseEntity<Object> handleAccessDeniedException(AccessDeniedException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.UNAUTHORIZED, getMessage("user_not_authorized_error"));
    }

    @ExceptionHandler({ProcessExecutionException.class})
    protected ResponseEntity<Object> handleProcessExecutionException(ProcessExecutionException ex, HttpServletRequest request) {
        return buildResponseEntity(HttpStatus.EXPECTATION_FAILED, messageService.getMessage(ex.getMessage()));
    }


    @ExceptionHandler({ConstraintViolationException.class})
    protected ResponseEntity<Object> handleConstraintViolationException(ConstraintViolationException be, HttpServletRequest request) {
        for (ConstraintViolation<?> exMessage : be.getConstraintViolations()) {
            return buildResponseEntity(HttpStatus.OK, messageService.getMessage(exMessage.getMessage()));
        }
        return buildResponseEntity(HttpStatus.OK, messageService.getMessage(be.getMessage()));
    }

    @ExceptionHandler({Exception.class})
    protected ResponseEntity<Object> handleException(Exception ex, HttpServletRequest request) {
        LOG.error("---------------------Internal server Error------------------------");
        if (ex.getCause() instanceof ConvertorException) {
            return buildResponseEntity(HttpStatus.OK, messageService.getMessage(((ConversionFailedException) ex).getRootCause().getMessage()));
        }
        ex.printStackTrace();
        return buildResponseEntity(HttpStatus.INTERNAL_SERVER_ERROR, INTERNAL_SERVER_ERROR_MSG);
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        BindingResult result = ex.getBindingResult();
        return buildResponseEntity(HttpStatus.OK, getMessage(result.getFieldError().getDefaultMessage()));
    }

}
