package com.mindbody.api.mapper;

import com.mindbody.api.dto.UserHealthReqDTO;
import com.mindbody.api.dto.UserHealthResDTO;
import com.mindbody.api.model.EntityUserHealthInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",unmappedTargetPolicy= ReportingPolicy.IGNORE)
public interface UserHealthInfoMapper {

    UserHealthResDTO toDTO(EntityUserHealthInfo entityUserHealthInfo);

    EntityUserHealthInfo toModel(UserHealthReqDTO userHealthReqDTO);
}
