package com.mindbody.api.mapper;

import com.mindbody.api.dto.cms.AddMagazineReqDTO;
import com.mindbody.api.model.EntityMagazine;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",unmappedTargetPolicy= ReportingPolicy.IGNORE)
public interface MagazineMapper {

    @Mapping(target = "magazineImage", ignore = true)
    @Mapping(target = "magazineThumbnailImage", ignore = true)
    EntityMagazine toModel(AddMagazineReqDTO addMagazineReqDTO);
}
