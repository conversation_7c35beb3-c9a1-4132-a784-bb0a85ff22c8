package com.mindbody.api.mapper;

import com.mindbody.api.dto.cms.AddWorkoutPlanReqDTO;
import com.mindbody.api.model.EntityWorkoutPlan;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",unmappedTargetPolicy= ReportingPolicy.IGNORE)
public interface WorkoutPlanMapper {

    @Mapping(target = "workoutVideoThumbnailImage", ignore = true)
    EntityWorkoutPlan toModel(AddWorkoutPlanReqDTO addWorkoutPlanReqDTO);
}
