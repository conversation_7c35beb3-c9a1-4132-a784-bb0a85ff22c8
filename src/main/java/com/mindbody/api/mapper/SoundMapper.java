package com.mindbody.api.mapper;

import com.mindbody.api.dto.cms.AddSoundReqDTO;
import com.mindbody.api.model.EntitySound;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",unmappedTargetPolicy= ReportingPolicy.IGNORE)
public interface SoundMapper {
    @Mapping(target = "soundFile", ignore = true)
    EntitySound toModel(AddSoundReqDTO addSoundReqDTO);
}
