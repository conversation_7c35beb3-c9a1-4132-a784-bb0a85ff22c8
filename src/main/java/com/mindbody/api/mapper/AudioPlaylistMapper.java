package com.mindbody.api.mapper;

import com.mindbody.api.dto.cms.AddAudioPlaylistReqDTO;
import com.mindbody.api.model.EntityAudioPlaylist;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",unmappedTargetPolicy= ReportingPolicy.IGNORE)
public interface AudioPlaylistMapper {
    EntityAudioPlaylist toModel(AddAudioPlaylistReqDTO addAudioPlaylistReqDTO);
}
