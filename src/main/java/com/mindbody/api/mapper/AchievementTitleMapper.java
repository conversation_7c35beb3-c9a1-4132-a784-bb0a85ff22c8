package com.mindbody.api.mapper;

import com.mindbody.api.dto.achievement.AchievementTitleResDTO;
import com.mindbody.api.dto.cms.AddAchievementTitleReqDTO;
import com.mindbody.api.model.EntityAchievementTitle;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",unmappedTargetPolicy= ReportingPolicy.IGNORE)
public interface AchievementTitleMapper {

    EntityAchievementTitle toModel(AddAchievementTitleReqDTO addAchievementTitleReqDTO);

    AchievementTitleResDTO toDTO(EntityAchievementTitle entityAchievementTitle);
}
