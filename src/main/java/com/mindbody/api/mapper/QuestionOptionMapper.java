package com.mindbody.api.mapper;

import com.mindbody.api.dto.AddQuestionReqDTO;
import com.mindbody.api.dto.OptionResDTO;
import com.mindbody.api.dto.QuestionOptionResDTO;
import com.mindbody.api.model.EntityQuestion;
import com.mindbody.api.model.EntityQuestionOption;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring",unmappedTargetPolicy= ReportingPolicy.IGNORE)
public interface QuestionOptionMapper {

    EntityQuestion toModel(AddQuestionReqDTO userDTO);
    
    QuestionOptionResDTO toDTOFromEntityQuestion(EntityQuestion entityQuestion);

    OptionResDTO toDTOFromEntityQuestionOption(EntityQuestionOption entityQuestionOption);



}
