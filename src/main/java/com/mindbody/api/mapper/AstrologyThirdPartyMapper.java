package com.mindbody.api.mapper;

import com.mindbody.api.dto.*;
import com.mindbody.api.dto.astrology.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AstrologyThirdPartyMapper {

    @Mapping(source = "latitude", target = "lat")
    @Mapping(source = "longitude", target = "lon")
    @Mapping(source = "timezone", target = "tzone")
    @Mapping(source = "houseType", target = "house_type")
    UserAstrologyThirdPartyReqDTO toUserAstrologyThirdPartyReqDTO(UserAstrologyReqDTO userAstrologyReqDTO);

    UserAstrologyDetailResDTO toUserAstrologyDetailResDTO(UserAstrologyDetailThirdPartyResDTO userAstrologyDetailThirdPartyResDTO);

    @Mapping(source = "latitude", target = "lat")
    @Mapping(source = "longitude", target = "lon")
    @Mapping(source = "timezone", target = "tzone")
    @Mapping(source = "houseType", target = "house_type")
    @Mapping(source = "innerCircleBackground", target = "inner_circle_background")
    @Mapping(source = "planetIconColor", target = "planet_icon_color")
    @Mapping(source = "signIconColor", target = "sign_icon_color")
    @Mapping(source = "signBackground", target = "sign_background")
    @Mapping(source = "chartSize", target = "chart_size")
    @Mapping(source = "imageType", target = "image_type")
    UserWheelChartThirdPartyReqDTO toUserWheelChartThirdPartyReqDTO(UserWheelChartReqDTO userWheelChartReqDTO);


    @Mapping(source = "chart_url", target = "chartUrl")
    UserWheelChartResDTO toUserWheelChartResDTO(UserWheelChartThirdPartyResDTO userWheelChartThirdPartyResDTO);

    @Mapping(source = "sign_name", target = "signName")
    @Mapping(source = "planet_name", target = "planetName")
    UserPlanetSignReportResDTO toUserPlanetSignReportResDTO(UserPlanetSignReportThirdPartyResDTO userPlanetSignReportThirdPartyResDTO);

    @Mapping(source = "timezone_in_ms", target = "timezoneInMs")
    TimezoneWithDstResDTO toTimezoneWithDstResDTO(TimezoneWithDstThirdPartyResDTO timezoneWithDstResDTO);

    TimeZoneWithDstThirdPartyReqDTO toTimeZoneWithDstThirdPartyReqDTO(TimeZoneWithDstReqDTO timezoneWithDstReqDTO);

    UserHoroscopeThirdPartyReqDTO toUserHoroscopeThirdPartyReqDTO(UserHoroscopeReqDTO userHoroscopeReqDTO);

}
