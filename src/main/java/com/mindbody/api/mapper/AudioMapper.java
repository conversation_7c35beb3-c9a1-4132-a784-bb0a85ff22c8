package com.mindbody.api.mapper;


import com.mindbody.api.dto.RandomAudioResDTO;
import com.mindbody.api.dto.cms.AddAudioReqDTO;
import com.mindbody.api.dto.cms.EditAudioReqDTO;
import com.mindbody.api.model.EntityAudio;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AudioMapper {


    @Mapping(target = "thumbnailImage", ignore = true)
    EntityAudio toModelFromAddAudioReqDTO(AddAudioReqDTO addAudioReqDTO);

    @Mapping(target = "thumbnailImage", ignore = true)
    EntityAudio toModelFromEditAudioReqDTO(EditAudioReqDTO editAudioReqDTO);

    RandomAudioResDTO toRandomAudioResDTO(EntityAudio entityAudio);

}