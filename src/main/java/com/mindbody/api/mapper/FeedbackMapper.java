package com.mindbody.api.mapper;

import com.mindbody.api.dto.feedback.FeedbackReqDTO;
import com.mindbody.api.dto.feedback.FeedbackResDTO;
import com.mindbody.api.model.EntityFeedback;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FeedbackMapper {
    FeedbackResDTO toDTO(EntityFeedback entityFeedback);
    EntityFeedback toModel(FeedbackReqDTO feedbackReqDTO);
} 