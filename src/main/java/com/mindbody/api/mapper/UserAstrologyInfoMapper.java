package com.mindbody.api.mapper;

import com.mindbody.api.dto.UserAstrologyDetailResDTO;
import com.mindbody.api.model.EntityUserAstrologyInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserAstrologyInfoMapper {

    UserAstrologyDetailResDTO toDTO(EntityUserAstrologyInfo entityUserAstrologyInfo);

    EntityUserAstrologyInfo toModel(UserAstrologyDetailResDTO userAstrologyDetailResDTO);

}
