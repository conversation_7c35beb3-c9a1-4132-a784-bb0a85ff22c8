package com.mindbody.api.mapper;


import com.mindbody.api.dto.cms.AddExerciseDTO;
import com.mindbody.api.model.EntityExercise;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",unmappedTargetPolicy= ReportingPolicy.IGNORE)
public interface ExerciseMapper {

    @Mapping(target = "exerciseThumbnailImage", ignore = true)
    EntityExercise toModel(AddExerciseDTO addExerciseDTO);
}
