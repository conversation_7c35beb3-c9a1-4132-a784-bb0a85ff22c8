package com.mindbody.api.mapper;

import com.mindbody.api.dto.cms.AddWorkoutPlaylistReqDTO;
import com.mindbody.api.model.EntityWorkoutPlaylist;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",unmappedTargetPolicy= ReportingPolicy.IGNORE)
public interface WorkoutPlaylistMapper {
    EntityWorkoutPlaylist toModel(AddWorkoutPlaylistReqDTO addWorkoutPlaylistReqDTO);
}
