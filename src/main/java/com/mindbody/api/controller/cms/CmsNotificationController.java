package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.notification.FilterUserIdsReqDTO;
import com.mindbody.api.dto.notification.FilterUsersForNotificationReqDTO;
import com.mindbody.api.dto.notification.SendNotificationToUsersReqDTO;
import com.mindbody.api.service.NotificationService;
import com.mindbody.api.service.UserService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.NOTIFICATION)
@Tag(name = "Notification-Management-Cms", description = "CMS Notification APIs")
public class CmsNotificationController extends BaseController {

    private final UserService userService;

    private final NotificationService notificationService;

    public CmsNotificationController(UserService userService, NotificationService notificationService) {
        this.userService = userService;
        this.notificationService = notificationService;
    }

    @PostMapping(Urls.LIST + Urls.USER)
    @Operation(summary = "List Users api based on notification filters", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listUsersBasedOnNotificationFilters(@Valid @RequestBody FilterUsersForNotificationReqDTO filterUsersForNotificationReqDTO) {
        logger.info("List Users based on notification filters api called.");
        return okSuccessResponse(userService.filterUsersBasedOnNotificationFilters(filterUsersForNotificationReqDTO), "common_success");
    }

    @PostMapping(Urls.LIST + Urls.USER_IDS)
    @Operation(summary = "List user ids api based on notification filters", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listUserIdsBasedOnNotificationFilters(@Valid @RequestBody FilterUserIdsReqDTO filterUserIdsReqDTO) {
        logger.info("Get user ids api based on notification filters api called.");
        return okSuccessResponse(userService.filterUserIdsBasedOnNotificationFilters(filterUserIdsReqDTO), "common_success");
    }

    @PostMapping(Urls.SEND_NOTIFICATION)
    @Operation(summary = "Send Notification To Users From CMS", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> sendNotificationToUsersFromCMS(@Valid @RequestBody SendNotificationToUsersReqDTO sendNotificationToUsersReqDTO) {
        logger.info("Send notification to users api is called.");
        notificationService.sendNotificationToUsersFromCMS(sendNotificationToUsersReqDTO);
        if (sendNotificationToUsersReqDTO.isScheduled()) {
            return okSuccessResponse("notification_scheduled_success");
        } else {
            return okSuccessResponse("notification_sent_success");
        }
    }

}
