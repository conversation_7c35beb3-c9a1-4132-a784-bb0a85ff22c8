package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.cms.AddAudioPlaylistReqDTO;
import com.mindbody.api.dto.cms.EditAudioPlaylistReqDTO;
import com.mindbody.api.service.AudioPlaylistService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.AUDIO_PLAYLIST)
@Tag(name = "Audio-Playlist-Management-Cms", description = "Audio Playlist Management APIs")
public class CmsAudioPlaylistController extends BaseController {

    private final AudioPlaylistService audioPlaylistService;

    public CmsAudioPlaylistController(AudioPlaylistService audioPlaylistService) {
        this.audioPlaylistService = audioPlaylistService;
    }

    @PostMapping(value = Urls.ADD)
    @Operation(summary = "Add Audio Playlist API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> addAudioPlaylist(@RequestParam(value = "audioPlaylistImage") MultipartFile audioPlaylistImage, AddAudioPlaylistReqDTO addAudioPlaylistReqDTO) {
        logger.info("Add Audio Playlist API is called!!");
        validData(addAudioPlaylistReqDTO);
        audioPlaylistService.addAudioPlaylist(audioPlaylistImage, addAudioPlaylistReqDTO);
        return okSuccessResponse("add_audio_playlist_success");
    }

    @PutMapping(Urls.EDIT)
    @Operation(summary = "Edit Audio Playlist API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> editAudioPlaylist(@RequestParam(value = "audioPlaylistImage", required = false) MultipartFile audioPlaylistImage, EditAudioPlaylistReqDTO editAudioPlaylistReqDTO) {
        validData(editAudioPlaylistReqDTO);
        audioPlaylistService.editAudioPlaylist(audioPlaylistImage, editAudioPlaylistReqDTO);
        return okSuccessResponse("edit_audio_playlist_success");
    }

    @PostMapping(Urls.LIST)
    @Operation(summary = "List Audio Playlist API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listAudioPlaylistCms(@RequestBody @Valid CommonListDTO commonListDTO) {
        logger.info("List audio playlist API for cms is called!!");
        boolean checkIsActiveRecord = false;
        return okSuccessResponse(audioPlaylistService.listAudioPlaylist(commonListDTO, checkIsActiveRecord), "common_success");
    }

    @DeleteMapping(value = Urls.DELETE)
    @Operation(summary = "Delete Audio Playlist API",  security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> deleteAudioPlaylist(@RequestParam Long audioPlaylistId) {
        logger.info("Delete audio playlist API is called!!");
        audioPlaylistService.deleteAudioPlaylist(audioPlaylistId);
        return okSuccessResponse("delete_audio_playlist_success");
    }

    @PutMapping(Urls.ACTIVE_INACTIVE)
    @Operation(summary = "Active/Inactive Audio Playlist API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> activeInactiveAudioPlaylist(@RequestParam Long audioPlaylistId) {
        logger.info("Active/Inactive audio playlist API is called.");
        String message=audioPlaylistService.activeInactiveAudioPlaylist(audioPlaylistId);
        return okSuccessResponse(message);
    }

    @GetMapping(Urls.LIST)
    @Operation(summary = "List Audio Playlist API based on Audio Playlist Type", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listAudioPlaylistForAudioPlaylistType(@RequestParam String audioPlaylistType) {
        logger.info("List category API for selected playlist module type is called!!");
        return okSuccessResponse(audioPlaylistService.listAudioPlaylistForSelectedAudioPlaylistTypeForCMS(audioPlaylistType), "common_success");
    }






}
