package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.cms.*;
import com.mindbody.api.security.CustomUserDetailsService;
import com.mindbody.api.service.AdminService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.ADMIN)
@Tag(name = "Admin", description = "Admin APIs")
public class AdminController extends BaseController {

    @Autowired
    private AdminService adminService;

    @Autowired
    private CustomUserDetailsService customUserDetailsService;

    @PostMapping(Urls.LOGIN)
    @Operation(summary = "Admin Login API")
    public ResponseEntity<?> loginAdmin(@RequestBody @Valid AdminLoginReqDTO adminLoginReqDTO) {
        AdminAccessTokenDTO adminAccessTokenDTO = adminService.adminLogin(adminLoginReqDTO);
        return okSuccessResponse(adminAccessTokenDTO,"login_success");
    }

    @PutMapping(Urls.EDIT_PROFILE)
    @Operation(summary = "Edit Admin Profile API",security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> editAdminProfile(@RequestParam(value = "profileImage", required = false) MultipartFile profileImage,EditAdminProfileReqDTO editAdminProfileReqDTO) {
        validData(editAdminProfileReqDTO);
        adminService.editAdminProfile(profileImage,editAdminProfileReqDTO);
        return okSuccessResponse("admin_profile_edit_success");
    }

    @GetMapping(value = Urls.VIEW_ADMIN_PROFILE)
    @Operation(summary = "View Admin Profile API",security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> viewAdminProfile() {
        ViewAdminProfileResDTO viewAdminProfileResDTO = adminService.viewAdminProfile();
        return okSuccessResponse(viewAdminProfileResDTO, "admin_profile_view_success");
    }


    @PostMapping (Urls.LOG_OUT)
    @Operation(summary = "Admin logout API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> logoutAdmin(@RequestHeader(value = Constant.ACCESS_TOKEN) String accessToken) {
        adminService.adminLogout(accessToken);
        return okSuccessResponse("logout_success");
    }

    @PostMapping(Urls.SEND_FORGOT_PASSWORD_LINK)
    @Operation(summary = "Admin Forgot Password API")
    public ResponseEntity<?> sendForgotPasswordLink(@RequestBody @Valid ForgotPasswordReqDTO forgotPasswordReqDTO) {
        adminService.sendForgotPasswordLink(forgotPasswordReqDTO);
        return okSuccessResponse("forgot_password_link_sent_success");
    }
    @PutMapping(value =  Urls.RESET_PASSWORD)
    public ResponseEntity<?> resetPassword(@RequestBody @Valid ResetPasswordReqDTO resetPasswordReqDTO) {
        adminService.resetPassword(resetPasswordReqDTO);
        return okSuccessResponse("reset_password_success");
    }

    @PostMapping(Urls.CHANGE_PASSWORD)
    @Operation(
            summary = " Admin Change Password API",
            security = @SecurityRequirement(name = Constant.ACCESS_TOKEN)
    )
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> changePassword(@RequestBody @Valid ChangePasswordReqDTO changePasswordReqDTO) {
        adminService.changePassword(changePasswordReqDTO);
        return okSuccessResponse("change_password_success");
    }














}
