package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.cms.AddWorkoutPlaylistReqDTO;
import com.mindbody.api.dto.cms.EditWorkoutPlaylistReqDTO;
import com.mindbody.api.service.WorkoutPlaylistService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.WORKOUT_PLAYLIST)
@Tag(name = "Workout-Playlist-Management-Cms", description = "Workout Playlist APIs")
public class CmsWorkoutPlaylistController extends BaseController {

    private final WorkoutPlaylistService workoutPlaylistService;

    public CmsWorkoutPlaylistController(WorkoutPlaylistService workoutPlaylistService) {
        this.workoutPlaylistService = workoutPlaylistService;
    }

    @PostMapping(value = Urls.ADD)
    @Operation(summary = "Add Workout Playlist API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> addWorkoutPlaylist(@RequestParam(value = "workoutPlaylistImage") MultipartFile workoutPlaylistImage, AddWorkoutPlaylistReqDTO addWorkoutPlaylistReqDTO) {
        logger.info("Add workout playlist API is called!!");
        validData(addWorkoutPlaylistReqDTO);
        workoutPlaylistService.addWorkoutPlaylist(workoutPlaylistImage, addWorkoutPlaylistReqDTO);
        return okSuccessResponse("add_workout_playlist_success");
    }

    @PutMapping(Urls.EDIT)
    @Operation(summary = "Edit Workout Playlist API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> editWorkoutPlaylist(@RequestParam(value = "workoutPlaylistImage", required = false) MultipartFile workoutPlaylistImage, EditWorkoutPlaylistReqDTO editWorkoutPlaylistReqDTO) {
        validData(editWorkoutPlaylistReqDTO);
        workoutPlaylistService.editWorkoutPlaylist(workoutPlaylistImage, editWorkoutPlaylistReqDTO);
        return okSuccessResponse("edit_workout_playlist_success");
    }

    @PostMapping(Urls.LIST)
    @Operation(summary = "List Workout Playlist API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listWorkoutPlaylistCms(@RequestBody @Valid CommonListDTO commonListDTO) {
        logger.info("List workout playlist API for cms is called!!");
        boolean checkIsActiveRecord = false;
        return okSuccessResponse(workoutPlaylistService.listWorkoutPlaylist(commonListDTO, checkIsActiveRecord), "common_success");
    }

    @DeleteMapping(value = Urls.DELETE)
    @Operation(summary = "Delete Workout Playlist API",  security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> deleteWorkoutPlaylist(@RequestParam Long workoutPlaylistId) {
        logger.info("Delete audio playlist API is called!!");
        workoutPlaylistService.deleteWorkoutPlaylist(workoutPlaylistId);
        return okSuccessResponse("delete_workout_playlist_success");
    }

    @PutMapping(Urls.ACTIVE_INACTIVE)
    @Operation(summary = "Active/Inactive Workout Playlist API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> activeInactiveWorkoutPlaylist(@RequestParam Long workoutPlaylistId) {
        logger.info("Active/Inactive audio playlist API is called.");
        String message=workoutPlaylistService.activeInactiveWorkoutPlaylist(workoutPlaylistId);
        return okSuccessResponse(message);
    }

    @GetMapping(Urls.LIST)
    @Operation(summary = "List Workout Playlist API based on Workout Playlist Type selected by Admin", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listWorkoutPlaylistForWorkoutPlaylistType(@RequestParam String workoutPlaylistType) {
        logger.info("List workout playlist API for selected workout playlist type is called for admin!!");
        return okSuccessResponse(workoutPlaylistService.listWorkoutPlaylistForSelectedWorkoutPlaylistType(workoutPlaylistType), "common_success");
    }


}
