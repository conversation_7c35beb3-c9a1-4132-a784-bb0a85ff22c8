package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.AbortFileUploadReqDTO;
import com.mindbody.api.dto.CompleteFileUploadReqDTO;
import com.mindbody.api.dto.GeneratePreSignedUrlReqDTO;
import com.mindbody.api.service.FileUploadService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.FILE_UPLOAD)
@Tag(name = "File-upload", description = "File upload APIs")
public class FileUploadController extends BaseController {


    private final FileUploadService fileUploadService;

    public FileUploadController(FileUploadService fileUploadService) {
        this.fileUploadService = fileUploadService;
    }

    @PostMapping(value = Urls.PRE_SIGNED_URLS)
    @Operation(summary = "Generate pre signed url API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> generatePreSignedUrls(@RequestBody @Valid GeneratePreSignedUrlReqDTO generatePreSignedUrlReqDTO) {
        logger.info("Generate pre signed url API is called!!");
        return okSuccessResponse(fileUploadService.generatePreSignedUrls(generatePreSignedUrlReqDTO), "generate_pre_signedUrl_success");
    }

    @PostMapping(value = Urls.COMPLETE)
    @Operation(summary = "Complete the file upload API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> completeFileUpload(@RequestBody @Valid CompleteFileUploadReqDTO completeFileUploadReqDTO) {
        logger.info("Complete the file upload API is called!!");
        fileUploadService.completeFileUpload(completeFileUploadReqDTO);
        return okSuccessResponse("complete_file_success");
    }

    @PostMapping(value = Urls.ABORT)
    @Operation(summary = "Abort the file upload API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> abortFileUpload(@RequestBody @Valid AbortFileUploadReqDTO abortFileUploadReqDTO) {
        logger.info("Abort the file upload API is called!!");
        fileUploadService.abortFileUpload(abortFileUploadReqDTO);
        return okSuccessResponse("abort_file_upload");
    }

}
