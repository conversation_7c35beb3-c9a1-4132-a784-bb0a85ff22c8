package com.mindbody.api.controller.cms;


import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.cms.AddExerciseDTO;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.cms.EditExerciseDTO;
import com.mindbody.api.service.ExerciseService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.EXERCISE)
@Tag(name = "Exercise-Cms", description = "Daily Tips APIs")
public class CmsExerciseController extends BaseController {

    private final ExerciseService exerciseService;

    public CmsExerciseController(ExerciseService exerciseService) {
        this.exerciseService = exerciseService;
    }

    @PostMapping(value = Urls.ADD)
    @Operation(summary = "Add Exercise API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> addExercise(@Valid AddExerciseDTO addExerciseDTO) {
        logger.info("Add Exercise API is called!!");
        exerciseService.addExercise(addExerciseDTO);
        return okSuccessResponse("add_exercise_success");
    }


    @PutMapping(value = Urls.EDIT)
    @Operation(summary = "Edit Exercise API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> editExercise(@Valid EditExerciseDTO editExerciseDTO) {
        logger.info("Edit Exercise API is called!!");
        exerciseService.editExercise(editExerciseDTO);
        return okSuccessResponse("edit_exercise_success");
    }

    @PostMapping(value = Urls.LIST)
    @Operation(summary = "List Exercise API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listExerciseCms(@RequestBody @Valid CommonListDTO commonListDTO) {
        logger.info("List Exercise API is called!!");
        return okSuccessResponse(exerciseService.listExerciseCms(commonListDTO), "common_success");
    }

    @DeleteMapping(value = Urls.DELETE)
    @Operation(summary = "Delete exercise API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> deleteExercise(@RequestParam Long exerciseId) {
        logger.info("Delete exercise API is called!!");
        exerciseService.deleteExercise(exerciseId);
        return okSuccessResponse("delete_exercise_success");
    }

    @PutMapping(Urls.ACTIVE_INACTIVE)
    @Operation(summary = "Active/Inactive exercise api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> activeInactiveExercise(@RequestParam Long exerciseId) {
        logger.info("Active/Inactive exercise API is called.");
        boolean isActive = exerciseService.activeInactiveExercise(exerciseId);
        if (isActive) {
            return okSuccessResponse("exercise_inactivate");
        } else {
            return okSuccessResponse("exercise_activate");
        }
    }

    @GetMapping(value = Urls.LIST)
    @Operation(summary = "List Exercise API to add workout plan in CMS", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listExerciseToAddWokoutPlan() {
        logger.info("List Exercise API to add workout plan in cms is called!!");
        return okSuccessResponse(exerciseService.listExerciseToAddWorkoutPlanCMS(), "common_success");
    }

}
