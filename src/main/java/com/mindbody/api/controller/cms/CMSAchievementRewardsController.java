package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.cms.EditAchievementRewardReqDTO;
import com.mindbody.api.service.AchievementRewardsService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.ACHIEVEMENT_REWARDS)
@Tag(name = "Achievement-Rewards-Management-Cms", description = "Achievement Rewards Management APIs")
public class CMSAchievementRewardsController extends BaseController {

    private final AchievementRewardsService achievementRewardsService;


    public CMSAchievementRewardsController(AchievementRewardsService achievementRewardsService) {
        this.achievementRewardsService = achievementRewardsService;
    }

    @GetMapping(value = Urls.VIEW)
    @Operation(summary = "View Achievement Rewards API for Admin", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> viewAchievementRewards() {
        logger.info("View Achievement Rewards API for Admin is called!!");
        return okSuccessResponse(achievementRewardsService.viewAchievementRewards(),"view_achievement_reward_success");
    }

    @PutMapping(value = Urls.EDIT)
    @Operation(summary = "Edit Achievement Rewards API for Admin", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> EditAchievementRewards(@Valid @RequestBody EditAchievementRewardReqDTO editAchievementRewardReqDTO) {
        logger.info("Edit Achievement Rewards API for Admin is called!!");
        achievementRewardsService.editAchievementRewards(editAchievementRewardReqDTO);
        return okSuccessResponse("edit_achievement_reward_success");
    }

}
