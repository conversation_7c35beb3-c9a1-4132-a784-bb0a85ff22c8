package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.service.FeedbackService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.FEEDBACK)
@Tag(name = "CMS-Feedback", description = "CMS Feedback Management APIs")
public class FeedbackCmsController extends BaseController {

    private final FeedbackService feedbackService;

    public FeedbackCmsController(FeedbackService feedbackService) {
        this.feedbackService = feedbackService;
    }

    @PostMapping(Urls.LIST)
    @Operation(summary = "Get all feedback", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> getAllFeedback(@Valid @RequestBody(required = false) CommonListDTO commonListDTO) {
        logger.info("Get all feedback API called from CMS");
        if (commonListDTO == null) {
            commonListDTO = new CommonListDTO();
        }
        return okSuccessResponse(feedbackService.listFeedback(commonListDTO), "common_success");
    }

} 