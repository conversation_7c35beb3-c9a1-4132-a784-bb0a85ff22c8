package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.cms.AddMagazineReqDTO;
import com.mindbody.api.dto.cms.EditMagazineReqDTO;
import com.mindbody.api.service.MagazineService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.MAGAZINE)
@Tag(name = "Magazine-Management-Cms", description = "CMS Magazine APIs")
public class CmsMagazineController extends BaseController {

    private final MagazineService magazineService;

    public CmsMagazineController(MagazineService magazineService) {
        this.magazineService = magazineService;
    }

    @PostMapping(value = Urls.ADD)
    @Operation(summary = "Add magazine API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> addMagazine(AddMagazineReqDTO addMagazineReqDTO) {
        logger.info("Add magazine API is called!!");
        validData(addMagazineReqDTO);
        magazineService.addMagazine(addMagazineReqDTO);
        return okSuccessResponse("add_magazine_success");
    }

    @PutMapping(Urls.EDIT)
    @Operation(summary = "Edit Magazine API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> editMagazine(EditMagazineReqDTO editMagazineReqDTO) {
        validData(editMagazineReqDTO);
        magazineService.editMagazine(editMagazineReqDTO);
        return okSuccessResponse("edit_magazine_success");
    }

    @PostMapping(Urls.LIST)
    @Operation(summary = "List magazine API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listMagazineCms(@RequestBody @Valid CommonListDTO commonListDTO) {
        logger.info("List magazine API is called!!");
        boolean checkIsActiveRecord = false;
        return okSuccessResponse(magazineService.listMagazine(commonListDTO, checkIsActiveRecord), "common_success");
    }

    @PutMapping(Urls.ACTIVE_INACTIVE)
    @Operation(summary = "Active/Inactive Magazine API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> activeInactiveMagazine(@RequestParam Long magazineId) {
        logger.info("Active/Inactive magazine API is called.");
        String message=magazineService.activeInactiveMagazine(magazineId);
        return okSuccessResponse(message);
    }


}
