package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.cms.AddWorkoutPlanReqDTO;
import com.mindbody.api.dto.cms.EditWorkoutPlanReqDTO;
import com.mindbody.api.dto.cms.WorkoutPlanListReqDTO;
import com.mindbody.api.service.WorkoutPlanService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.WORKOUT_PLAN)
@Tag(name = "Workout-Plan-Management-Cms", description = "Workout Plan APIs")
public class CmsWorkoutPlanController extends BaseController {

    private final WorkoutPlanService workoutPlanService;

    public CmsWorkoutPlanController(WorkoutPlanService workoutPlanService) {
        this.workoutPlanService = workoutPlanService;
    }

    @PostMapping(value = Urls.ADD)
    @Operation(summary = "Add Workout Plan API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> addWorkoutPlan(AddWorkoutPlanReqDTO addWorkoutPlanReqDTO) {
        logger.info("Add workout plan API is called!!");
        validData(addWorkoutPlanReqDTO);
        workoutPlanService.addWorkoutPlan(addWorkoutPlanReqDTO);
        return okSuccessResponse("add_workout_plan_success");
    }

    @PutMapping(value = Urls.EDIT)
    @Operation(summary = "Edit Workout Plan API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> editWorkoutPlan(EditWorkoutPlanReqDTO editWorkoutPlanReqDTO) {
        logger.info("Edit workout plan API is called!!");
        validData(editWorkoutPlanReqDTO);
        workoutPlanService.editWorkoutPlan(editWorkoutPlanReqDTO);
        return okSuccessResponse("edit_workout_plan_success");
    }

    @PutMapping(Urls.ACTIVE_INACTIVE)
    @Operation(summary = "Active/Inactive Workout Plan API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> activeInactiveWorkoutPlan(@RequestParam Long workoutPlanId) {
        logger.info("Active/Inactive workout plan API is called.");
        String message=workoutPlanService.activeInactiveWorkoutPlan(workoutPlanId);
        return okSuccessResponse(message);
    }

    @DeleteMapping(value = Urls.DELETE)
    @Operation(summary = "Delete Workout Plan API",  security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> deleteWorkoutPlan(@RequestParam Long workoutPlanId) {
        logger.info("Delete workout plan API is called!!");
        workoutPlanService.deleteWorkoutPlan(workoutPlanId);
        return okSuccessResponse("delete_workout_plan_success");
    }

    @PostMapping(Urls.LIST)
    @Operation(summary = "List Workout Plan API for CMS", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listWorkoutPlanCMS(@RequestBody @Valid WorkoutPlanListReqDTO workoutPlanListReqDTO) {
        logger.info("List workout plan API for cms is called!!");
        return okSuccessResponse(workoutPlanService.listWorkoutPlanList(workoutPlanListReqDTO), "common_success");
    }







}
