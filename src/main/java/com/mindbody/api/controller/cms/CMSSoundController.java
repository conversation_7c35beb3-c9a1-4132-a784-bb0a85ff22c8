package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.cms.AddSoundReqDTO;
import com.mindbody.api.dto.cms.EditSoundReqDTO;
import com.mindbody.api.service.SoundService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.SOUND)
@Tag(name = "Sound-Management-Cms", description = "Sound APIs")
public class CMSSoundController extends BaseController {

    private final SoundService soundService;

    public CMSSoundController(SoundService soundService) {
        this.soundService = soundService;
    }

    @PostMapping(value = Urls.ADD)
    @Operation(summary = "Add sound API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> addSound(AddSoundReqDTO addSoundReqDTO) {
        logger.info("Add sound API is called!!");
        validData(addSoundReqDTO);
        soundService.addSound(addSoundReqDTO);
        return okSuccessResponse("add_sound_success");
    }


    @PutMapping(Urls.EDIT)
    @Operation(summary = "Edit Sound API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> editSound(EditSoundReqDTO editSoundReqDTO) {
        logger.info("Edit sound API is called!!");
        validData(editSoundReqDTO);
        soundService.editSound(editSoundReqDTO);
        return okSuccessResponse("edit_sound_success");
    }

    @DeleteMapping(value = Urls.DELETE)
    @Operation(summary = "Delete Sound API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> deleteSound(@RequestParam Long soundId) {
        logger.info("Delete sound API is called!!");
        soundService.deleteSound(soundId);
        return okSuccessResponse("delete_sound_success");
    }

    @PostMapping(Urls.LIST)
    @Operation(summary = "List sound API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listSoundCms(@RequestBody @Valid CommonListDTO commonListDTO) {
        logger.info("List sound API is called!!");
        boolean checkIsActiveRecord = false;
        return okSuccessResponse(soundService.listSoundCms(commonListDTO, checkIsActiveRecord), "common_success");
    }


}
