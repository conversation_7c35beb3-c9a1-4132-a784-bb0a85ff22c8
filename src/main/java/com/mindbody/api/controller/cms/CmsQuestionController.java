package com.mindbody.api.controller.cms;


import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.AddQuestionReqDTO;
import com.mindbody.api.dto.ChangeQuestionOrderReqDTO;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.EditQuestionReqDTO;
import com.mindbody.api.service.QuestionService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.QUESTION)
@Tag(name = "Question-Cms", description = "Questions APIs")
public class CmsQuestionController extends BaseController {

    private final QuestionService questionService;

    public CmsQuestionController(QuestionService questionService) {
        this.questionService = questionService;
    }

    @PostMapping(value = Urls.ADD)
    @Operation(
            summary = "Add question API",
            security = @SecurityRequirement(name = Constant.ACCESS_TOKEN)
    )
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> addQuestion(AddQuestionReqDTO addQuestionReqDTO) {
        logger.info("Add question API is called!!");
        validData(addQuestionReqDTO);
        return okSuccessResponse(questionService.addQuestion(addQuestionReqDTO), "add_question_success");
    }

    @PutMapping(value = Urls.EDIT)
    @Operation(
            summary = "Edit question API",
            security = @SecurityRequirement(name = Constant.ACCESS_TOKEN)
    )
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> updateQuestion(EditQuestionReqDTO editQuestionReqDTO) {
        logger.info("Edit question API is called!!");
        validData(editQuestionReqDTO);
        return okSuccessResponse(questionService.editQuestion(editQuestionReqDTO), "edit_question_success");
    }

    @PostMapping(value = Urls.LIST)
    @Operation(
            summary = "List question API",
            security = @SecurityRequirement(name = Constant.ACCESS_TOKEN)
    )
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listQuestionCms(@RequestBody @Valid CommonListDTO commonListDTO) {
        logger.info("List question API is called!!");
        boolean checkIsActiveRecord = false;
        return okSuccessResponse(questionService.listQuestion(commonListDTO, checkIsActiveRecord), "common_success");
    }

    @DeleteMapping(value = Urls.DELETE)
    @Operation(
            summary = "Delete question API",
            security = @SecurityRequirement(name = Constant.ACCESS_TOKEN)
    )
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> deleteQuestionCms(@RequestParam Long questionId) {
        logger.info("Delete question API is called!!");
        questionService.deleteQuestion(questionId);
        return okSuccessResponse("delete_question_success");
    }


    @PostMapping(value = Urls.CHANGE_ORDER)
    @Operation(summary = "Change question order API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> changeQuestionOrder(@RequestBody List<ChangeQuestionOrderReqDTO> changeQuestionOrderReqDTOList) {
        logger.info("Change question order API is called!!");
        questionService.changeQuestionOrder(changeQuestionOrderReqDTOList);
        return okSuccessResponse("order_change_success");
    }


}
