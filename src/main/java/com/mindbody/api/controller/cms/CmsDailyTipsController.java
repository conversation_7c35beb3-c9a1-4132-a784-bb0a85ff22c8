package com.mindbody.api.controller.cms;


import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.cms.AddDailyTipsReqDTO;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.cms.EditDailyTipsReqDTO;
import com.mindbody.api.service.DailyTipsService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.DAILY_TIPS)
@Tag(name = "Daily-Tips-Cms", description = "Daily Tips APIs")
public class CmsDailyTipsController extends BaseController {

    private final DailyTipsService dailyTipsService;

    public CmsDailyTipsController(DailyTipsService dailyTipsService) {
        this.dailyTipsService = dailyTipsService;
    }

    @PostMapping(value = Urls.ADD)
    @Operation(summary = "Add daily tips API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> addDailyTips(@RequestParam(value = "dailyTipsFile") MultipartFile dailyTipsFile, @Valid AddDailyTipsReqDTO addDailyTipsReqDTO) {
        logger.info("Add daily tips API is called!!");
        dailyTipsService.addDailyTips(addDailyTipsReqDTO, dailyTipsFile);
        return okSuccessResponse("add_daily_tips_success");
    }

    @PutMapping(Urls.EDIT)
    @Operation(summary = "Edit daily tips API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> editDailyTips(@RequestBody @Valid EditDailyTipsReqDTO editDailyTipsReqDTO) {
        dailyTipsService.editDailyTips(editDailyTipsReqDTO);
        return okSuccessResponse("edit_daily_tips_success");
    }

    @PostMapping(value = Urls.LIST)
    @Operation(summary = "List daily tips API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listDailyTips(@RequestBody @Valid CommonListDTO commonListDTO) {
        logger.info("List daily tips API is called!!");
        return okSuccessResponse(dailyTipsService.listDailyTips(commonListDTO), "common_success");
    }

    @DeleteMapping(value = Urls.DELETE)
    @Operation(summary = "Delete daily tip API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> deleteDailyTip(@RequestParam Long dailyTipsId) {
        logger.info("Delete daily tip API is called!!");
        dailyTipsService.deleteDailyTip(dailyTipsId);
        return okSuccessResponse("delete_daily_tip_success");
    }

    @PutMapping(Urls.ACTIVE_INACTIVE)
    @Operation(summary = "Active/Inactive Daily tip api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> activeInactiveDailyTip(@RequestParam Long dailyTipsId) {
        logger.info("Active/Inactive daily tip API is called.");
        boolean isActive = dailyTipsService.activeInactiveDailyTip(dailyTipsId);
        if (isActive) {
            return okSuccessResponse("daily_tip_inactivate");
        } else {
            return okSuccessResponse("daily_tip_activate");
        }
    }

}
