package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.cms.AddProductCategoryReqDTO;
import com.mindbody.api.dto.cms.EditProductCategoryReqDTO;
import com.mindbody.api.service.ProductCategoryService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.PRODUCT_CATEGORY)
@Tag(name = "Product-Category-Cms", description = "Product Category APIs")
public class CmsProductCategoryController extends BaseController {

    private final ProductCategoryService productCategoryService;

    public CmsProductCategoryController(ProductCategoryService productCategoryService) {
        this.productCategoryService = productCategoryService;
    }

    @PostMapping(value = Urls.ADD)
    @Operation(summary = "Add Product category API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> addProductCategory(@Valid AddProductCategoryReqDTO addProductCategoryReqDTO) {
        logger.info("Add Product category API is called!!");
        productCategoryService.addProductCategory(addProductCategoryReqDTO);
        return okSuccessResponse("add_product_category_success");
    }


    @PutMapping(value = Urls.EDIT)
    @Operation(summary = "Edit Product category API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> editProductCategory(@Valid EditProductCategoryReqDTO editProductCategoryReqDTOt) {
        logger.info("Edit Product category API is called!!");
        productCategoryService.editProductCategory(editProductCategoryReqDTOt);
        return okSuccessResponse("edit_product_category_success");
    }

    @PostMapping(Urls.LIST)
    @Operation(summary = "List Product category API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listProductCategoryCms(@RequestBody @Valid CommonListDTO commonListDTO) {
        logger.info("List Product category API for cms is called!!");
        return okSuccessResponse(productCategoryService.listProductCategoryCms(commonListDTO), "common_success");
    }

    @DeleteMapping(value = Urls.DELETE)
    @Operation(summary = "Delete Product category API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> deleteProductCategory(@RequestParam Long productCategoryId) {
        logger.info("Delete Product category API is called!!");
        productCategoryService.deleteProductCategory(productCategoryId);
        return okSuccessResponse("delete_product_category_success");
    }

    @PutMapping(Urls.ACTIVE_INACTIVE)
    @Operation(summary = "Active/Inactive Product category api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> activeInactiveProductCategory(@RequestParam Long productCategoryId) {
        logger.info("Active/Inactive Product category API is called.");
        boolean isActive = productCategoryService.activeInactiveProductCategory(productCategoryId);
        if (isActive) {
            return okSuccessResponse("product_category_activate");
        } else {
            return okSuccessResponse("product_category_inactivate");
        }
    }

    @GetMapping(Urls.LIST)
    @Operation(summary = "List Product category API for Cms without pagination", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    public ResponseEntity<?> listProductCategoryCmsWithoutPagination() {
        logger.info("List Product category API For Cms without pagination is called!!");
        return okSuccessResponse(productCategoryService.listProductCategoryForUserAndCms(), "common_success");
    }


}
