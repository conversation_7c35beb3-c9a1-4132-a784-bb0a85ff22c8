package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.service.ReferralService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.REFERRAL)
@Tag(name = "Referral-Cms", description = "Referral Management APIs")
@RequiredArgsConstructor
public class CmsReferralController extends BaseController {

    private final ReferralService referralService;

    @PostMapping(value = Urls.LIST)
    @Operation(summary = "List Referral Codes API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listReferralCodes(@RequestBody @Valid CommonListDTO commonListDTO) {
        logger.info("List Referral Codes API is called!!");
        return okSuccessResponse(referralService.listReferralCodesForCMS(commonListDTO), "common_success");
    }
}
