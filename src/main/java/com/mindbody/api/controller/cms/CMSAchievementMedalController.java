package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.service.AchievementMedalService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.ACHIEVEMENTS + Urls.ACHIEVEMENT_MEDAL)
@Tag(name = "Achievement-Medal-Management-Cms", description = "Achievement Medal Management APIs")
public class CMSAchievementMedalController extends BaseController {

    private final AchievementMedalService achievementMedalService;

    public CMSAchievementMedalController(AchievementMedalService achievementMedalService) {
        this.achievementMedalService = achievementMedalService;
    }

    @GetMapping(Urls.LIST)
    @Operation(summary = "List Achievement Medals API for Achievement Module in CMS", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listAchievementMedal() {
        logger.info("List achievement medals api for Achievement Module in CMS is called!!");
        return okSuccessResponse(achievementMedalService.listAchievementMedal(), "common_success");
    }

}
