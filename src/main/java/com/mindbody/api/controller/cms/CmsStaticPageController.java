package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.cms.EditStaticPageReqDTO;
import com.mindbody.api.dto.cms.StaticPageResDTO;
import com.mindbody.api.service.StaticPageService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.STATIC_PAGE)
@Tag(name = "Static-Page-Management-Cms", description = "Static Page APIs")
public class CmsStaticPageController extends BaseController {

    private final StaticPageService staticPageService;

    public CmsStaticPageController(StaticPageService staticPageService) {
        this.staticPageService = staticPageService;
    }

    @PutMapping(value = Urls.EDIT)
    @Operation(summary = "Update Static Page API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> editStaticPage(@RequestBody @Valid EditStaticPageReqDTO editStaticPageReqDTO) {
        logger.info("Edit Static Page API is called!!");
        staticPageService.editStaticPage(editStaticPageReqDTO);
        return okSuccessResponse("edit_static_page_success");
    }

    @GetMapping(value = Urls.VIEW)
    @Operation(summary = "View Static Page API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> viewStaticPage(@RequestParam String staticPageType) {
        logger.info("View Static Page API is called!!");
        StaticPageResDTO staticPageResDTO= staticPageService.viewStaticPage(staticPageType);
        return okSuccessResponse(staticPageResDTO,"view_static_page_success");
    }

}
