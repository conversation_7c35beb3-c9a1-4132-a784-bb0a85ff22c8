package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.cms.DashboardResDTO;
import com.mindbody.api.service.DashboardService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.DASHBOARD)
@Tag(name = "Dashboard", description = "Dashboard API")
public class CMSDashboardController extends BaseController {

    private final DashboardService dashboardService;

    public CMSDashboardController(DashboardService dashboardService) {
        this.dashboardService = dashboardService;
    }

    @GetMapping(value = Urls.VIEW)
    @Operation(summary = "View Dashboard Data API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> viewDashboardData() {
        logger.info("View Dashboard Data API is called!!");
        DashboardResDTO dashboardResDTO = dashboardService.viewDashboardData();
        return okSuccessResponse(dashboardResDTO, "view_dashboard_data_success");
    }
}
