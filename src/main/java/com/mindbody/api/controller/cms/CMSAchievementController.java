package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.cms.AchievementListReqDTO;
import com.mindbody.api.dto.cms.EditAchievementReqDTO;
import com.mindbody.api.service.AchievementsService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.ACHIEVEMENTS)
@Tag(name = "Achievement-Management-Cms", description = "Achievement Management APIs")
public class CMSAchievementController extends BaseController {

    private final AchievementsService achievementsService;

    public CMSAchievementController(AchievementsService achievementsService) {
        this.achievementsService = achievementsService;
    }


    @PostMapping(Urls.LIST)
    @Operation(summary = "List Achievement api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listAchievement(@RequestBody @Valid AchievementListReqDTO achievementListReqDTO) {
        logger.info("List achievements api called.");
        return okSuccessResponse(achievementsService.listAchievements(achievementListReqDTO), "common_success");
    }

    @PutMapping(Urls.EDIT)
    @Operation(summary = "Edit Achievement api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> editAchievement(@RequestParam(value = "borderImage", required = false) MultipartFile borderImage, @RequestParam(value = "badgeImage", required = false) MultipartFile badgeImage, @Valid EditAchievementReqDTO editAchievementReqDTO) {
        logger.info("Edit achievements api called.");
        achievementsService.editAchievement(editAchievementReqDTO, borderImage, badgeImage);
        return okSuccessResponse("edit_achievement_success");
    }

}
