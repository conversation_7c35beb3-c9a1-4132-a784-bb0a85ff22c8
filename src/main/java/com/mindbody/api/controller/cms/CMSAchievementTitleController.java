package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.achievement.AchievementTitleResDTO;
import com.mindbody.api.dto.cms.AddAchievementTitleReqDTO;
import com.mindbody.api.service.AchievementTitleService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.ACHIEVEMENTS + Urls.ACHIEVEMENT_TITLE)
@Tag(name = "Achievement-Title-Management-Cms", description = "Achievement Title Management APIs")
public class CMSAchievementTitleController extends BaseController {

    private final AchievementTitleService achievementTitleService;

    public CMSAchievementTitleController(AchievementTitleService achievementTitleService) {
        this.achievementTitleService = achievementTitleService;
    }

    @PostMapping(value = Urls.ADD)
    @Operation(summary = "Add Achievement Title API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> addAchievementTitle(@Valid @RequestBody AddAchievementTitleReqDTO addAchievementTitleReqDTO) {
        logger.info("Add Achievement Title API is called!!");
        AchievementTitleResDTO achievementTitleResDTO = achievementTitleService.addAchievementTitle(addAchievementTitleReqDTO);
        return okSuccessResponse(achievementTitleResDTO, "add_achievement_title_success");
    }

    @GetMapping(Urls.LIST)
    @Operation(summary = "List Achievement Title API for Achievement Module in CMS", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listAchievementTitle() {
        logger.info("List achievement title api for Achievement Module in CMS is called!!");
        return okSuccessResponse(achievementTitleService.listAchievementTitle(), "common_success");
    }
}
