package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.service.UserAchievementService;
import com.mindbody.api.service.UserService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

//@RestController
//@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.USER_REQUEST_MANAGEMENT)
//@Tag(name = "User-Request-Management-Cms", description = "CMS User Request Management APIs")
//public class CmsUserRequestManagementController extends BaseController {
//
//    private final UserService userService;
//
//    public CmsUserRequestManagementController(UserService userService, UserAchievementService userAchievementService) {
//        this.userService = userService;
//    }
//
//    @PostMapping(Urls.LIST)
//    @Operation(summary = "List User Request Management api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
//    @PreAuthorize("hasAnyAuthority('ADMIN')")
//    public ResponseEntity<?> listUserRequestManagement(@RequestBody CommonListDTO commonListDTO) {
//        logger.info("List User Request Management api called.");
//        return okSuccessResponse(userService.listUserRequestManagement(commonListDTO), "common_success");
//    }
//
//}
