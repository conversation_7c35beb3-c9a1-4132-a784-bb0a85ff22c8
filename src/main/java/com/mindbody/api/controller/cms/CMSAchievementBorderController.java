package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.service.AchievementBorderService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.ACHIEVEMENTS + Urls.ACHIEVEMENT_BORDER)
@Tag(name = "Achievement-Border-Management-Cms", description = "Achievement Border Management APIs")
public class CMSAchievementBorderController extends BaseController {

    private final AchievementBorderService achievementBorderService;

    public CMSAchievementBorderController(AchievementBorderService achievementBorderService) {
        this.achievementBorderService = achievementBorderService;
    }

    @GetMapping(Urls.LIST)
    @Operation(summary = "List Achievement Border API for Achievement Module in CMS", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listAchievementBorder() {
        logger.info("List achievement border api for Achievement Module in CMS is called!!");
        return okSuccessResponse(achievementBorderService.listAchievementBorder(), "common_success");
    }

}
