package com.mindbody.api.controller.cms;


import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.AudioListReqDTO;
import com.mindbody.api.dto.cms.AddAudioReqDTO;
import com.mindbody.api.dto.cms.EditAudioReqDTO;
import com.mindbody.api.service.AudioService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.AUDIO)
@Tag(name = "Audio-Cms", description = "Audio APIs")
public class CmsAudioController extends BaseController {

    private final AudioService audioService;

    public CmsAudioController(AudioService audioService) {
        this.audioService = audioService;
    }

    @PostMapping(value = Urls.ADD)
    @Operation(summary = "Add audio API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> addAudio(AddAudioReqDTO addAudioReqDTO) {
        logger.info("Add audio API is called!!");
        validData(addAudioReqDTO);
        audioService.addAudio(addAudioReqDTO);
        return okSuccessResponse("add_audio_success");
    }

    @PostMapping(value = Urls.EDIT)
    @Operation(summary = "update audio API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> updateAudio(EditAudioReqDTO editAudioReqDTO) {
        logger.info("update audio API is called!!");
        validData(editAudioReqDTO);
        audioService.updateAudio(editAudioReqDTO);
        return okSuccessResponse("edit_audio_success");
    }

    @PostMapping(value = Urls.LIST)
    @Operation(summary = "List audio API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listAudioCms(@RequestBody @Valid AudioListReqDTO audioListReqDTO) {
        logger.info("List audio API is called!!");
        return okSuccessResponse(audioService.listAudioCms(audioListReqDTO), "common_success");
    }

    @DeleteMapping(value = Urls.DELETE)
    @Operation(summary = "Delete audio API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> deleteAudio(@RequestParam Long audioId) {
        logger.info("Delete audio API is called!!");
        audioService.deleteAudio(audioId);
        return okSuccessResponse("delete_audio_success");
    }

    @PutMapping(Urls.ACTIVE_INACTIVE)
    @Operation(summary = "Active/Inactive audio api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> activeInactiveAudio(@RequestParam Long audioId) {
        logger.info("Active/Inactive audio API is called.");
        boolean isActive = audioService.activeInactiveAudio(audioId);
        if (isActive) {
            return okSuccessResponse("audio_activate");
        } else {
            return okSuccessResponse("audio_inactivate");
        }
    }
}


