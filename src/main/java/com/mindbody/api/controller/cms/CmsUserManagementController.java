package com.mindbody.api.controller.cms;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.*;
import com.mindbody.api.dto.cms.EditUserDetailsReqDTO;
import com.mindbody.api.dto.cms.ViewUserDetailsResDTO;
import com.mindbody.api.service.UserAchievementService;
import com.mindbody.api.service.UserService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH_CMS + Urls.USER_MANAGEMENT)
@Tag(name = "User-Management-Cms", description = "CMS User Management APIs")
public class CmsUserManagementController extends BaseController {

    private final UserService userService;

    private final UserAchievementService userAchievementService;


    public CmsUserManagementController(UserService userService, UserAchievementService userAchievementService) {
        this.userService = userService;
        this.userAchievementService = userAchievementService;
    }

    @PostMapping(Urls.LIST)
    @Operation(summary = "List User Management api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> listUserManagement(@RequestBody CommonListDTO commonListDTO) {
        logger.info("List User Management api called.");
        return okSuccessResponse(userService.listUserManagement(commonListDTO), "common_success");
    }

    @GetMapping(Urls.VIEW)
    @Operation(summary = "View user details api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> viewUserDetails(@RequestParam long userId) {
        logger.info("View user details api called.");
        ViewUserDetailsResDTO viewUserDetailsResDTO = userService.viewUserDetails(userId);
        return okSuccessResponse(viewUserDetailsResDTO, "common_success");
    }

    @PutMapping(Urls.EDIT)
    @Operation(summary = "Edit registered user details api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> editUserDetails(@Valid @RequestBody EditUserDetailsReqDTO editUserDetailsReqDTO) {
        logger.info("Edit user details api called.");
        userService.editUserDetails(editUserDetailsReqDTO);
        return okSuccessResponse("edit_profile_success");
    }

    @PutMapping(Urls.ACTIVE_INACTIVE)
    @Operation(summary = "Active/Inactive User api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> activeInactiveUser(@RequestParam Long userId) {
        logger.info("Active/Inactive User api called.");
        String message = userService.activeInactiveUserByAdmin(userId);
        return okSuccessResponse(message);
    }

    @DeleteMapping(Urls.DELETE)
    @Operation(summary = "Delete User Account Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> deleteUserAccountByAdmin(@RequestParam Long userId) {
        logger.info("Delete user account by admin api called.");
        userService.deleteUserAccountByAdmin(userId);
        return okSuccessResponse("delete_account_success");
    }

    @PostMapping(Urls.VIEW_USER_ACHIEVEMENTS)
    @Operation(summary = "View CMS User Achievement Details api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('ADMIN')")
    public ResponseEntity<?> findAllUserAchievementsByUserIdForCMS(@Valid @RequestBody UserAchievementListReqDTO userAchievementListReqDTO) {
        logger.info("View user achievement details for CMS api called.");
        SearchResultDTO<UserAllAchievementDTO> userAllAchievementDTO = userAchievementService.findAllUserAchievementsByUserIdForCMS(userAchievementListReqDTO);
        return okSuccessResponse(userAllAchievementDTO, "common_success");
    }

//    @PostMapping(value = Urls.UPDATE_REACTIVATION_REQUEST_PENDING_STATUS)
//    @Operation(summary = "Approve/Reject user reactivation request API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
//    @PreAuthorize("hasAnyAuthority('ADMIN')")
//    public ResponseEntity<?> updateRequestStatus(@RequestBody @Valid RequestActionReqDTO requestActionReqDTO) {
//        logger.info("Approve/Reject user reactivation request API is called.");
//        userService.updateRequestStatus(requestActionReqDTO);
//        return okSuccessResponse("common_success");
//    }

}
