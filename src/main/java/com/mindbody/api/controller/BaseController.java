package com.mindbody.api.controller;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.mindbody.api.base.ApiResponse;
import com.mindbody.api.service.MessageService;
import com.mindbody.api.util.Constant;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;


import java.util.Set;
import java.util.stream.Collectors;


@RestController
public class BaseController {

    protected static final Logger logger = LoggerFactory.getLogger(BaseController.class);
    @Autowired
    protected Validator validator;
    @Autowired
    private MessageService messageService;
    @Autowired
    private ObjectMapper objectMapper;

    protected <T> ResponseEntity<?> okSuccessResponse(T t, String messageId) {
        return new ResponseEntity<>(new ApiResponse<>(t, messageService.getMessage(messageId), Constant.SUCCESS), HttpStatus.OK);
    }

    protected <T> ResponseEntity<?> okWarningResponse(T t, String messageId) {
        return new ResponseEntity<>(new ApiResponse<>(t, messageService.getMessage(messageId), Constant.WARN), HttpStatus.OK);
    }

    protected <T> ResponseEntity<?> okFailResponse(T t, String messageId) {
        return new ResponseEntity<>(new ApiResponse<>(t, messageService.getMessage(messageId), Constant.FAIL), HttpStatus.OK);
    }

    protected ResponseEntity okSuccessResponse(String messageId) {
        return new ResponseEntity<>(new ApiResponse<>(objectMapper.createObjectNode(), messageService.getMessage(messageId), Constant.SUCCESS), HttpStatus.OK);
    }

    protected ResponseEntity okSuccessResponse(String messageId, String... params) {
        return new ResponseEntity<>(new ApiResponse<>(objectMapper.createObjectNode(), messageService.getMessage(messageId, params), Constant.SUCCESS), HttpStatus.OK);
    }

    protected <T> ResponseEntity<?> okSuccessResponse(T t, String messageId, String... params) {
        return new ResponseEntity<>(new ApiResponse<>(objectMapper.createObjectNode(), messageService.getMessage(messageId, params), Constant.SUCCESS), HttpStatus.OK);
    }

    protected <T> ResponseEntity<?> okSuccessResponseResource(T t, String messageId, String... params) {
        return new ResponseEntity<>(new ApiResponse<>(t, messageService.getMessage(messageId, params), Constant.SUCCESS), HttpStatus.OK);
    }

    protected <T> ResponseEntity<?> okWarningResponse(T t, String messageId, String... params) {
        return new ResponseEntity<>(new ApiResponse<>(objectMapper.createObjectNode(), messageService.getMessage(messageId, params), Constant.WARN), HttpStatus.OK);
    }


    protected ResponseEntity okFailResponse(String messageId) {
        return new ResponseEntity<>(new ApiResponse<>(objectMapper.createObjectNode(), messageService.getMessage(messageId), Constant.FAIL), HttpStatus.OK);
    }

    protected <T> void validData(T data) {
        Set<ConstraintViolation<T>> violations = validator.validate(data);
        if (!violations.isEmpty()) {
            throw new ConstraintViolationException(violations);
        }
    }

//    protected void checkBindingResult(BindingResult bindingResult) {
//        if (bindingResult.hasErrors()) {
//            throw new ResponseStatusException(HttpStatus.PARTIAL_CONTENT, bindingResult.getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining(",")));
//        }
//    }

}
