package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.shopify.UpdateBuyerIdentityReqDTO;
import com.mindbody.api.service.ShopifyCheckoutService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.SHOPIFY + Urls.CHECKOUT)
@Tag(name = "Shopify-User-Checkout", description = "Shopify Checkout APIs")
public class ShopifyCheckoutController extends BaseController {
    private final ShopifyCheckoutService shopifyCheckoutService;

    public ShopifyCheckoutController(ShopifyCheckoutService shopifyCheckoutService) {
        super();
        this.shopifyCheckoutService = shopifyCheckoutService;
    }

    @PostMapping(value = Urls.ADD_SHIPPING_ADDRESS)
    @Operation(summary = "Add Shipping address for user API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> addShippingAddress(@RequestBody @Valid UpdateBuyerIdentityReqDTO updateBuyerIdentityReqDTO) {
        logger.info("Add Shipping address for user API is called!!");
        return okSuccessResponse(shopifyCheckoutService.addShippingAddress(updateBuyerIdentityReqDTO), "shipping_address_add_success");
    }
}
