package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.feedback.FeedbackReqDTO;
import com.mindbody.api.service.FeedbackService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.FEEDBACK)
@Tag(name = "User-Feedback", description = "User Feedback APIs")
public class FeedbackController extends BaseController {

    private final FeedbackService feedbackService;

    public FeedbackController(FeedbackService feedbackService) {
        this.feedbackService = feedbackService;
    }

    @PostMapping(Urls.SUBMIT)
    @Operation(summary = "Submit feedback", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> submitFeedback(@Valid @RequestBody FeedbackReqDTO feedbackReqDTO) {
        logger.info("Submit feedback API called");
        return okSuccessResponse(feedbackService.submitFeedback(feedbackReqDTO), "feedback_submit_success");
    }

} 