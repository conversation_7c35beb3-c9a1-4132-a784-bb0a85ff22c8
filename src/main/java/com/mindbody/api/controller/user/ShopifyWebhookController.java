package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.SubmitUserAchievementReqDTO;
import com.mindbody.api.dto.shopify.TotalNumberOfProductsReqDTO;
import com.mindbody.api.enums.AchievementActivityType;
import com.mindbody.api.model.EntityShopifyCustomers;
import com.mindbody.api.model.EntityUser;
import com.mindbody.api.repository.ShopifyCustomersRepository;
import com.mindbody.api.service.ShopifyCustomerService;
import com.mindbody.api.service.ShopifyOrderService;
import com.mindbody.api.service.UserAchievementService;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.SHOPIFY + Urls.WEBHOOK)
@Tag(name = "Shopify-Webhook", description = "Shopify Webhook APIs")
public class ShopifyWebhookController extends BaseController {

    private final ShopifyCustomersRepository shopifyCustomersRepository;

    private final ShopifyCustomerService shopifyCustomerService;

    private final ShopifyOrderService shopifyOrderService;

    private final UserAchievementService userAchievementService;

    public ShopifyWebhookController(ShopifyCustomersRepository shopifyCustomersRepository, ShopifyCustomerService shopifyCustomerService, ShopifyOrderService shopifyOrderService, UserAchievementService userAchievementService) {
        this.shopifyCustomersRepository = shopifyCustomersRepository;
        this.shopifyCustomerService = shopifyCustomerService;
        this.shopifyOrderService = shopifyOrderService;
        this.userAchievementService = userAchievementService;
    }


    @PostMapping(Urls.CUSTOMERS_CREATE)
    public ResponseEntity<String> handleCustomerCreation(@RequestBody Map<String, Object> payload) {

        System.out.println("Received customer creation event: " + payload);

        // Process the payload (e.g., save customer details to the database)
//        String customerId = payload.get("admin_graphql_api_id").toString();
        String customerId = payload.get("id").toString();
        String email = payload.get("email").toString();
        String firstName = payload.get("first_name").toString();
        String lastName = payload.get("last_name").toString();

        // Perform your logic here
        EntityUser entityUser = shopifyCustomerService.getUserByEmail(email);
        if (entityUser == null) {
            System.out.println("No user found for email: " + email);
        }
        EntityShopifyCustomers entityShopifyCustomers = new EntityShopifyCustomers();
        entityShopifyCustomers.setEntityUser(entityUser);
        entityShopifyCustomers.setUserId(entityUser.getUserId());
        entityShopifyCustomers.setCustomerId(customerId);
        entityShopifyCustomers.setEmail(email);
        entityShopifyCustomers.setFirstName(firstName);
        entityShopifyCustomers.setLastName(lastName);
        shopifyCustomersRepository.save(entityShopifyCustomers);
        return ResponseEntity.ok("Customer creation Webhook processed successfully");
    }

    @PostMapping(Urls.ORDERS_CREATE)
    public ResponseEntity<String> handleOrderCreation(@RequestBody Map<String, Object> payload) {
        System.out.println("Received order creation event: " + payload);
        return ResponseEntity.ok("Order creation Webhook processed successfully");
    }

    @PostMapping(Urls.ORDERS_PAYMENT)
    public ResponseEntity<String> handleOrderPayment(@RequestBody Map<String, Object> payload) {
        System.out.println("Received order payment event: " + payload);
        return ResponseEntity.ok("Order payment Webhook processed successfully");
    }

    @PostMapping(Urls.ORDERS_PAID)
    public ResponseEntity<String> handleOrderPaid(@RequestBody Map<String, Object> payload) {
        System.out.println("Received order paid event: " + payload);
        String customerId;
        if (payload.containsKey("customer") && payload.get("customer") instanceof Map) {
            Map<String, Object> customer = (Map<String, Object>) payload.get("customer");
            if (customer.containsKey("id")) {
                customerId = customer.get("id").toString();

                EntityShopifyCustomers entityShopifyCustomers = shopifyCustomersRepository.findByCustomerId(customerId);
                if (entityShopifyCustomers != null) {
                    TotalNumberOfProductsReqDTO totalNumberOfProductsReqDTO = new TotalNumberOfProductsReqDTO();
                    totalNumberOfProductsReqDTO.setCustomerId(customerId);
                    totalNumberOfProductsReqDTO.setFirst(250);
                    int totalProductPurchases = shopifyOrderService.getTotalProductsPurchasedByCustomer(totalNumberOfProductsReqDTO);

                    if(totalProductPurchases == 1 && !userAchievementService.existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Health_Purchase_1_Products,entityShopifyCustomers.getUserId())) {
                        userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Health_Purchase_1_Products, entityShopifyCustomers.getUserId()));
                    }
                    if(totalProductPurchases >= 2 && !userAchievementService.existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Health_Purchase_2_Products,entityShopifyCustomers.getUserId())) {
                        userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Health_Purchase_2_Products, entityShopifyCustomers.getUserId()));
                    }
                    if(totalProductPurchases >= 5 && !userAchievementService.existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Health_Purchase_5_Products,entityShopifyCustomers.getUserId())) {
                        userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Health_Purchase_5_Products, entityShopifyCustomers.getUserId()));
                    }
                    if(totalProductPurchases >= 20 && !userAchievementService.existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Health_Purchase_20_Products,entityShopifyCustomers.getUserId())) {
                        userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Health_Purchase_20_Products, entityShopifyCustomers.getUserId()));
                    }
                    if(totalProductPurchases >= 50 && !userAchievementService.existsByAchievementCategoryTypeAndUserIdAndIsDeleted(AchievementActivityType.Warrior_Empowerment_Health_Purchase_50_Products,entityShopifyCustomers.getUserId())) {
                        userAchievementService.submitAchievement(new SubmitUserAchievementReqDTO(AchievementActivityType.Warrior_Empowerment_Health_Purchase_50_Products, entityShopifyCustomers.getUserId()));
                    }
                    System.out.println("totalProductPurchases: " + totalProductPurchases);
                }
            }
        }

        return ResponseEntity.ok("Order paid Webhook processed successfully");
    }


    @PostMapping(Urls.ORDERS_FULFILLMENT)
    public ResponseEntity<String> handleOrderFulfillment(@RequestBody Map<String, Object> payload) {
        System.out.println("Received order fulfillment event: " + payload);
        return ResponseEntity.ok("Order fulfillment Webhook processed successfully");
    }


}
