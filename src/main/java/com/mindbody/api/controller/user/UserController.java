package com.mindbody.api.controller.user;


import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.*;
import com.mindbody.api.dto.cms.ForgotPasswordReqDTO;
import com.mindbody.api.dto.cms.ResetPasswordReqDTO;
import com.mindbody.api.service.OTPService;
import com.mindbody.api.service.UserService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER)
@Tag(name = "User", description = "User management APIs")
public class UserController extends BaseController {

    private final UserService userService;

    private final OTPService otpService;

    public UserController(UserService userService, OTPService otpService) {
        this.userService = userService;
        this.otpService = otpService;
    }

    @PostMapping(value = Urls.LOGIN)
    @Operation(summary = "User login API")
    public ResponseEntity<?> userLogin(@Valid @RequestBody UserLoginDTO userLoginDTO) {
        logger.info("User login API is called!!");
        return okSuccessResponse(userService.userLogin(userLoginDTO), "login_success");
    }

    @PostMapping(value = Urls.SIGN_UP)
    @Operation(summary = "User signUp API")
    public ResponseEntity<?> signUpUser(@RequestBody @Valid UserSignupReqDTO userSignupReqDTO) {
        logger.info("User signUp API is called!!");
        return okSuccessResponse(userService.signUpUser(userSignupReqDTO), "user_register_success");
    }

    @PostMapping(value = Urls.SOCIAL_MEDIA + Urls.LOGIN)
    @Operation(summary = "User social media login API")
    public ResponseEntity<?> userSocialMediaLogin(@Valid @RequestBody SocialMediaLoginDTO socialMediaLoginDTO) {
        logger.info("User social media login API is called!!");
        return okSuccessResponse(userService.userSocialMediaLogin(socialMediaLoginDTO), "login_success");
    }

    @PostMapping(value = Urls.SEND_OTP)
    @Operation(summary = "Send verification OTP to User API")
    public ResponseEntity<?> sendVerificationOTP(@RequestBody @Valid SendOTPReqDTO sendOTPReqDTO) {
        logger.info("Send verification OTP to User API is called!!");
        return okSuccessResponse(otpService.sendVerificationOTP(sendOTPReqDTO), "sent_sms_otp_success");

    }

    @PostMapping(value = Urls.VERIFY_OTP)
    @Operation(summary = "Verify OTP API")
    public ResponseEntity<?> verifyOTP(@RequestBody @Valid VerifyOTPReqDTO verifyOTPReqDTO) {
        logger.info("Verify OTP API is called!!");
        return okSuccessResponse(otpService.verifyOTP(verifyOTPReqDTO), "verify_otp_success");

    }

    @PostMapping(Urls.RESET_PASSWORD_USER)
    @Operation(summary = "User Reset Password API")
    public ResponseEntity<?> resetPassword(@RequestBody @Valid ResetPasswordReqDTO resetPasswordUserReqDTO) {
        logger.info("User Reset Password API is called!!");
        userService.resetPassword(resetPasswordUserReqDTO);
        return okSuccessResponse("reset_password_success");
    }

    @PostMapping(value = Urls.LOG_OUT)
    @Operation(summary = "User logout API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> userLogout(@RequestHeader(value = Constant.ACCESS_TOKEN) String accessToken, @RequestBody DeviceUniqueIdDTO deviceUniqueIdDto) {
        logger.info("User Log Out API is called.");
        userService.userLogout(accessToken, deviceUniqueIdDto);
        return okSuccessResponse("logout_success");
    }

    @PostMapping(value = Urls.HOME_SCREEN)
    @Operation(summary = "Home screen api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> homeScreenList(@RequestBody @Valid HomeScreenReqDTO homeScreenReqDTO) {
        logger.info("Home screen api is called.");
        return okSuccessResponse(userService.homeScreenList(homeScreenReqDTO), "common_success");
    }

    @GetMapping(value = Urls.VIEW)
    @Operation(summary = "View My Profile Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> viewMyProfile() {
        logger.info("View my profile api is called.");
        return okSuccessResponse(userService.viewMyProfile(), "common_success");
    }

    @PutMapping(value = Urls.EDIT)
    @Operation(summary = "Edit Profile Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> editProfile(@Valid @RequestBody EditProfileReqDTO editProfileReqDTO) {
        logger.info("Edit profile api is called.");
        return okSuccessResponse(userService.editProfile(editProfileReqDTO), "edit_profile_success");
    }

    @DeleteMapping(Urls.DELETE)
    @Operation(summary = "Delete User Account Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> deleteUserAccount(@Valid @RequestBody DeactivateUserAccountReqDTO deactivateUserAccountReqDTO) {
        logger.info("Delete user account api called.");
        userService.deleteUserAccount(deactivateUserAccountReqDTO);
        return okSuccessResponse("delete_account_success");
    }

    @PutMapping(Urls.DEACTIVATE)
    @Operation(summary = "Deactivate user account api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> deactivateUserAccount(@Valid @RequestBody DeactivateUserAccountReqDTO deactivateUserAccountReqDTO) {
        logger.info("Deactivate user account api called.");
        userService.deactivateUserAccount(deactivateUserAccountReqDTO);
        return okSuccessResponse("user_account_deactivate_success");
    }

    @PutMapping(value = Urls.ACHIEVEMENT_TITLE + Urls.EDIT)
    @Operation(summary = "Edit User Achievement Title Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> updateAchievementTitle(@Valid @RequestBody UserIdAchievementIdDTO editProfileReqDTO) {
        logger.info("Edit Achievement Title Api is called.");
        userService.updateUserTitleByAchievementId(editProfileReqDTO);
        return okSuccessResponse("profile_achievement_title_update_success");
    }

    @PutMapping(value = Urls.ACHIEVEMENT_BORDER + Urls.EDIT)
    @Operation(summary = "Edit User Achievement Border Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> updateUserBorderByAchievementId(@Valid @RequestBody UserIdAchievementIdDTO userIdAchievementIdDTO) {
        logger.info("Edit User Achievement Border Api is called.");
        userService.updateUserBorderByAchievementId(userIdAchievementIdDTO);
        return okSuccessResponse("profile_achievement_border_update_success");
    }

    @PutMapping(value = Urls.ACHIEVEMENT_MEDAL + Urls.EDIT)
    @Operation(summary = "Edit User Achievement Medal Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> updateUserMedalByAchievementId(@Valid @RequestBody UserIdAchievementIdDTO userIdAchievementIdDTO) {
        logger.info("Edit User Achievement Medal Api is called.");
        userService.updateUserMedalByAchievementId(userIdAchievementIdDTO);
        return okSuccessResponse("profile_achievement_badge_update_success");
    }

//    @PostMapping(value = Urls.UPDATE_REACTIVATION_REQUEST_PENDING_STATUS)
//    @Operation(summary = "Update Reactivation Request Pending Status API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
//    public ResponseEntity<?> updateReactivationRequestPending(@RequestBody @Valid UserReactivationReqDTO userReactivationReqDTO) {
//        logger.info("Update Reactivation Request Pending Status API is called.");
//        userService.updateReactivationRequestPending(userReactivationReqDTO.isReactivationRequestPending(), userReactivationReqDTO.getUserId());
//        return okSuccessResponse("common_success");
//    }

    @PutMapping(Urls.REACTIVATE)
    @Operation(summary = "Reactivate User By User api")
    public ResponseEntity<?> reactivateUser(@RequestBody ForgotPasswordReqDTO forgotPasswordReqDTO) {
        logger.info("Reactivate User by user api called.");
        String message = userService.reActivateUserByEmailId(forgotPasswordReqDTO);
        return okSuccessResponse(message);
    }

    @PutMapping(Urls.THEME_MODE)
    @Operation(summary = "Update Theme Mode For User", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> updateThemeMode(@Valid @RequestBody UpdateThemeModeReqDTO updateThemeModeReqDTO) {
        return okSuccessResponse(userService.updateThemeMode(updateThemeModeReqDTO),"theme_mode_updated_successfully");
    }


}
