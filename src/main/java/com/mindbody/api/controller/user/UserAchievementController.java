package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.ClaimAchievementReqDTO;
import com.mindbody.api.dto.SubmitUserAchievementReqDTO;
import com.mindbody.api.dto.UserAchievementReqDTO;
import com.mindbody.api.dto.UserIdDTO;
import com.mindbody.api.service.UserAchievementService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.USER_ACHIEVEMENTS)
@Tag(name = "User-Achievements", description = "User Achievements User APIs")
public class UserAchievementController extends BaseController {

    @Autowired
    private UserAchievementService userAchievementService;

    @PostMapping(value = Urls.SUBMIT_USER_ACHIEVEMENT)
    @Operation(summary = "Submit User Achievements Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> submitUserAchievements(@RequestBody @Valid SubmitUserAchievementReqDTO submitUserAchievementReqDTO) {
        logger.info("Submit User Achievements Api called..!!!");
        userAchievementService.submitAchievement(submitUserAchievementReqDTO);
        return okSuccessResponse("common_success");
    }

    @PostMapping(value = Urls.CLAIM_USER_ACHIEVEMENT)
    @Operation(summary = "Claim User Achievements Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> claimUserAchievements(@RequestBody @Valid ClaimAchievementReqDTO claimAchievementReqDTO) {
        logger.info("Claim User Achievements Api called..!!!");
        userAchievementService.claimUserAchievement(claimAchievementReqDTO);
        return okSuccessResponse("common_success");
    }

    @PostMapping(value = Urls.GET_USER_TITLES)
    @Operation(summary = "Get user titles data Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getUserTitlesData(@RequestBody @Valid UserIdDTO userIdDTO) {
        logger.info("Get user titles data Api called..!!!");
        return okSuccessResponse(userAchievementService.getUserTitlesData(userIdDTO),"common_success");
    }

    @PostMapping(value = Urls.GET_USER_ACHIEVEMENT)
    @Operation(summary = "Get particular user achievement by enum and user ID Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getUserAchievementByEnumAndUserId(@RequestBody @Valid UserAchievementReqDTO userAchievementReqDTO) {
        logger.info("Get particular user achievement by enum and user ID Api called..!!!");
        return okSuccessResponse(userAchievementService.getUserAchievementByEnumAndUserId(userAchievementReqDTO),"common_success");
    }

    @PostMapping(value = Urls.GET_USER_BADGES)
    @Operation(summary = "Get user badges data Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getUserBadgesData(@RequestBody @Valid UserIdDTO userIdDTO) {
        logger.info("Get user badges data Api called..!!!");
        return okSuccessResponse(userAchievementService.getUserBadgesData(userIdDTO),"common_success");
    }

    @PostMapping(value = Urls.GET_USER_BORDERS)
    @Operation(summary = "Get user border data Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getUserBorderData(@RequestBody @Valid UserIdDTO userIdDTO) {
        logger.info("Get user border data Api called..!!!");
        return okSuccessResponse(userAchievementService.getUserBorderData(userIdDTO),"common_success");
    }
}
