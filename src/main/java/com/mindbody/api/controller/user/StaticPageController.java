package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.cms.StaticPageResDTO;
import com.mindbody.api.service.StaticPageService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.STATIC_PAGE)
@Tag(name = "Static-Page-Management-User", description = "Static Page user APIs")
public class StaticPageController extends BaseController {
    private final StaticPageService staticPageService;

    public StaticPageController(StaticPageService staticPageService) {
        this.staticPageService = staticPageService;
    }

    @GetMapping(value = Urls.VIEW)
    @Operation(summary = "View Static Page API for User", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    public ResponseEntity<?> viewStaticPage(@RequestParam String staticPageType) {
        logger.info("View Static Page API for App User is called!!");
        StaticPageResDTO staticPageResDTO= staticPageService.viewStaticPage(staticPageType);
        return okSuccessResponse(staticPageResDTO,"view_static_page_success");
    }

}
