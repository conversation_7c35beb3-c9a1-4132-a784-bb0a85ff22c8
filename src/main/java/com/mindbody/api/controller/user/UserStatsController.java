package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.UserStatsTimeSpentReqDTO;
import com.mindbody.api.service.UserStatsService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER_STATS)
@Tag(name = "User Stats", description = "User Stats APIs")
public class UserStatsController extends BaseController {

    private final UserStatsService userStatsService;

    public UserStatsController(UserStatsService userStatsService) {
        this.userStatsService = userStatsService;
    }

    @PostMapping(Urls.SUBMIT_CATEGORY_TIME_SPENT)
    @Operation(summary = "Submit category time spent Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> saveCategoryTimeSpentForUser(@RequestBody @Valid UserStatsTimeSpentReqDTO userStatsTimeSpentReqDTO) {
        logger.info("Submit category time spent API is called for user!!");
        userStatsService.saveCategoryStatsForUser(userStatsTimeSpentReqDTO);
        return okSuccessResponse("common_success");
    }

}
