package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.ExclusiveAudioListReqDTO;
import com.mindbody.api.service.UserUnlockedAudioService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for managing user unlocked audio functionality
 */
@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.AUDIO + Urls.EXCLUSIVE)
@Tag(name = "User-Unlocked-Audio", description = "User Unlocked Audio APIs")
@RequiredArgsConstructor
public class UserUnlockedAudioController extends BaseController {

    private final UserUnlockedAudioService userUnlockedAudioService;

    /**
     * Get all exclusive audios with their unlock status for a user
     *
     * @param exclusiveAudioListReqDTO Request DTO containing user ID and pagination info
     * @return List of exclusive audios with their unlock status
     */
    @PostMapping(value = Urls.LIST)
    @Operation(summary = "List exclusive audios with unlock status API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getExclusiveAudiosWithStatus(@RequestBody @Valid ExclusiveAudioListReqDTO exclusiveAudioListReqDTO) {
        logger.info("List exclusive audios with unlock status API is called!!");
        return okSuccessResponse(
                userUnlockedAudioService.getExclusiveAudiosWithStatus(
                        exclusiveAudioListReqDTO.getUserId(),
                        exclusiveAudioListReqDTO.getQueryToSearch(),
                        ((com.mindbody.api.filter.Page)exclusiveAudioListReqDTO.getPage()).toPageable(exclusiveAudioListReqDTO.getSortBy())
                ),
                "common_success"
        );
    }

    /**
     * Get locked exclusive audios for a user
     *
     * @param exclusiveAudioListReqDTO Request DTO containing user ID and pagination info
     * @return List of locked exclusive audios
     */
    @PostMapping(value = Urls.LIST + "/locked")
    @Operation(summary = "List locked exclusive audios API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getLockedExclusiveAudios(@RequestBody @Valid ExclusiveAudioListReqDTO exclusiveAudioListReqDTO) {
        logger.info("List locked exclusive audios API is called!!");
        return okSuccessResponse(
                userUnlockedAudioService.getLockedExclusiveAudios(
                        exclusiveAudioListReqDTO.getUserId(),
                        exclusiveAudioListReqDTO.getQueryToSearch(),
                        ((com.mindbody.api.filter.Page)exclusiveAudioListReqDTO.getPage()).toPageable(exclusiveAudioListReqDTO.getSortBy())
                ),
                "common_success"
        );
    }

    /**
     * Get unlocked exclusive audios for a user
     *
     * @param exclusiveAudioListReqDTO Request DTO containing user ID and pagination info
     * @return List of unlocked exclusive audios
     */
    @PostMapping(value = Urls.LIST + "/unlocked")
    @Operation(summary = "List unlocked exclusive audios API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getUnlockedExclusiveAudios(@RequestBody @Valid ExclusiveAudioListReqDTO exclusiveAudioListReqDTO) {
        logger.info("List unlocked exclusive audios API is called!!");
        return okSuccessResponse(
                userUnlockedAudioService.getUnlockedExclusiveAudios(
                        exclusiveAudioListReqDTO.getUserId(),
                        exclusiveAudioListReqDTO.getQueryToSearch(),
                        ((com.mindbody.api.filter.Page)exclusiveAudioListReqDTO.getPage()).toPageable(exclusiveAudioListReqDTO.getSortBy())
                ),
                "common_success"
        );
    }

    /**
     * Unlock an audio for a user
     *
     * @param userId User ID
     * @param audioId Audio ID
     * @return Success response
     */
    @PostMapping(value = "/unlock/{userId}/{audioId}")
    @Operation(summary = "Unlock audio API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> unlockAudio(
            @PathVariable("userId") Long userId,
            @PathVariable("audioId") Long audioId) {
        logger.info("Unlock audio API is called!!");
        userUnlockedAudioService.unlockAudio(userId, audioId);
        return okSuccessResponse("audio_unlocked_success");
    }

    /**
     * Lock an audio for a user
     *
     * @param userId User ID
     * @param audioId Audio ID
     * @return Success response
     */
    @PostMapping(value = "/lock/{userId}/{audioId}")
    @Operation(summary = "Lock audio API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> lockAudio(
            @PathVariable("userId") Long userId,
            @PathVariable("audioId") Long audioId) {
        logger.info("Lock audio API is called!!");
        userUnlockedAudioService.lockAudio(userId, audioId);
        return okSuccessResponse("audio_locked_success");
    }
}
