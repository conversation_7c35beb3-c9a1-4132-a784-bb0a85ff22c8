package com.mindbody.api.controller.user;


import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.dto.UserQuestionAnswerReqDTO;
import com.mindbody.api.service.QuestionService;
import com.mindbody.api.service.UserAnswerService;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.QUESTION)
@Tag(name = "Question-Answer-User", description = "Question Answers User APIs")
public class QuestionAnswerController extends BaseController {

    private final UserAnswerService userAnswerService;

    private final QuestionService questionService;


    public QuestionAnswerController(UserAnswerService userAnswerService, QuestionService questionService) {
        this.userAnswerService = userAnswerService;
        this.questionService = questionService;
    }

    @PostMapping(value = Urls.LIST)
    @Operation(summary = "List question user API")
    public ResponseEntity<?> listQuestionForUser(@RequestBody @Valid CommonListDTO commonListDTO) {
        logger.info("List question user API is called!!");
        boolean checkIsActiveRecord = true;
        return okSuccessResponse(questionService.listQuestion(commonListDTO, checkIsActiveRecord), "common_success");
    }

    @PostMapping(value = Urls.ANSWER)
    @Operation(summary = "User question's answer API")
    public ResponseEntity<?> userQuestionAnswers(@RequestBody @Valid UserQuestionAnswerReqDTO userQuestionAnswerReqDTO) {
        logger.info("User question's answer API is called!!");
        return okSuccessResponse(userAnswerService.userQuestionAnswers(userQuestionAnswerReqDTO), "answer_submitted_success");
    }
}
