package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.notification.SubscribeTopicReqDTO;
import com.mindbody.api.service.NotificationService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.NOTIFICATION)
@Tag(name = "User-Notification", description = "Notification APIs")

public class UserNotificationController extends BaseController {

    private final NotificationService notificationService;

    public UserNotificationController(NotificationService notificationService) {
        this.notificationService = notificationService;
    }

    @GetMapping(value = Urls.LIST)
    @Operation(summary = "List notification for user API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    public ResponseEntity<?> listNotificationsForUser() {
        logger.info("List notifications for user API is called!!");
        return okSuccessResponse(notificationService.listNotificationsForUser(), "common_success");
    }

    @PostMapping(value = Urls.SUBSCRIBE_USER_FOR_TOPIC)
    @Operation(summary = "Subscribe user for notification topic API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    public ResponseEntity<?> subscribeUserForTopic(@Valid @RequestBody SubscribeTopicReqDTO subscribeTopicReqDTO) {
        logger.info("Subscribe user for notification topic API is called!!");
        notificationService.subscribeUserForTopic(subscribeTopicReqDTO);
        return okSuccessResponse("user_subscribed_topic_success");

    }
}
