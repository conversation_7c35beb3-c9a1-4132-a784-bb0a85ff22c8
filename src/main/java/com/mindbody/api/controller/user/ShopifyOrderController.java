package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.shopify.OrdersListGraphqlReqDTO;
import com.mindbody.api.service.ShopifyOrderService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.SHOPIFY + Urls.ORDERS)
@Tag(name = "Shopify-User-Orders", description = "Shopify User Orders APIs")
public class ShopifyOrderController extends BaseController {

    private final ShopifyOrderService shopifyOrderService;

    public ShopifyOrderController(ShopifyOrderService shopifyOrderService) {
        this.shopifyOrderService = shopifyOrderService;
    }

    @GetMapping(Urls.LIST)
    @Operation(summary = "Get all orders list API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getAllOrdersList(@RequestParam int limit) {
        logger.info("Get all orders list API is called!!");
        return okSuccessResponse(shopifyOrderService.getAllOrdersList(limit), "common_success");
    }

    @PostMapping(Urls.SHOPIFY_ADMIN_ORDERS_LIST)
    @Operation(summary = "Get all orders list API using shopify admin graphql", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getAllOrdersListShopifyAdminGraphql(@Valid @RequestBody OrdersListGraphqlReqDTO ordersListGraphqlReqDTO) {
        logger.info("Get all orders list API using shopify admin graphql is called!!");
        return okSuccessResponse(shopifyOrderService.getAllOrdersListShopifyAdminGraphql(ordersListGraphqlReqDTO), "common_success");
    }

    @GetMapping(Urls.TRACK_ORDER)
    @Operation(summary = "Get tracking information of fulfilled order by orderId", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> trackOrder(@RequestParam String id) {
        logger.info("Track Order API is called!!");
        return okSuccessResponse(shopifyOrderService.trackOrder(id), "common_success");
    }

}
