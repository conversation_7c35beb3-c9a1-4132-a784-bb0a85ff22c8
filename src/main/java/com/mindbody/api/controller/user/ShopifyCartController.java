package com.mindbody.api.controller.user;


import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.shopify.*;
import com.mindbody.api.service.ShopifyCartService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.SHOPIFY + Urls.CART)
@Tag(name = "Shopify-User-Cart", description = "Shopify User Cart APIs")
public class ShopifyCartController extends BaseController {

    private final ShopifyCartService shopifyCartService;

    public ShopifyCartController(ShopifyCartService shopifyCartService) {
        this.shopifyCartService = shopifyCartService;
    }

    @PostMapping(value = Urls.VIEW)
    @Operation(summary = "View cart API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> viewCart(@RequestBody @Valid ViewCartReqDTO viewCartReqDTO) {
        logger.info("View cart API is called!!");
        return okSuccessResponse(shopifyCartService.viewCart(viewCartReqDTO), "common_success");
    }

    @PostMapping(value = Urls.CREATE)
    @Operation(summary = "Create cart and first product to cart API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> createCartWithFirstProduct(@RequestBody @Valid CreateCartWithFirstProductReqDTO createCartWithFirstProductReqDTO) {
        logger.info("Create cart and first product to cart API is called!!");
        return okSuccessResponse(shopifyCartService.createCartWithFirstProduct(createCartWithFirstProductReqDTO), "cart_create_success");
    }

    @PostMapping(value = Urls.ADD)
    @Operation(summary = "Add product to cart API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> addProductToCart(@RequestBody @Valid AddProductToCartReqDTO addProductToCartReqDTO) {
        logger.info("Add product to cart API is called!!");
        return okSuccessResponse(shopifyCartService.addProductToCart(addProductToCartReqDTO), "add_product_cart_success");
    }

    @PutMapping(value = Urls.EDIT)
    @Operation(summary = "Update product to cart API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> updateCart(@RequestBody @Valid UpdateCartReqDTO updateCartReqDTO) {
        logger.info("Update product to cart API is called!!");
        return okSuccessResponse(shopifyCartService.updateCart(updateCartReqDTO), "update_product_cart_success");
    }

    @DeleteMapping(value = Urls.DELETE)
    @Operation(summary = "Remove product from cart API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> removeProductFromCart(@RequestBody @Valid RemoveProductFromCartReqDTO removeProductFromCartReqDTO) {
        logger.info("Remove product from cart API is called!!");
        return okSuccessResponse(shopifyCartService.removeProductFromCart(removeProductFromCartReqDTO), "remove_product_cart_success");
    }

}
