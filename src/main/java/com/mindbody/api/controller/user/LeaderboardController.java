package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.GenerateLeaderboardReqDTO;
import com.mindbody.api.dto.MeSectionResponseDTO;
import com.mindbody.api.dto.PinUserRequestDTO;
import com.mindbody.api.service.LeaderboardService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.LEADERBOARD)
@Tag(name = "Leaderboard", description = "Leaderboard APIs")
public class LeaderboardController extends BaseController {

    private final LeaderboardService leaderboardService;

    public LeaderboardController(LeaderboardService leaderboardService) {
        this.leaderboardService = leaderboardService;
    }

    @PostMapping(Urls.LIST)
    @Operation(summary = "Generate leaderboard", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> generateLeaderboard(@RequestBody @Valid GenerateLeaderboardReqDTO generateLeaderboardReqDTO) {
        logger.info("Generate Leaderboard API is called for user!!");
        return okSuccessResponse(leaderboardService.generateLeaderboard(generateLeaderboardReqDTO), "common_success");
    }

    /**
     * API to pin a user on the leaderboard.
     *
     * @param request DTO containing pinnedUserId and forUserId.
     * @return ResponseEntity with success or error message.
     */
    @PostMapping(Urls.LEADERBOARD_PIN_USER)
    @Operation(summary = "Pin leaderboard user", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> pinLeaderboardUser(@Valid @RequestBody PinUserRequestDTO request) {
        leaderboardService.pinLeaderboardUser(request);
        return okSuccessResponse("user_pinned_success");
    }

    /**
     * API to unpin a user from the leaderboard.
     *
     * @param pinUserRequestDTO DTO containing pinnedUserId and forUserId.
     * @return ResponseEntity with success or error message.
     */
    @DeleteMapping(Urls.LEADERBOARD_UNPIN_USER)
    @Operation(summary = "Unpin leaderboard user", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> unpinUser(@Valid @RequestBody PinUserRequestDTO pinUserRequestDTO) {
        leaderboardService.unpinUser(pinUserRequestDTO);
        return okSuccessResponse("user_unpinned_success");
    }

    /**
     * API to fetch the "Me" section of the leaderboard.
     *
     * @param userId ID of the current user.
     * @return ResponseEntity containing the "Me" section details.
     */
    @GetMapping(Urls.LEADERBOARD_VIEW_PROFILE)
    @Operation(summary = "View leaderboard Profile", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getLeaderboardProfile(@RequestParam Long currentUserId, @RequestParam Long userId, @RequestParam String userType) {
        MeSectionResponseDTO response = leaderboardService.getLeaderboardProfile(currentUserId, userType, userId);
        return okSuccessResponse(response,"common_success");
    }


}
