package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.RedeemItemReqDTO;
import com.mindbody.api.dto.RedeemableItemListReqDTO;
import com.mindbody.api.service.UserRedeemableItemService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for managing user redeemable items functionality
 */
@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + "/redeemable-items")
@Tag(name = "User-Redeemable-Items", description = "User Redeemable Items APIs")
@RequiredArgsConstructor
public class UserRedeemableItemController extends BaseController {

    private final UserRedeemableItemService userRedeemableItemService;

    /**
     * List all redeemable items (audios and/or rewards) with their redemption status
     *
     * @param redeemableItemListReqDTO Request DTO containing filters and pagination
     * @return List of redeemable items with their status
     */
    @PostMapping(value = Urls.LIST)
    @Operation(summary = "List redeemable items with status API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getRedeemableItemsWithStatus(@RequestBody @Valid RedeemableItemListReqDTO redeemableItemListReqDTO) {
        logger.info("List redeemable items with status API is called!!");
        Sort sort = redeemableItemListReqDTO.getSortBy() != null ?
                Sort.by(redeemableItemListReqDTO.getSortBy().getDirection(), redeemableItemListReqDTO.getSortBy().getProperty()) :
                Sort.unsorted();
        return okSuccessResponse(
                userRedeemableItemService.getRedeemableItemsWithStatus(
                        redeemableItemListReqDTO.getUserId(),
                        redeemableItemListReqDTO.getQueryToSearch(),
                        redeemableItemListReqDTO.getItemType(),
                        PageRequest.of(
                            redeemableItemListReqDTO.getPage().getPageId(),
                            redeemableItemListReqDTO.getPage().getLimit(),
                            sort
                        )
                ),
                "common_success"
        );
    }

    /**
     * Redeem an item (audio or reward) using points
     *
     * @param redeemItemReqDTO Request DTO containing user ID, item ID, and item type
     * @return Success response with redemption details
     */
    @PostMapping(value = "/redeem")
    @Operation(summary = "Redeem item API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> redeemItem(@RequestBody @Valid RedeemItemReqDTO redeemItemReqDTO) {
        logger.info("Redeem item API is called!!");
        return okSuccessResponse(
                userRedeemableItemService.redeemItem(redeemItemReqDTO),
                "item_redeemed_success"
        );
    }
} 