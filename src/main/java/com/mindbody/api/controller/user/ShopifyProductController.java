package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.shopify.ProductByCategoryReqDTO;
import com.mindbody.api.dto.shopify.ProductByIdReqDTO;
import com.mindbody.api.dto.shopify.ProductListReqDTO;
import com.mindbody.api.service.ShopifyProductService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.SHOPIFY + Urls.PRODUCT)
@Tag(name = "Shopify-User-Product", description = "Shopify user product APIs")
public class ShopifyProductController extends BaseController {

    private final ShopifyProductService shopifyProductService;

    public ShopifyProductController(ShopifyProductService shopifyProductService) {
        this.shopifyProductService = shopifyProductService;
    }

    @PostMapping(Urls.LIST)
    @Operation(summary = "Get all product list API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getAllProductList(@RequestBody @Valid ProductListReqDTO productListReqDTO) {
        logger.info("Get all product API is called!!");
        return okSuccessResponse(shopifyProductService.getAllProductList(productListReqDTO), "common_success");
    }

    @PostMapping(value = Urls.ID)
    @Operation(summary = "Get product by id API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getProductById(@RequestBody @Valid ProductByIdReqDTO productByIdReqDTO) {
        logger.info("Get product by id API is called!!");
        return okSuccessResponse(shopifyProductService.getProductById(productByIdReqDTO), "common_success");
    }

    @PostMapping(value = Urls.CATEGORY + Urls.LIST)
    @Operation(summary = "Get product by category API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getProductByCategory(@RequestBody @Valid ProductByCategoryReqDTO productByCategoryReqDTO) {
        logger.info("Get product by category API is called!!");
        return okSuccessResponse(shopifyProductService.getProductByCategory(productByCategoryReqDTO), "common_success");
    }

}
