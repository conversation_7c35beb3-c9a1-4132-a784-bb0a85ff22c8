package com.mindbody.api.controller.user;


import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.FavoriteAudioReqDTO;
import com.mindbody.api.dto.FavoriteListReqDTO;
import com.mindbody.api.dto.FavoriteWorkoutPlanReqDTO;
import com.mindbody.api.service.FavoriteAudioService;
import com.mindbody.api.service.FavoriteWorkoutPlanService;
import com.mindbody.api.service.UserService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER)
@Tag(name = "Favorite-User", description = "Favorite Audio/Workout/Product User apis")
public class FavoriteController extends BaseController {

    private final FavoriteWorkoutPlanService favoriteWorkoutPlanService;

    private final FavoriteAudioService favoriteAudioService;

    private final UserService userService;

    public FavoriteController(FavoriteWorkoutPlanService favoriteWorkoutPlanService, FavoriteAudioService favoriteAudioService, UserService userService) {
        this.favoriteWorkoutPlanService = favoriteWorkoutPlanService;
        this.favoriteAudioService = favoriteAudioService;
        this.userService = userService;
    }

    @PostMapping(value = Urls.AUDIO + Urls.FAVORITE)
    @Operation(summary = "Favorite audio API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> favoriteAudio(@RequestBody @Valid FavoriteAudioReqDTO favoriteAudioReqDTO) {
        logger.info("Favorite audio API is called!!");
        boolean isFavorite = favoriteAudioService.favoriteAudio(favoriteAudioReqDTO);
        if (isFavorite) {
            return okSuccessResponse("audio_favorite_success");
        } else {
            return okSuccessResponse("audio_remove_favorite_success");
        }
    }

    @PostMapping(value = Urls.WORKOUT_PLAN + Urls.FAVORITE)
    @Operation(summary = "Favorite workout plan for user API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> favoriteWorkoutPlan(@RequestBody @Valid FavoriteWorkoutPlanReqDTO favoriteWorkoutPlanReqDTO) {
        logger.info("Favorite workout plan for user API is called!!");
        boolean isFavorite = favoriteWorkoutPlanService.favoriteWorkoutPlan(favoriteWorkoutPlanReqDTO);
        if (isFavorite) {
            return okSuccessResponse("workout_plan_favorite_success");
        } else {
            return okSuccessResponse("workout_plan_remove_favorite_success");
        }
    }

    @PostMapping(value = Urls.FAVORITE + Urls.LIST)
    @Operation(summary = "User's Favorite list api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> favoriteList(@Valid @RequestBody FavoriteListReqDTO favoriteListReqDTO) {
        logger.info("User's Favorite list api is called.");
        return okSuccessResponse(userService.favoriteList(favoriteListReqDTO), "common_success");
    }
}
