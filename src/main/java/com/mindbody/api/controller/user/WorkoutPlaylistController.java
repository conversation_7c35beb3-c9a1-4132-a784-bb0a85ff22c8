package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.service.WorkoutPlaylistService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.WORKOUT_PLAYLIST)
@Tag(name = "Workout-Playlist-User", description = "Workout Playlist User APIs")
public class WorkoutPlaylistController extends BaseController {
    private final WorkoutPlaylistService workoutPlaylistService;

    public WorkoutPlaylistController(WorkoutPlaylistService workoutPlaylistService) {
        this.workoutPlaylistService = workoutPlaylistService;
    }

    @GetMapping(Urls.LIST)
    @Operation(summary = "List Workout Playlist API based on Workout Playlist Type selected by User", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> listWorkoutPlaylistForWorkoutPlaylistType(@RequestParam String workoutPlaylistType) {
        logger.info("List workout playlist API for selected workout playlist type is called for user!!");
        return okSuccessResponse(workoutPlaylistService.listWorkoutPlaylistForSelectedWorkoutPlaylistType(workoutPlaylistType), "common_success");
    }

}
