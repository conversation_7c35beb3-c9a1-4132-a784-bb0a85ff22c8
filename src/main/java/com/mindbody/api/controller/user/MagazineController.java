package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.CommonListDTO;
import com.mindbody.api.service.MagazineService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.MAGAZINE)
@Tag(name = "Magazine-User", description = "Magazine User APIs")
public class MagazineController extends BaseController {

    private final MagazineService magazineService;

    public MagazineController(MagazineService magazineService) {
        this.magazineService = magazineService;
    }

    @PostMapping(Urls.LIST)
    @Operation(summary = "List magazine API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    public ResponseEntity<?> listMagazineForUser(@RequestBody @Valid CommonListDTO commonListDTO) {
        logger.info("List magazine API For User is called!!");
        boolean checkIsActiveRecord = true;
        return okSuccessResponse(magazineService.listMagazine(commonListDTO, checkIsActiveRecord), "common_success");
    }
}
