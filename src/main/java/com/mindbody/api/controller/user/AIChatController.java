package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.MarkMessageAsReadReqDTO;
import com.mindbody.api.service.ChatMessagesService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.AI_CHAT)
@Tag(name = "AI-Chat", description = "AI Chat APIs")
public class AIChatController extends BaseController {

    private final ChatMessagesService chatMessagesService;

    public AIChatController(ChatMessagesService chatMessagesService){
        this.chatMessagesService=chatMessagesService;
    }

    @GetMapping(value = Urls.VIEW_CATEGORY_DETAILS)
    @Operation(summary = "Get chat category details API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getChatCategoryDetails(@RequestParam Long userId) {
        logger.info("Get chat category details API is called!!");
        return okSuccessResponse(chatMessagesService.getChatCategoryDetails(userId),"common_success");
    }

    @PostMapping(value = Urls.MARK_AI_CHAT_MESSAGES_READ)
    @Operation(summary = "Mark AI Chat messages as read API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> markMessagesAsRead(@Valid @RequestBody MarkMessageAsReadReqDTO markMessageAsReadReqDTO) {
        logger.info("Mark AI Chat messages as read API is called!!");
        chatMessagesService.markMessagesAsRead(markMessageAsReadReqDTO);
        return okSuccessResponse("common_success");
    }

}
