package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.AddStreakReqDTO;
import com.mindbody.api.service.UserStreakService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.STREAK)
@Tag(name = "Streak-User", description = "Streak User APIs")
public class StreakController extends BaseController {

    private final UserStreakService userStreakService;

    public StreakController(UserStreakService userStreakService) {
        this.userStreakService = userStreakService;
    }

    @PostMapping
    @Operation(summary = "Create user streak API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    public ResponseEntity<?> createStreak(@RequestBody @Valid AddStreakReqDTO addStreakReqDTO) {
        logger.info("Create user streak API is called!!");
        return okSuccessResponse(userStreakService.createStreak(addStreakReqDTO), "streak_create_success");
    }

}

