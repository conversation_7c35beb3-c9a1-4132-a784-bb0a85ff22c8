package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.UserIdDTO;
import com.mindbody.api.service.AstrologyService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.ASTROLOGY)
@Tag(name = "Astrology-User", description = "Astrology User APIs")
public class AstrologyController extends BaseController {

    private final AstrologyService astrologyService;

    public AstrologyController(AstrologyService astrologyService) {
        this.astrologyService = astrologyService;
    }

    @PostMapping(value = Urls.GENERATE)
    @Operation(summary = "Generate user astrology detail API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getUserAstrologyDetails(@RequestBody @Valid UserIdDTO userIdDTO) {
        logger.info("Generate user astrology detail API is called!!");
        return okSuccessResponse(astrologyService.generateUserAstrologyDetails(userIdDTO), "common_success");
    }

    @PostMapping(value = Urls.GENERATE_DAILY_HOROSCOPE)
    @Operation(summary = "Generate daily horoscope for user API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getDailyHoroscopeForUser(@RequestBody @Valid UserIdDTO userIdDTO) {
        logger.info("Generate daily horoscope for user API is called!!");
        return okSuccessResponse(astrologyService.generateUserDailyHoroscope(userIdDTO), "common_success");
    }

    @PostMapping(value = Urls.GENERATE_MONTHLY_HOROSCOPE)
    @Operation(summary = "Generate monthly horoscope for user API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getMonthlyHoroscopeForUser(@RequestBody @Valid UserIdDTO userIdDTO) {
        logger.info("Generate monthly horoscope for user API is called!!");
        return okSuccessResponse(astrologyService.generateUserMonthlyHoroscope(userIdDTO), "common_success");
    }


}
