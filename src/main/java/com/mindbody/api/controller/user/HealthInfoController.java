package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.UserHealthReqDTO;
import com.mindbody.api.dto.UserIdDTO;
import com.mindbody.api.service.UserHealthInfoService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.HEALTH)
@Tag(name = "Health-Info-User", description = "Health Info User APIs")
public class HealthInfoController extends BaseController {

    private final UserHealthInfoService userHealthInfoService;

    public HealthInfoController(UserHealthInfoService userHealthInfoService) {
        this.userHealthInfoService = userHealthInfoService;
    }

    @PostMapping(value = Urls.CALCULATE)
    @Operation(summary = "Calculate user health info API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> calculateUserHealthInfo(@RequestBody UserHealthReqDTO userHealthReqDTO) {
        logger.info("Calculate user health info API is called!!");
        return okSuccessResponse(userHealthInfoService.calculateUserHealthInfo(userHealthReqDTO),"user_health_info_success");
    }

    @PostMapping(value = Urls.VIEW)
    @Operation(summary = "View user health info API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> viewUserHealth(@RequestBody UserIdDTO userIdDTO) {
        logger.info("View user health info API is called!!");
        return okSuccessResponse(userHealthInfoService.viewUserHealth(userIdDTO),"common_success");
    }
}
