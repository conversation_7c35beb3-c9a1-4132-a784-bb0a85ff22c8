package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.service.ProductCategoryService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.PRODUCT_CATEGORY)
@Tag(name = "Product-Category-User", description = "Product Category User APIs")
public class ProductCategoryController extends BaseController {

    private final ProductCategoryService productCategoryService;

    public ProductCategoryController(ProductCategoryService productCategoryService) {
        this.productCategoryService = productCategoryService;
    }

    @GetMapping(Urls.LIST)
    @Operation(summary = "List Product category API for user", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    public ResponseEntity<?> listProductCategoryForUser() {
        logger.info("List Product category API For User is called!!");
        return okSuccessResponse(productCategoryService.listProductCategoryForUserAndCms(), "common_success");
    }
}
