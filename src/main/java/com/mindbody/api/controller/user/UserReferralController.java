package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.ExclusiveAudioListReqDTO;
import com.mindbody.api.dto.ReferralListReqDTO;
import com.mindbody.api.dto.SubmitReferralCodeDTO;
import com.mindbody.api.service.UserReferralService;
import com.mindbody.api.service.UserUnlockedAudioService;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.USER_REFERRAL)
@Tag(name = "User-Referral", description = "User Referral APIs")
@RequiredArgsConstructor
public class UserReferralController extends BaseController {

    private final UserReferralService userReferralService;
    private final UserUnlockedAudioService userUnlockedAudioService;

    @PostMapping(value = Urls.SUBMIT_USER_REFERRAL)
    @Operation(summary = "Submit User Referral Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> submitUserReferral(@RequestBody @Valid SubmitReferralCodeDTO submitReferralCodeDTO) {
        logger.info("Submit User Referral API is called!!");
        userReferralService.submitReferralCode(submitReferralCodeDTO);
        return okSuccessResponse("referral_code_submitted_success");
    }

    @PostMapping(value = Urls.LIST_USER_REFERRAL)
    @Operation(summary = "List User Referral Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> listUserReferral(@Valid @RequestBody ReferralListReqDTO referralListReqDTO) {
        logger.info("List User Referral API is called!!");
        return okSuccessResponse(userReferralService.listReferrals(referralListReqDTO),"common_success");
    }

    /**
     * Get all exclusive audios with their unlock status for a user
     *
     * @param exclusiveAudioListReqDTO Request DTO containing user ID and pagination info
     * @return List of exclusive audios with their unlock status
     */
    @PostMapping(value = Urls.EXCLUSIVE + Urls.LIST)
    @Operation(summary = "List exclusive audios with unlock status API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getExclusiveAudiosWithStatus(@RequestBody @Valid ExclusiveAudioListReqDTO exclusiveAudioListReqDTO) {
        logger.info("List exclusive audios with unlock status API is called!!");
        return okSuccessResponse(
                userUnlockedAudioService.getExclusiveAudiosWithStatus(
                        exclusiveAudioListReqDTO.getUserId(),
                        exclusiveAudioListReqDTO.getQueryToSearch(),
                        PageRequest.of(
                            exclusiveAudioListReqDTO.getPage().getPageId(),
                            exclusiveAudioListReqDTO.getPage().getLimit(),
                            exclusiveAudioListReqDTO.getSortBy() != null ?
                                Sort.by(exclusiveAudioListReqDTO.getSortBy().getDirection(), exclusiveAudioListReqDTO.getSortBy().getProperty()) :
                                Sort.unsorted()
                        )
                ),
                "common_success"
        );
    }
}
