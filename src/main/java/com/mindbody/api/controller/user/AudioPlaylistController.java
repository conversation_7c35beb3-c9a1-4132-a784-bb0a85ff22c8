package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.service.AudioPlaylistService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.AUDIO_PLAYLIST)
@Tag(name = "Audio-Playlist-User", description = "Audio Playlist User APIs")
public class AudioPlaylistController extends BaseController {

    private final AudioPlaylistService audioPlaylistService;

    public AudioPlaylistController(AudioPlaylistService audioPlaylistService) {
        this.audioPlaylistService = audioPlaylistService;
    }


    @GetMapping(Urls.LIST)
    @Operation(summary = "List Audio Playlist API based on Audio Playlist Type selected by User", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> listAudioPlaylistForAudioPlaylistType(@RequestParam String audioPlaylistType) {
        logger.info("List audio playlist API for selected audio playlist type is called for user!!");
        return okSuccessResponse(audioPlaylistService.listAudioPlaylistForSelectedAudioPlaylistTypeForUser(audioPlaylistType), "common_success");
    }
}
