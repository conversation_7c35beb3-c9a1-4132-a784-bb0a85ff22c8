package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.PlayRandomReqDTO;
import com.mindbody.api.dto.cms.WorkoutPlanListReqDTO;
import com.mindbody.api.service.FavoriteWorkoutPlanService;
import com.mindbody.api.service.RandomMediaTrackerService;
import com.mindbody.api.service.WorkoutPlanService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping(value = Urls.BASE_PATH +Urls.USER+ Urls.WORKOUT_PLAN)
@Tag(name = "Workout-Plan-User", description = "Workout plan User APIs")
public class WorkoutPlanController extends BaseController {

    private final WorkoutPlanService workoutPlanService;

    private final FavoriteWorkoutPlanService favoriteWorkoutPlanService;

    private final RandomMediaTrackerService randomMediaTrackerService;

    public WorkoutPlanController(WorkoutPlanService workoutPlanService, FavoriteWorkoutPlanService favoriteWorkoutPlanService, RandomMediaTrackerService randomMediaTrackerService) {
        this.workoutPlanService = workoutPlanService;
        this.favoriteWorkoutPlanService = favoriteWorkoutPlanService;
        this.randomMediaTrackerService = randomMediaTrackerService;
    }

    @PostMapping(value = Urls.LIST)
    @Operation(summary = "List workout plan video for user API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> listWorkoutPlanForUser(@RequestBody @Valid WorkoutPlanListReqDTO workoutPlanListReqDTO) {
        logger.info("List workout plan video for user API API is called!!");
        return okSuccessResponse(workoutPlanService.listWorkoutPlanList(workoutPlanListReqDTO), "common_success");
    }

    @GetMapping(value = Urls.VIEW)
    @Operation(summary = "View workout plan for user API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> viewWorkoutPlanForUser(@RequestParam Long workoutPlanId) {
        logger.info("View workout plan for user API is called!!");
        return okSuccessResponse(workoutPlanService.viewWorkoutPlan(workoutPlanId), "common_success");
    }


    @PostMapping(value = Urls.RANDOM)
    @Operation(summary = "Play random workout plan API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> playRandomWorkoutPlan(@RequestBody @Valid PlayRandomReqDTO playRandomReqDTO) {
        logger.info("Play random workout plan API is called!!");
        return okSuccessResponse(randomMediaTrackerService.playRandomWorkoutPlan(playRandomReqDTO), "common_success");
    }


}
