package com.mindbody.api.controller.user;


import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.shopify.getCategoryListReqDTO;
import com.mindbody.api.service.ShopifyCategoryService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.SHOPIFY + Urls.CATEGORY)
@Tag(name = "Shopify-User-Category", description = "Shopify user category APIs")
public class ShopifyCategoryController extends BaseController {

    private final ShopifyCategoryService shopifyCategoryService;

    public ShopifyCategoryController(ShopifyCategoryService shopifyCategoryService) {
        this.shopifyCategoryService = shopifyCategoryService;
    }

    @PostMapping(value = Urls.LIST)
    @Operation(summary = "Fetch category list API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getCategoryList(@RequestBody @Valid getCategoryListReqDTO getCategoryListReqDTO) {
        logger.info("Fetch category API is called!!");
        return okSuccessResponse(shopifyCategoryService.getCategoryList(getCategoryListReqDTO), "common_success");
    }

}
