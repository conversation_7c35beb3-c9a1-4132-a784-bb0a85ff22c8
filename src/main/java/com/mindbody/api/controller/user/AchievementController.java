package com.mindbody.api.controller.user;

import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.AchievementsBySetNameReq;
import com.mindbody.api.dto.SubmitCategoryTimeSpentReqDTO;
import com.mindbody.api.dto.achievement.AchievementCategoryListReqDTO;
import com.mindbody.api.dto.achievement.AchievementCountForCategoryResDTO;
import com.mindbody.api.service.AchievementsService;
import com.mindbody.api.service.UserPointsTrackerService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.ACHIEVEMENTS)
@Tag(name = "Achievements-User", description = "Achievements User APIs")
public class AchievementController extends BaseController {

    private final AchievementsService achievementsService;
    private final UserPointsTrackerService userPointsTrackerService;

    public AchievementController(AchievementsService achievementsService, UserPointsTrackerService userPointsTrackerService) {
        this.achievementsService = achievementsService;
        this.userPointsTrackerService = userPointsTrackerService;
    }


    @GetMapping(value = Urls.GET_ACHIEVEMENTS_COUNT_FOR_ALL_CATEGORIES)
    @Operation(summary = "Get Achievement Count For All categories Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getAchievementsCountForAllCategories(@RequestParam Long userId) {
        logger.info("Get Achievement Count For All Categories API");
        List<AchievementCountForCategoryResDTO> achievementCountForCategoryResDTOList=achievementsService.getAchievementsCountForAllCategories(userId);
        return okSuccessResponse(achievementCountForCategoryResDTOList, "common_success");
    }

    @PostMapping(value = Urls.GET_ACHIEVEMENT_SUB_CATEGORY_LIST_FOR_CATEGORY_SELECTED_BY_USER)
    @Operation(summary = "Get Achievements Sub Category List For Category Selected By User Api", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getAchievementSubcategoryListForCategorySelectedByUser(@RequestBody @Valid AchievementCategoryListReqDTO achievementCategoryListReqDTO) {
        logger.info("Achievements Sub Category List For Category Selected By User Api");
        return okSuccessResponse(achievementsService.getAchievementSubCategoryListForCategorySelectedByUser(achievementCategoryListReqDTO), "common_success");
    }


    @PostMapping(value = Urls.GET_ACHIEVEMENTS_BY_SET_NAME)
    @Operation(summary = "Get All Achievements By Set Name Api for User", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> getAllAchievementsBySetName(@RequestBody @Valid AchievementsBySetNameReq achievementsBySetNameReq) {
        logger.info("Get All Achievements By Set Name Api");
        return okSuccessResponse(achievementsService.getAllAchievementsBySetName(achievementsBySetNameReq), "common_success");

    }


}
