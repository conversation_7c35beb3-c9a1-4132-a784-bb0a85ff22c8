package com.mindbody.api.controller.user;


import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.CommonAudioListUserReqDTO;
import com.mindbody.api.dto.FavoriteAudioReqDTO;
import com.mindbody.api.dto.PlayRandomReqDTO;
import com.mindbody.api.service.AudioService;
import com.mindbody.api.service.FavoriteAudioService;
import com.mindbody.api.service.RandomMediaTrackerService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER + Urls.AUDIO)
@Tag(name = "Audio-User", description = "Audio User APIs")
public class AudioController extends BaseController {

    private final AudioService audioService;

    private final FavoriteAudioService favoriteAudioService;

    private final RandomMediaTrackerService randomMediaTrackerService;

    public AudioController(AudioService audioService, FavoriteAudioService favoriteAudioService, RandomMediaTrackerService randomMediaTrackerService) {
        this.audioService = audioService;
        this.favoriteAudioService = favoriteAudioService;
        this.randomMediaTrackerService = randomMediaTrackerService;
    }

    @PostMapping(value = Urls.LIST)
    @Operation(summary = "List audio user API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> listAudioUser(@RequestBody @Valid CommonAudioListUserReqDTO commonAudioListUserReqDTO) {
        logger.info("List audio user API is called!!");
        return okSuccessResponse(audioService.listAudioUser(commonAudioListUserReqDTO), "common_success");
    }

    @PostMapping(value = Urls.RANDOM)
    @Operation(summary = "Play random audio API", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> playRandomAudio(@RequestBody @Valid PlayRandomReqDTO playRandomReqDTO) {
        logger.info("Play random audio API is called!!");
        return okSuccessResponse(randomMediaTrackerService.playRandomAudio(playRandomReqDTO), "common_success");
    }


}
