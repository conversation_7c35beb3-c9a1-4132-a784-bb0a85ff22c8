package com.mindbody.api.controller.user;


import com.mindbody.api.controller.BaseController;
import com.mindbody.api.dto.*;
import com.mindbody.api.dto.cms.ForgotPasswordReqDTO;
import com.mindbody.api.dto.cms.ResetPasswordReqDTO;
import com.mindbody.api.service.OTPService;
import com.mindbody.api.service.UserService;
import com.mindbody.api.util.Constant;
import com.mindbody.api.util.Urls;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = Urls.BASE_PATH + Urls.USER_CURRENT_CONNECTION)
@Tag(name = "User-Current-Connection", description = "User current connection APIs")
public class UserCurrentConnectionController extends BaseController {

    private final UserService userService;

    public UserCurrentConnectionController(UserService userService) {
        this.userService = userService;
    }

    @GetMapping(Urls.LIST)
    @Operation(summary = "List user current connection", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> listCurrentConnection(@RequestParam long userId) {
        logger.info("List user current connection Api is called.");
        return okSuccessResponse(userService.listCurrentConnection(userId),"common_success");
    }

    @PostMapping(Urls.ADD)
    @Operation(summary = "Add user current connection", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> addCurrentConnection(@Valid @RequestBody AddCurrentConnectionReqDTO addCurrentConnectionReqDTO){
        logger.info("Add user current connection Api is called.");
        userService.addCurrentConnection(addCurrentConnectionReqDTO);
        return okSuccessResponse("current_connection_add_success");
    }

    @DeleteMapping(Urls.DELETE)
    @Operation(summary = "Delete user current connection", security = @SecurityRequirement(name = Constant.ACCESS_TOKEN))
    @PreAuthorize("hasAnyAuthority('USER')")
    public ResponseEntity<?> removeCurrentConnection(@Valid @RequestBody RemoveCurrentConnectionReqDTO removeCurrentConnectionReqDTO){
        logger.info("Delete user current connection Api is called.");
        userService.removeCurrentConnection(removeCurrentConnectionReqDTO);
        return okSuccessResponse("current_connection_remove_success");
    }

}
