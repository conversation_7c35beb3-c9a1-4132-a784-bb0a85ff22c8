package com.mindbody.api;

import com.mindbody.api.model.EntityAdmin;
import com.mindbody.api.repository.AdminRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.Objects;


@Component
public class InitializeEntity implements CommandLineRunner {
    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        registerAdmin();
    }

    private void registerAdmin() {
        EntityAdmin entityAdmin = adminRepository.findAdmin(true,false);
        if (Objects.isNull(entityAdmin)) {
            EntityAdmin adminDetail = new EntityAdmin();
            adminDetail.setFirstName("John");
            adminDetail.setLastName("Doe");
            adminDetail.setEmail("<EMAIL>");
            adminDetail.setPassword(passwordEncoder.encode("Admin@1234"));
            adminRepository.save(adminDetail);
        }

    }
}
