## Mind-body-warrior

## Description

The end user will be able to create their profile on our platform. Platform offers a unique and
personalized approach to self-improvement. Combining astrology insights with holistic living practices, the app
aims to enhance mental, physical, and spiritual well-being for its users. Through features like personalized
astrology sign birth charts, daily guidance based on Sun Signs, meditation and sleep sound content, and a
curated shop with astrology-based product recommendations, users can embark on a journey towards a more
balanced and fulfilling lifestyle. With a focus on customization options, notifications for daily reminders, and
future plans for social features and nutrition information, this application provides a comprehensive
experience for individuals seeking to improve their overall wellness.

## Getting Started

To run this project in your local you have to clone this project from git. And for clone this project you can use git clone command.

### Prerequisites

1. Any IDE which supports JAVA and Spring Boot framework
2. MySql

### Installing

2. Installing Intellij IDE
   https://www.jetbrains.com/help/idea/installation-guide.html

### Built With

* [Spring-Boot](https://start.spring.io/) - The framework used
* [Gradle](https://gradle.org/) - Dependency Management

## Environment Variables

| VARIABLE NAME | DEFAULT VALUE | DESCRIPTION    |
|---------------|---------------|----------------|
| development   | development   | Staging Env    |
| master        | master        | Production Env |
| localhost     | localhost     | localhost Env  |

## Running the tests

From IDE - Intellij: Doing a Right-Click on the feature files gives you different options to run the entire frature or a specific scenario.

### And coding style tests

1. Readability counts.
2. Be consistent.
3. Don't repeat yourself.
4. Flat is better than nested.

## Deployment
The deployment of this project is done with CI/CD after the subsequent changes are merged into the respective branches.

## Authors

* **Ronak Gurjar** - *Initial work* 
